<template>
  <div class="readingActivityNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item class="form-title"
                    label="主题"
                    prop="title">
        <el-input placeholder="请输入主题"
                  v-model="form.title"
                  clearable
                  prop="meetName">
        </el-input>
      </el-form-item>
      <el-form-item label="发布部门"
                    class="form-input">
        <zy-select width="296"
                   node-key="id"
                   v-model="form.officeId"
                   :data="institutions"
                   placeholder="请选择发布部门"></zy-select>
      </el-form-item>
      <el-form-item label="活动类型"
                    class="form-input">
        <el-select v-model="form.schemeType"
                   filterable
                   clearable
                   placeholder="请选择活动类型">
          <el-option v-for="item in schemeType"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否公开"
                    prop="isOpen"
                    class="form-input">
        <el-radio-group v-model="form.isOpen">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="form-title"
                    label="地点">
        <el-input placeholder="请输入地点"
                  v-model="form.handlAddress"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上传封面图"
                    class="form-img">
        <el-upload class="form-img-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="fileImg.filePath"
               :src="fileImg.filePath"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="报名时间"
                    prop="applyTime"
                    class="form-title">
        <el-date-picker v-model="form.applyTime"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="报名开始日期"
                        end-placeholder="报名结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="签到时间"
                    prop="signTime"
                    class="form-title">
        <el-date-picker v-model="form.signTime"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="签到开始日期"
                        end-placeholder="签到结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="活动时间"
                    prop="activityTime"
                    class="form-title">
        <el-date-picker v-model="form.activityTime"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="活动开始日期"
                        end-placeholder="活动结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="邀请人"
                    class="form-title">
        <candidates-box point="point_20"
                        placeholder="请选择邀请人"
                        :data.sync="userData"></candidates-box>
      </el-form-item>
      <el-form-item label="内容"
                    prop="content"
                    class="form-ue">
        <UEditor v-model="form.content"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'readingActivityNew',
  data () {
    return {
      form: {
        title: '',
        handlAddress: '',
        officeId: '',
        schemeType: '',
        applyTime: '',
        isOpen: 1,
        signTime: '',
        activityTime: '',
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入主题', trigger: 'blur' }
        ],
        officeId: [
          { required: true, message: '请选择组织部门', trigger: 'blur' }
        ],
        // content: [
        //   { required: true, message: '请输入内容', trigger: 'blur' }
        // ],
        applyTime: [
          {
            type: 'array',
            required: true,
            message: '请选择日期区间',
            fields: {
              0: { type: 'number', required: true, message: '请选择报名开始日期' },
              1: { type: 'number', required: true, message: '请选择报名结束日期' }
            }
          }
        ],
        signTime: [
          {
            type: 'array',
            required: true,
            message: '请选择日期区间',
            fields: {
              0: { type: 'number', required: true, message: '请选择签到开始日期' },
              1: { type: 'number', required: true, message: '请选择签到结束日期' }
            }
          }
        ],
        activityTime: [
          {
            type: 'array',
            required: true,
            message: '请选择日期区间',
            fields: {
              0: { type: 'number', required: true, message: '请选择活动开始日期' },
              1: { type: 'number', required: true, message: '请选择活动结束日期' }
            }
          }
        ]
      },
      fileImg: {},
      institutions: [],
      schemeType: [],
      userData: []
    }
  },
  props: ['id'],
  mounted () {
    this.dictionaryPubkvs()
    this.treeList()
    if (this.id) {
      this.readschemeInfo()
    }
  },
  methods: {
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'library_scheme_type'
      })
      var { data } = res
      this.schemeType = data.library_scheme_type
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.fileImg = data[0]
      }).catch(() => {
        files.onError()
      })
    },
    /**
     *机构树
    */
    async treeList () {
      const res = await this.$api.systemSettings.treeList({})
      var { data } = res
      this.institutions = data
    },
    async readschemeInfo () {
      const res = await this.$api.academy.readschemeInfo(this.id)
      var { data } = res
      this.form.title = data.title
      this.form.officeId = data.officeId
      this.form.isOpen = data.isOpen
      this.form.schemeType = data.schemeType
      this.form.handlAddress = data.handlAddress
      this.userData = data.users ? data.users : []
      this.$set(this.form, 'applyTime', [data.applyStartTime, data.applyEndTime]) // 报名时间
      this.$set(this.form, 'signTime', [data.signStartTime, data.signEndTime]) // 签到时间
      this.$set(this.form, 'activityTime', [data.startTime, data.endTime]) // 活动时间
      this.form.content = data.content
      if (data.coverImg) {
        this.fileImg = { id: data.coverImg, filePath: data.coverImgUrl }
      }
    },
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var userData = []
          this.userData.forEach(item => {
            userData.push(item.userId)
          })
          this.generalAdd({
            // empty: '1', // 置空过滤标识
            id: this.id,
            title: this.form.title,
            officeId: this.form.officeId,
            isOpen: this.form.isOpen,
            schemeType: this.form.schemeType,
            handlAddress: this.form.handlAddress,
            applyStartTime: this.form.applyTime[0], // 报名开始时间
            applyEndTime: this.form.applyTime[1], // 报名截止时间
            signStartTime: this.form.signTime[0], // 开始签到时间
            signEndTime: this.form.signTime[1], // 签到截至时间
            startTime: this.form.activityTime[0], // 活动开始时间
            endTime: this.form.activityTime[1], // 活动结束时间
            userIds: userData.join(','),
            content: this.form.content,
            coverImg: this.fileImg.id
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    async generalAdd (data) {
      var url = '/readscheme/add?'
      if (this.id) {
        url = '/readscheme/edit?'
      }
      const res = await this.$api.general.generalAdd(url, data)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        this.$emit('callback')
      }
    },
    /**
 * 取消按钮
*/
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.readingActivityNew {
  width: 988px;
  .form-img {
    width: 100%;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
