.zy-tree-components {
  background-color: #fff;
  width: 268px;
  // min-width: 268px;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;

  .zy-tree-components-item {
    display: flex;
    align-items: center;
    cursor: pointer;

    .zy-tree-components-item-icon {
      height: 24px;
      width: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: rotate(0deg);
      transition-duration: 0.4s;

      .el-icon-caret-right {
        color: #c0c4cc;
      }
    }

    .zy-tree-components-item-text {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &:hover {
      background-color: $zy-withColor;
    }
  }

  .zy-tree-components-item-active {
    .zy-tree-components-item-icon {
      transform: rotate(90deg);
      transition-duration: 0.4s;
    }
  }

  .zy-tree-components-item-selected {
    background-color: $zy-withColor;

    .zy-tree-components-item-text {
      color: $zy-color;
      font-weight: 600;
    }
  }
}
