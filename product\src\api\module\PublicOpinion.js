// 导入封装的方法
import {
  get,
  post,
  filedownload
} from '../http'

const PublicOpinion = {
  generalAdd (url, params) {
    return post(url, params)
  },
  checkTitleSimilarity (params) { // 标题查重
    return post('/socialinfo/checkTitleSimilarity', params)
  },
  socialinfoList (params) { // 社情民意列表
    return post('/socialinfo/list', params)
  },
  socialinfodels (params) { // 社情民意列表删除
    return post('socialinfo/dels', params)
  },
  socialinfolook (params) { // 社情民意详情【仅显示】
    return post('/socialinfo/look/' + params)
  },
  socialinfolookAudit (params) { // 社情民意审核详情【仅显示】
    return post('/socialinfo/look2', params)
  },
  opinionoriginalInfo (params) { // 原稿详情
    return post(`/opinionoriginal/info/${params}`)
  },
  processimg (params) { // 查看办理进度图
    return filedownload('/socialinfo/processimg', params, 'arraybuffer')
  },
  socialinfoinfo (params) { // 社情民意详情【仅显示】
    return post('/socialinfo/info/' + params)
  },
  complatetask (params) { // 办理当前流程
    return post('/socialinfo/complatetask', params)
  },
  opinionexamineList (params) { // 查询采用批示列表
    return post('/opinionexamine/list', params)
  },
  opinionexamineDel (params) { // 采用批示列表删除
    return post('/opinionexamine/dels', params)
  },
  opinionexamineInfo (params) { // 采用批示列表删除
    return post('/opinionexamine/info/' + params)
  },
  dictionarylist (params) { // 字典
    return post('/dictionary/list', params)
  },
  opinionnumberInfo (params) { // 编号详情
    return post(`/opinionnumber/info/${params}`)
  },
  socialinfoCount (params) { // 来稿统计
    return post('/socialinfo/count', params)
  },
  getCirclesCount (params) { // circlesType（界别） specialCommitteeType（专委会）partyType（党派）
    return post('/socialInfoCount/getCirclesCount', params)
  },
  socialintegralstatisticsQueryList (params) { // 统计列表
    return get('/socialintegralstatistics/queryList', params)
  },
  socialintegralstatisticsAddList (data) { // 保存统计列表
    return post('/socialintegralstatistics/addList', data, {
      'Content-Type': 'application/json;charset=UTF-8'
    })
  },
  // 下载exl
  socialintegralstatisticsList (year) {
    return get(`/socialintegralstatistics/list?years=${year}`)
  },
  findOpinionList (params) { // 社情民意初审列表
    return get('/socialopinionpreliminaryexamination/findOpinionList', params)
  },
  socialopinionpreliminaryexaminationlist (params) { // 社情民意初审记录列表
    return get('/socialopinionpreliminaryexamination/list', params)
  },
  socialopinionpreliminaryexaminationDels (params) { // 社情民意初审记录删除
    return post('/socialopinionpreliminaryexamination/dels', params)
  },
  socialopinionreviewfindOpinionList (params) { // 社情民意复审列表
    return get('/socialopinionreview/findOpinionList', params)
  },
  socialopinionreviewlist (params) { // 社情民意复审记录列表
    return get('/socialopinionreview/list', params)
  },
  audit (params) { // 初审
    return post('/socialopinionpreliminaryexamination/audit', params)
  },
  review (params) { // 复审
    return post('/socialopinionreview/review', params)
  },
  editOpinion (params) { // 退回编辑
    return post('/socialopinionpreliminaryexamination/editOpinion', params)
  },
  // 委员之声
  socialvoiceofmembersList (params) { // 列表
    return post('/socialvoiceofmembers/list', params)
  },
  socialvoiceofmembersInfo (params) { // 详情
    return post('/socialvoiceofmembers/info/' + params)
  },
  socialvoiceofmembersDels (params) { // 删除
    return post('/socialvoiceofmembers/dels', params)
  }
}
export default PublicOpinion
