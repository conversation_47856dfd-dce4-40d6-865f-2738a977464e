<template>
  <div class="committee-data-custom-topic">
    <generalCustomTopic :module="module"
                        :columnModule="columnModule"></generalCustomTopic>
  </div>
</template>
<script>
import generalCustomTopic from '@/views/app-management/generalCustomTopic/generalCustomTopic'
export default {
  name: 'committeeDataCustomTopic',
  data () {
    return {
      module: 2,
      columnModule: 6
    }
  },

  components: {
    generalCustomTopic
  },
  mounted () {
    var module = this.$route.query.module || ''
    if (module) {
      if (module.indexOf('-') >= 0) {
        this.module = module.split('-')[0] || ''
        this.columnModule = module.split('-')[1] || ''
      } else {
        this.module = module
        this.columnModule = module
      }
    }
  }

}
</script>
<style lang="scss">
.committee-data-custom-topic {
  height: 100%;
  width: 100%;
}
</style>
