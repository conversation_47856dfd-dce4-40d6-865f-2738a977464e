<template>
  <div class="add-join-user">
    <div class="add-user-form-label">邀请人</div>
    <el-button size="small"
               plain
               @click="isShow = true"></el-button>
    <div class="add-user-form-content">
      <el-tag style="margin-right: 10px; margin-bottom: 7px"
              class="tag"
              v-for="(item, index) in userData"
              :key="index"
              @close="deleteMan(index)"
              closable>
        {{ item.name }}
      </el-tag>
    </div>
    <zy-pop-up v-model="isShow"
               title="添加参与人员">
      <candidates-user point="point_15"
                       :data="userData"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  data () {
    return {
      isShow: false,
      isVisible: false,
      userData: []
    }
  },
  methods: {
    userCallback (data, type) {
      if (type) {
        this.userData = data
      }
      this.isShow = false
    },
    deleteMan (index) {
      this.userData.splice(index, 1)
    }
  }
}
</script>

<style lang="scss">
.add-join-user {
  width: 100%;
  padding: 0 40px;
  .add-user-form-label {
    font-size: 18px;
    line-height: 52px;
  }
  .add-user-form-content {
    padding: 24px 0;
  }
}
</style>
