
<template>
  <div class="zy-table"
       ref="zy-table">
    <el-scrollbar class="my-scroll-bar"
                  :style="{width:width+'px',height:height+'px',}">
      <slot name="zytable"></slot>
    </el-scrollbar>
  </div>
</template>
<script>
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
export default {
  name: 'zyTable',
  data () {
    return {
      width: 0,
      height: 0,
      top: 0,
      left: 0
    }
  },
  // created () {
  //   this.scrollshow()
  // },
  activated () {
    this.scrollshow()
    this.handleScroll()
  },
  mounted () {
    this.$nextTick(() => {
      if (this.$refs['zy-table']) {
        this.$refs['zy-table'].querySelector('.el-scrollbar__wrap').addEventListener('scroll', this.handleScroll)
      }
      this.handleScroll()
      this.scrollshow()
      this.sliding()
      if (this.$refs['zy-table']) {
        const that = this
        erd.listenTo(this.$refs['zy-table'], (element) => {
          that.$nextTick(() => {
            that.width = element.offsetWidth
            that.height = element.offsetHeight
            this.scrollshow()
            this.handleScroll()
          })
        })
      }
    })
    // this.loading()
  },
  methods: {
    loading () {
      this.$nextTick(() => {
        var box = this.$refs['zy-table']
        var loading = box.querySelector('.el-loading-mask')
        if (loading) {
          loading.style.height = `${box.clientHeight}px`
        }
      })
    },
    handleScroll (event) {
      if (this.$refs['zy-table']) {
        var box = this.$refs['zy-table'].querySelector('.el-table__header-wrapper')
        this.top = this.$refs['zy-table'].getBoundingClientRect().top
        this.left = this.$refs['zy-table'].getBoundingClientRect().left
        var boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().top
        var boxss = this.top - 1 - boxTop
        box.style.top = boxss + 'px'
        var fixed = this.$refs['zy-table'].querySelector('.el-table__fixed')
        if (fixed) {
          const fixedHeader = fixed.querySelector('.el-table__fixed-header-wrapper')
          const boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().left
          const distance = this.left - 1 - boxTop
          fixed.style.left = distance + 'px'
          fixedHeader.style.top = boxss + 'px'
        }
        var fixedright = this.$refs['zy-table'].querySelector('.el-table__fixed-right')
        if (fixedright) {
          const fixedHeader = fixedright.querySelector('.el-table__fixed-header-wrapper')
          const aa = this.$refs['zy-table'].offsetWidth
          const boxTop = this.$refs['zy-table'].querySelector('.el-table').getBoundingClientRect().left
          const distance = this.left - 1 - boxTop
          fixedright.style.left = (distance + aa - fixedright.offsetWidth) + 'px'
          fixedHeader.style.top = boxss + 'px'
        }
      }
    },
    sliding () {
      if (this.$refs['zy-table']) {
        var fixedright = this.$refs['zy-table'].querySelector('.el-table__fixed-right')
        if (fixedright) {
          const aa = this.$refs['zy-table'].offsetWidth
          fixedright.style.left = (aa - fixedright.offsetWidth) + 'px'
        }
      }
    },
    scrollshow () {
      this.$nextTick(() => {
        if (this.$refs['zy-table']) {
          var arrayWidth = this.$refs['zy-table'].clientWidth
          var arrWidth = this.$refs['zy-table'].querySelector('.el-table__body-wrapper').querySelector('tbody').clientWidth
          var horizontal = this.$refs['zy-table'].querySelector('.is-horizontal')
          if (arrayWidth < arrWidth) {
            horizontal.style.backgroundColor = '#EEF1F4'
          } else {
            horizontal.style.backgroundColor = 'transparent'
          }
        }
      })
    }
  },
  beforeDestroy () {
    erd.uninstall(this.$refs['zy-table'])
  }
}
</script>
<style lang="scss">
@import "./zy-table.scss";
</style>
