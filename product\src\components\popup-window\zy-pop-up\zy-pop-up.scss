.zy-pop-up {
  .zy-pop-up-cover {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .zy-pop-up-content {
    position: fixed;
    max-height: 80%;
    background-color: #fff;
    z-index: 1000;
    .zy-pop-up-head {
      width: 100%;
      height: 52px;
      background: $zy-color;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      cursor: move;
      .zy-pop-up-title {
        color: #fff;
      }
      .el-icon-close {
        color: #fff;
        cursor: pointer;
        font-size: 18px;
      }
    }
    .zy-pop-up-body {
      width: 100%;
      height: calc(100% - 52px);
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
      .is-vertical {
        .el-scrollbar__thumb {
          background-color: rgba(144, 147, 153, 0.8);
        }
      }
    }
  }
}
