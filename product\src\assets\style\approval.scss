.table-approval {
  width: 100%;
  height: 100%;
  padding: 0 25px;
  box-sizing: border-box;

  .table-box {
    position: relative;

    .Printing {
      position: absolute;
      right: 45px;
      top: 20px;
    }

    .table-title {
      line-height: 100px;
      height: 100px;
      font-size: 32px;
      color: #ec0e22;
      text-align: center;
      font-family: 'SimSun';
      font-weight: 700;
    }

    .table-column {
      border: 2px solid #ec0e22;

      .table-row {
        display: flex;
        position: relative;

        .table-row-item {
          display: flex;
          position: relative;
          flex: auto;

          .table-row-item-lable {
            width: 120px;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            min-height: 60px;
            font-size: 18px;
            color: red;
            padding: 10px;
            flex-shrink: 0;
            font-family: 'SimHei';
            font-weight: 600;
          }

          .table-row-item-content {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
            font-size: 14px;
            min-height: 60px;
            padding: 0 8px;
            font-family: 'FangSong';

            i {
              font-style: normal;
            }
          }

          // 文件不居中
          .table-file {
            justify-content: flex-start !important;

            >i {
              margin: 0 8px;
              font-size: 24px;
              color: #ec0e22;
            }

            .file-list {
              >p {
                >i {
                  display: none;
                  cursor: pointer;
                }
              }

              >p:hover {
                >i {
                  display: inline-block;
                }
              }
            }
          }

          // 添加审核人
          .node-check-user {
            justify-content: flex-start;
          }

          .text-left {
            justify-content: flex-start;

            >span {
              margin-right: 15px;
            }
          }

          .red-head {
            color: #ec0e22;
            font-size: 18px;
            font-family: 'SimHei';
            font-weight: 600;

            >span {
              color: #444;
              font-family: 'FangSong';
              font-size: 14px;
            }
          }
        }
      }

      .table-row-column {
        min-height: 100%;
        flex: auto;
      }

      .border-width1-top {
        border-top: 1px solid #ec0e22;
      }

      .border-width1-left {
        border-left: 1px solid #ec0e22;
      }

      .border-width1-right {
        border-right: 1px solid #ec0e22;
      }

      .border-width1-bottom {
        border-bottom: 1px solid #ec0e22;
      }

      .border-width2-top {
        border-top: 2px solid #ec0e22;
      }

      .border-width2-right {
        border-right: 2px solid #ec0e22;
      }

      .border-width2-left {
        border-left: 2px solid #ec0e22;
      }

      .border-width2-bottom {
        border-bottom: 2px solid #ec0e22;
      }
    }

    .table-select {
      .el-input--suffix {
        .el-input__inner {
          border: none;
        }
      }
    }

    .table-input {
      align-self: center;

      >.el-input__inner {
        border: none !important;
      }
    }

    .table-date {
      border: none !important;
    }

    .table-date-time {
      >.el-input__inner {
        border: none;
      }
    }

    //
    .bankInfo {
      p {
        line-height: 28px;
        font-family: 'FangSong';
        font-size: 14px;
        padding-left: 12px;
      }
    }

    // 置灰
    .table-disabled {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: #595959;
      opacity: 0.10;
      z-index: 1000;
    }

    // 按钮
    .table-icon-btn {
      background-color: #d6d6d6;
      border-color: #eee;
      color: #ec0e22;
      align-self: center;
      margin-left: 4px;
      padding: 4px;
      font-size: 12px;
    }

    .table-icon-btn:hover {
      background-color: #EfEfEf;
      border-color: #eee;
    }

    .table-icon-btn:focus {
      background-color: #EfEfEf;
      border-color: #eee;
    }

    .table-icon-btn:active {
      background-color: #EfEfEf;
      border-color: #eee;
    }

    .table-span {
      align-self: center;
      margin: 0 8px;
    }

    .table-tips {
      font-size: 12pt;
      color: #ec0e22;
    }

    .costList {
      width: 100%;

      .costList-item {
        display: flex;
        font-family: "FangSong";
        height: 36px;
        border-bottom: 1px solid #ec0e22;
        box-sizing: border-box;
        line-height: 36px;

        .name {
          width: 120px;
          text-align: center;
        }

        .value {
          flex: auto;
          padding-left: 20px;
        }
      }

      .costList-item:last-child {
        border-bottom: none;
      }
    }

    .verify {
      flex-direction: column;
      padding: 10px;
      width: 100%;

      .table-verify-select {
        width: 100%;
      }

      .table-verify-content {
        padding: 10px 0;
        width: 100%;
      }

      .table-verify-btn {
        width: 100%;
        padding-right: 30px;
        box-sizing: border-box;
        display: flex;
        justify-content: flex-end;

        .signature-img {
          height: 36px;
          margin-left: 24px;
        }
      }
    }

    // 回显操作
    .signature {
      display: flex;
      align-items: center;
      font-size: 14px;
      position: relative;

      .sign {
        width: 169px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        font-size: 14px;
        font-family: 'FangSong';

        >img {
          max-width: 100%;
          max-height: 48px;
        }
      }

      .opition {
        flex: auto;
        padding: 8px 10px;

        >p {
          font-family: 'FangSong';
        }
      }
    }

    .signature-record {
      height: 60px;
    }
  }

  .table-th-lable {
    color: red;
    font-family: 'SimHei';
    font-weight: 600;
  }

  .btn-box {
    height: 54px;
    line-height: 54px;
    text-align: center;
    width: 1000px;
  }

  .flex1 {
    .table-row-item {
      flex: 1 !important;
    }
  }

  .files {
    flex-direction: column;
    align-items: flex-start !important;
  }

  .node-label {
    padding: 0 15px !important;
  }

  .downloadfile {
    cursor: pointer;
    font-family: 'FangSong';
  }

  .downloadfile:hover {
    color: #199bc5;
  }

  .max-ht {
    height: 100% !important;
  }
}

.apply-car-table {

  // 随行乘客
  .passenger {
    display: flex;

    .passenger-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 45px;
      line-height: 45px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  // 调配安排
  .deploy-car {
    display: flex;

    .deploy-car-item {
      display: flex;
      font-size: 10px;
      justify-content: center;
      align-items: center;
      height: 45px;
      line-height: 45px;
      flex-shrink: 0;
    }
  }

  .deploy-car-th {
    .deploy-car-item {
      color: red;
    }
  }

  .get-out {
    display: flex;

    .get-out-item {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 45px;
      padding: 0 4px;
      line-height: 45px;
      flex-shrink: 0;

      >p {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .car-info {
    width: 100px;
  }

  .car-num {
    width: 100px;
  }

  .car-time {
    width: 140px;
  }

  .car-mile {
    width: 125px;
  }

  .car-opreate {
    width: 108px;
  }

  // 出车
  .get-out-th {
    >.get-out-item {
      font-family: 'SimHei';
      color: red;
    }
  }

  .get-out-txt {
    >.get-out-item {
      font-family: 'FangSong';
      font-size: 14px;
    }
  }

  .fs {
    font-family: 'FangSong';
    font-size: 14px;
  }

  .car-img-box {
    width: 400px;
    padding: 15px;
    box-sizing: border-box;

    .car-btn-box {
      display: flex;
      height: 54px;
      align-items: center;
    }
  }
}

.reception-table {
  .letter-left {
    width: calc(100%/3);
  }

  .letter-right {
    width: calc(100%/3*2);
  }

  .guest {
    display: flex;

    .guest-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 45px;
      line-height: 45px;
      font-family: 'FangSong';
    }
  }

  .guest-remark {
    height: 45px;
    line-height: 45px;
    padding: 0 10px;
    font-family: 'FangSong';
  }

  .reception-info-item {
    display: flex;
    height: 45px;
    line-height: 45px;

    .reception-info-item-label {
      width: 169px;
      display: flex;
      flex-shrink: 0;
      justify-content: center;
      align-items: center;
      font-family: 'FangSong';
    }

    .reception-info-item-content {
      flex: auto;
      align-items: center;
      font-family: 'FangSong';
      padding: 0 10px;
    }
  }
}

.apply-goods-table {
  .goods-item {
    font-size: 14px;
    padding: 0 8px;
    font-family: 'FangSong';
    line-height: 30px;
  }

  .Purchase-list {
    display: flex;

    .Purchase-list-item {
      flex: 1;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-family: 'FangSong';
    }
  }
}



.sp-bottom:last-child {
  border-bottom: none !important;
}