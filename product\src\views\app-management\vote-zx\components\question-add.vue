<template>
  <div class="question-add">
    <div class="add-box scrollBar">
      <el-form :model="addform" ref="addform" :rules="addRule" :label-position="'top'">
        <el-form-item label="题目" prop="name">
          <el-input v-model="addform.name"></el-input>
        </el-form-item>
        <el-form-item label="题型" prop="topic">
          <el-select v-model="addform.topic" placeholder="请选择">
            <el-option v-for="item in topicList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选项" v-if="addform.topic === 'single' || addform.topic === 'multi'">
          <el-button type="primary" @click="addOption" size="small">添加选项</el-button>
          <div class="option-item" v-for="(item,index) in option" :key="index">
            <el-input v-model="item.text" placeholder="请输入选项" class="input" size="small"></el-input>
            <div class="close" @click="delOption(index)">
              <i class="el-icon-close"></i>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit('addform')">提交</el-button>
          <el-button @click="cancel('addform')">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    editData: Object,
    paperId: String
  },
  data () {
    return {
      isAdd: true,
      addform: {
        name: '',
        topic: ''
      },
      addRule: {
        name: [{ required: true, message: '请输入题目', trigger: 'blur' }],
        topic: [{ required: true, message: '请选择题型', trigger: 'blur' }]
      },
      option: [],
      topicList: [
        { value: 'single', label: '单选' },
        { value: 'multi', label: '多选' },
        { value: 'text', label: '简答' }
      ]
    }
  },
  mounted () {
    if (this.editData) {
      this.addform.name = this.editData.name
      this.addform.topic = this.editData.topic
      if (this.editData.options) {
        this.option = this.editData.options.split('|').map(item => {
          return { text: item }
        })
      }
    }
  },
  methods: {
    // 添加选项
    addOption () {
      this.option.push({ text: '' })
    },
    // 删除选项
    delOption (index) {
      this.option.splice(index, 1)
    },
    // 提交
    onSubmit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const data = this.addform
          data.module = 'questionnaire'
          if (this.paperId) { // 投票新增题目
            data.paperId = this.paperId
            data.isPublic = 1
          } else { // 题库新增题目
            data.isPublic = 0
          }
          if (data.topic === 'single' || data.topic === 'multi') {
            data.options = this.option.map(item => item.text).join('|')
          }
          if (this.editData) {
            data.id = this.editData.id
            this.$api.appManagement.questionEdit(data).then(res => {
              if (res.errcode) {
                this.$message.success('修改成功')
                this.$emit('cancel', true)
              }
            })
          } else {
            this.$api.appManagement.questionAdd(data).then(res => {
              if (res.errcode) {
                this.$message.success('新增成功')
                this.$emit('cancel', true)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 取消
    cancel (formName) {
      this.$refs[formName].resetFields()
      this.option = [{ text: '' }]
      this.$emit('cancel')
    }
  },
  watch: {
    isAdd () {
      if (!this.isAdd) {
        this.$emit('cancel')
      }
    }
  }
}
</script>

<style lang="scss">
.question-add {
  .add-box {
    width: 600px;
    padding: 10px 20px;
    max-height: 500px;
    .option-item {
      display: flex;
      .input {
        width: calc(100% - 42px);
      }
      .close {
        display: none;
        width: 32px;
        height: 32px;
        font-size: 18px;
        color: red;
        text-align: center;
        line-height: 32px;
      }
    }
    .options-item {
      line-height: 36px;
    }
    .option-item:hover {
      .close {
        display: block;
      }
    }
  }
}
</style>
