<template>
  <div class="voteZXList">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input v-model="keyword"
                  placeholder="请输入关键词"
                  @keydown.native.enter="search"
                  clearable></el-input>
        <el-date-picker v-model="time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   @click="deleteClick">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="投票主题"
                           prop="theme"
                           min-width="180"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="投票时间"
                           min-width="360">
            <template slot-scope="scope">{{scope.row.startTime}}至{{scope.row.endTime}}</template>
          </el-table-column>
          <el-table-column label="发起人"
                           prop="createBy"
                           min-width="120"></el-table-column>
          <el-table-column label="发布状态"
                           prop="isPublishValue"
                           min-width="120"></el-table-column>
          <el-table-column label="投票状态"
                           prop="voteStatusValue"
                           min-width="120"></el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           min-width="300">
            <template slot-scope="scope">
              <el-button @click="handleEnable(scope.row.id,1)"
                         v-if="scope.row.isPublish === 0"
                         type="text">发布</el-button>
              <el-button @click="handleEnable(scope.row.id,0)"
                         v-else
                         type="text">取消发布</el-button>
              <el-button @click="handleEdit(scope.row)"
                         type="text">编辑</el-button>
              <el-button @click="handleVoteItem(scope.row.id)"
                         type="text">投票项</el-button>
              <el-button @click="handlePreview(scope.row.id)"
                         type="text">预览</el-button>
              <el-button @click="handleDelete(scope.row.id)"
                         type="text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <!-- <zy-pop-up v-model="show"
               :title="id?'编辑':'新增'">
      <appManagementNew :id="id"
                            @newCallback="callback"></appManagementNew>
    </zy-pop-up>
    <zy-pop-up v-model="detailsShow"
               title="详情">
      <appManagementDetails :id="id"></appManagementDetails>
    </zy-pop-up> -->
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
// const appManagementNew = () => import('./appManagementNew')
// const appManagementDetails = () => import('./appManagementDetails')
export default {
  name: 'voteZXList',
  data () {
    return {
      keyword: '',
      time: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false,
      detailsShow: false
    }
  },
  mixins: [tableData],
  components: {
    // appManagementNew,
    // appManagementDetails
  },
  mounted () {
    this.voteZXList()
  },
  methods: {
    search () {
      this.page = 1
      this.voteZXList()
    },
    reset () {
      this.keyword = ''
      this.time = []
      this.voteZXList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    edit (row) {
      this.id = row.id
      this.show = true
    },
    callback () {
      this.show = false
      this.voteZXList()
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    async voteZXList () {
      const res = await this.$api.appManagement.voteZXList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        startTime: this.time ? this.time[0] : '',
        endTime: this.time ? this.time[1] : ''
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.voteZXList()
    },
    whatPage (val) {
      this.voteZXList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的值班, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.voteZXDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async voteZXDel (id) {
      const res = await this.$api.appManagement.voteZXDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.voteZXList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.voteZXList {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
