<template>
  <div class="generalBehalfStatistical">
    <histogram title="代表团组分析"
               :data="representerTeam"
               :type="'representerTeam'"
               v-if="representerTeam.length"
               :path="memberType == 3 ? 'behalfStatisticalList' : 'nationalBehalfStatisticalList'"></histogram>
    <histogram title="代表结构分析"
               :data="party"
               :type="'representerElement'"
               v-if="party.length"
               :path="memberType == 3 ? 'behalfStatisticalList' : 'nationalBehalfStatisticalList'"></histogram>
    <histogram title="民族"
               :data="nation"
               :type="'nation'"
               v-if="nation.length"
               :path="memberType == 3 ? 'behalfStatisticalList' : 'nationalBehalfStatisticalList'"></histogram>
    <ring-view title="性别"
               :data="sex"
               :type="'sex'"
               v-if="sex.length"
               :path="memberType == 3 ? 'behalfStatisticalList' : 'nationalBehalfStatisticalList'"></ring-view>
    <ring-view title="年龄"
               :data="birthday"
               :type="'birthday'"
               v-if="birthday.length"
               :path="memberType == 3 ? 'behalfStatisticalList' : 'nationalBehalfStatisticalList'"></ring-view>
  </div>
</template>
<script>
import histogram from '../../memberInformation/histogram/histogram'
import ringView from '../../memberInformation/ring-view/ring-view'
export default {
  name: 'generalBehalfStatistical',
  data () {
    return {
      birthday: [],
      nation: [],
      party: [],
      representerTeam: [],
      sex: []
    }
  },
  props: ['memberType'],
  components: {
    histogram,
    ringView
  },
  mounted () {
    this.memberCount()
  },
  methods: {
    async memberCount () {
      const res = await this.$api.memberInformation.memberCount({
        memberType: this.memberType
      })
      var { data } = res
      this.birthday = data.birthday
      this.nation = data.nation
      this.party = data.representerElement
      this.representerTeam = data.representerTeam
      this.sex = data.sex
    }
  }
}
</script>
<style lang="scss">
.generalBehalfStatistical {
  width: 100%;
  height: 100%;
}
</style>
