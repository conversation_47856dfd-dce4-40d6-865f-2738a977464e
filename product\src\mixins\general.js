export default {
  data () {
    return {
      name: this.$route.query.name,
      menuId: '',
      menu: [],
      childTo: '',
      jumpTo: '',
      jumpData: {},
      menuActive: '',
      menuChild: [],
      tabId: '',
      tabList: [],
      includes: [],
      show: false,
      RefreshShow: true,
      type: true
    }
  },
  created () {
    this.menuData()
  },
  methods: {
    jump (to, childTo = '') {
      this.childTo = childTo
      this.menu.forEach(item => {
        if (item.to === to) {
          this.menuId = item.menuId
        }
      })
    },
    menuData () {
      this.name = JSON.parse(sessionStorage.getItem('name'))
      this.menu = JSON.parse(sessionStorage.getItem('menuChild'))
      if (this.$route.query.jumpId) {
        this.menuId = this.$route.query.jumpId
      } else {
        var queryId = JSON.parse(sessionStorage.getItem('queryId'))
        if (queryId) {
          this.menuId = queryId
        } else {
          this.menuId = this.menu[0].menuId
        }
      }
    },
    slidingClick (data) {
      sessionStorage.setItem('queryId', JSON.stringify(this.menuId))
      this.show = false
      this.menuActive = ''
      this.tabList = []
      this.includes = []
      if (data.to.includes('#')) { // 判断路由是否带有#号，如果是就是跳转首页
        this.type = true // 是首页就隐藏侧边栏盒子
        if (this.$route.path !== data.to.slice(1)) {
          this.$router.push({ path: data.to.slice(1) })
        }
        setTimeout(() => {
          this.show = true
        }, 300)
      } else {
        this.type = false
        this.menuChildData(data) // 侧边栏菜单默认进来打开第一个菜单菜单
      }
    },
    menuChildData (data) {
      this.menuChild = data.children
      var arr = []
      if (this.childTo) {
        arr = this.menuDataTo(this.menuChild)
      } else if (this.jumpTo) {
        arr = this.jumpToData(this.menuChild, this.jumpTo)
      } else {
        arr = this.menuDataList(this.menuChild)
      }
      this.childTo = ''
      this.menuActive = arr[0].id
      this.jumpTo = ''
      setTimeout(() => {
        this.show = true
      }, 300)
    },
    jumpToData (data, to) {
      let arr = []
      data.forEach(item => {
        if (item.children.length === 0) {
          if (item.to === to) {
            arr.push({ id: item.menuId, name: item.name, to: item.to })
          }
        } else {
          arr = arr.concat(this.jumpToData(item.children, to))
        }
      })
      return arr
    },
    menuDataTo (data, to) {
      let arr = []
      data.forEach(item => {
        if (item.to === to) {
          arr.push({ id: item.menuId, name: item.name, to: item.to })
        }
        if (item.children.length) {
          arr = arr.concat(this.menuDataTo(item.children))
        }
      })
      return arr
    },
    menuDataList (data) {
      let arr = []
      data.forEach((item, index) => {
        if (index === 0) {
          if (item.children.length === 0) {
            arr.push({ id: item.menuId, name: item.name, to: item.to })
          } else {
            arr = arr.concat(this.menuDataList(item.children))
          }
        }
      })
      return arr
    },
    rowClick (data) {
      // if (data) {
      //   sessionStorage.setItem('curMenuItem', JSON.stringify(data))
      // }
      if (JSON.stringify(this.jumpData) === '{}') {
        this.newTabMethods(data)
      } else {
        this.newTabJumpData(data, this.jumpData)
      }
    },
    newTabJumpData (data, params) {
      const arr = this.tabList
      let logo = 1
      arr.forEach((item, index) => {
        if (data.to === item.to) {
          logo = 0
          setTimeout(() => {
            this.tabId = data.menuId
          }, 100)
        }
      })
      if (logo) {
        arr.push({ name: data.name, id: data.menuId, type: true, to: data.to, params: params })
        this.tabId = data.menuId
        this.includes.push(data.to.substring(1))
      }
      this.tabList = arr
      this.jumpData = {}
    },
    tabClick (data) {
      var tabs = this.tabList
      this.tabList = tabs.filter(tab => tab.id !== data.id)
      var includes = this.includes
      this.includes = includes.filter(tab => tab !== data.to.substring(1))
    },
    tabRefresh (data) {
      this.includesData(data.to.substring(1))
      this.RefreshShow = false
      setTimeout(() => {
        this.includes.push(data.to.substring(1))
        this.RefreshShow = true
      }, 200)
    },
    refresh (id) {
      var data = ''
      if (id) {
        this.tabList.forEach(item => {
          if (id === item.id || id === item.to) {
            data = item
          }
        })
      } else {
        this.tabList.forEach(item => {
          if (this.tabId === item.id) {
            data = item
          }
        })
      }
      this.includesData(data.to.substring(1))
      this.RefreshShow = false
      setTimeout(() => {
        this.includes.push(data.to.substring(1))
        this.RefreshShow = true
      }, 200)
    },
    tabDelete (id) {
      var includes = this.includes
      this.tabList.forEach((item, index) => {
        if (item.id === id) {
          if (index === 0) {
            this.tabDataDelete(id, 1)
          } else {
            this.tabDataDelete(id, index - 1)
          }
          this.includes = includes.filter(tab => tab !== item.to.substring(1))
        }
      })
    },
    tabDelJump (id, toId) {
      this.tabId = toId
      var includes = this.includes
      this.tabList.forEach((item, index) => {
        if (item.id === id) {
          this.includes = includes.filter(tab => tab !== item.to.substring(1))
        }
      })
      var tabs = this.tabList
      this.tabList = tabs.filter(tab => tab.id !== id)
    },
    tabNameDelete (name) {
      var includes = this.includes
      this.tabList.forEach((item, index) => {
        if (item.to === name) {
          if (index === 0) {
            this.tabDataDelete(item.id, 1)
          } else {
            this.tabDataDelete(item.id, index - 1)
          }
          this.includes = includes.filter(tab => tab !== item.to.substring(1))
        }
      })
    },
    tabDataDelete (id, index) {
      var tabs = this.tabList
      // if (index) {
      this.tabId = tabs[index].id
      // }
      this.tabList = tabs.filter(tab => tab.id !== id)
    },
    includesData (to) {
      const arr = this.includes
      arr.forEach((item, index) => {
        if (item === to) {
          arr.splice(index, 1)
        }
      })
      this.includes = arr
    },
    newTabMethods (data) {
      const arr = this.tabList
      let logo = 1
      arr.forEach((item, index) => {
        if (data.to === item.to) {
          logo = 0
          setTimeout(() => {
            this.tabId = data.menuId
          }, 100)
        }
      })
      if (logo) {
        arr.push({ name: data.name, id: data.menuId, to: data.to, params: data.params })
        this.tabId = data.menuId
        this.includes.push(data.to.substring(1))
      }
      this.tabList = arr
    },
    newMenuTab (data) {
      var arr = this.newTabData(this.menuChild, data.to)
      if (arr.length) {
        this.menuActive = arr[0].menuId
      }
    },
    newTabData (data, to) {
      var arr = []
      data.forEach(item => {
        if (item.children.length === 0) {
          if (item.to === to) {
            arr.push(item)
          }
        } else {
          arr = arr.concat(this.newTabData(item.children, to))
        }
      })
      return arr
    },
    newTab (data) {
      const arr = this.tabList
      var i = null
      arr.forEach((item, index) => {
        if (data.menuId === item.id) {
          i = index
          arr.splice(index, 1)
          this.includesData(data.to.substring(1))
        }
      })
      if (i !== null) {
        // data.params.toId = this.tabId
        arr.splice(i, 0, { name: data.name, id: data.menuId, to: data.to, type: true, params: { ...data.params, toId: this.tabId } })
        setTimeout(() => {
          this.includes.push(data.to.substring(1))
        }, 100)
      } else {
        // data.params.toId = this.tabId
        arr.push({ name: data.name, id: data.menuId, to: data.to, type: true, params: { ...data.params, toId: this.tabId } })
        this.includes.push(data.to.substring(1))
      }
      this.tabId = data.menuId
      this.tabList = arr
    },
    returnClick () {
      sessionStorage.removeItem('queryId')
      sessionStorage.removeItem('menuChild')
      sessionStorage.removeItem('meetMenuChild')
      localStorage.removeItem('moduleName')
      // let flag = sessionStorage.getItem('theme')
      // if (flag === 'Abz') {
      //   this.$router.push({ name: 'home' })
      // } else {
      this.$router.push({ name: 'home' })
      // }
    },
    jumpMenu (to, params = {}) {
      var menu = this.filterMenu(this.menu, this.menuFilterNode, to) || []
      if (menu.length) {
        this.jumpTo = to
        this.jumpData = params
        this.menuId = menu[0].menuId
      } else {
        this.$message.error('未检测到你有此菜单！')
      }
    },
    filterMenu (nodes, predicate, to) {
      if (!nodes || !nodes.length) return void 0  // eslint-disable-line
      const children = []
      for (let node of nodes) {
        node = Object.assign({}, node)
        const sub = this.filterMenu(node.children, predicate, to)
        if ((sub && sub.length) || predicate(node, to)) {
          sub && (node.children = sub)
          children.push(node)
        }
      }
      return children.length ? children : void 0  // eslint-disable-line
    },
    menuFilterNode (data, to) {
      return data.to.includes(to)
    }
  },
  watch: {
    tabId (val) {
      if (val) {
        this.menuActive = val
      }
    }
  }
}
