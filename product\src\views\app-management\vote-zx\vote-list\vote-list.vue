<template>
  <div class="vote-list">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入内容"
                v-model="keyword"
                clearable
                @keyup.enter.native="search"></el-input>
      <el-date-picker v-model="startTime"
                      type="date"
                      placeholder="开始日期"
                      clearable
                      value-format="yyyy-MM-dd">
      </el-date-picker>
      <el-date-picker v-model="endTime"
                      type="date"
                      placeholder="结束日期"
                      clearable
                      value-format="yyyy-MM-dd">
      </el-date-picker>
      <el-select v-model="release"
                 placeholder="是否发布">
        <el-option v-for="item in releaseList"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
      <el-select v-if="release === '1'"
                 v-model="status"
                 clearable
                 placeholder="状态">
        <el-option v-for="item in statusList"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="handleAdd">新增</el-button>
      <el-button type="primary"
                 @click="handleExport">导出</el-button>
      <el-button type="primary"
                 @click="handleDelete">删除</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="问卷名称"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="openDetail(scope.row)">{{scope.row.name}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="调查时间"
                           width="400">
            <template slot-scope="scope">{{scope.row.startTime}}至{{scope.row.endTime}}</template>
          </el-table-column>
          <el-table-column label="发布者"
                           prop="createBy"
                           min-width="120"></el-table-column>
          <el-table-column label="组织方"
                           prop="organize"
                           min-width="120"></el-table-column>
          <el-table-column label="发布时间"
                           prop="publishDate"
                           min-width="200"></el-table-column>
          <el-table-column label="应参与人数"
                           prop="shouldCount"
                           min-width="120"></el-table-column>
          <el-table-column label="参与人数"
                           width="120"
                           prop="answerCount">
          </el-table-column>
          <el-table-column label="是否发布"
                           width="160"
                           prop="isPublish">
          </el-table-column>
          <el-table-column label="状态"
                           prop="status"
                           width="160">
          </el-table-column>
          <el-table-column label="操作"
                           min-width="480">
            <template slot-scope="scope">
              <el-button @click="handlePublish(scope.row)"
                         v-if="scope.row.isPublic === '1'"
                         type="primary"
                         plain
                         size="mini">发布</el-button>
              <el-button @click="handlePublishCancel(scope.row)"
                         v-if="scope.row.isPublic === '0'"
                         type="primary"
                         plain
                         size="mini">取消发布</el-button>
              <el-button @click="handleEdit(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
              <el-button @click="handleQuestion(scope.row)"
                         type="primary"
                         plain
                         size="mini">题目</el-button>
              <el-button @click="handleCount(scope.row)"
                         type="primary"
                         plain
                         size="mini">统计详情</el-button>
              <el-button @click="handlePreview(scope.row)"
                         type="primary"
                         plain
                         size="mini">预览</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="isAdd"
               :title="addTitle">
      <vote-add :editData="editData"
                @cancel="handleAddCancel"></vote-add>
    </zy-pop-up>
    <zy-pop-up v-model="isMan"
               title="参加人数">
      <join-man :id="paperId"></join-man>
    </zy-pop-up>
    <qr-Code v-if="isQrcode"
             @cancel="isQrcode = false"
             :id="paperId"></qr-Code>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <!--TDD 导出的type 要修改 -->
      <zy-export :excelId="excelId"
                 :type="107"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
  </div>
</template>
<script>
import voteAdd from '../components/vote-add'
import joinMan from '../components/join-man'
import qrCode from '../components/qr-code'
export default {
  inject: ['newTab'],
  data () {
    return {
      keyword: '',
      startTime: null,
      endTime: null,
      status: '',
      release: '',
      releaseList: [
        { label: '已发布', value: '1' },
        { label: '未发布', value: '0' }
      ],
      statusList: [
        { label: '未开始', value: '0' },
        { label: '进行中', value: '1' },
        { label: '已结束', value: '2' }
      ],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      selectionList: [],
      isAdd: false,
      addTitle: '新增',
      isMan: false,
      isQrcode: false,
      paperId: null,
      editData: null,
      exportShow: false,
      excelId: null
    }
  },
  components: { voteAdd, joinMan, qrCode },
  mounted () {
    this.getList()
  },
  methods: {
    // 列表
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        isPublic: 1,
        module: 'questionnaire'
      }
      if (this.keyword !== '') {
        data.keyword = this.keyword
      }
      if (this.startTime && this.endTime) {
        data.startTime = this.startTime
        data.endTime = this.endTime
      }
      if (this.release !== '') {
        data.isPublish = this.release
      }
      if (this.status !== '') {
        data.status = this.status
      }
      this.$api.appManagement.voteList(data).then(res => {
        const { errcode, total, data } = res
        if (errcode === 200) {
          this.total = total
          this.tableData = data
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.startTime = null
      this.endTime = null
      this.status = ''
      this.release = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 激活新增弹窗
    handleAdd () {
      this.isAdd = true
      this.addTitle = '新增'
      this.editData = null
    },
    // 激活编辑弹窗
    handleEdit (val) {
      this.editData = val
      this.isAdd = true
      this.addTitle = '编辑'
    },
    // 取消弹窗
    handleAddCancel (val) {
      this.isAdd = false
      this.editData = null
      if (val) {
        this.getList()
      }
    },
    // 导出
    handleExport () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.exportShow = true
    },
    // 发布
    handlePublish (val) {
      this.$confirm('此操作将发布此问卷, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          id: val.id,
          isRelease: '0',
          module: 'questionnaire'
        }
        this.$api.appManagement.voteAdd(data).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('发布成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    // 取消发布
    handlePublishCancel (val) {
      this.$confirm('此操作将取消发布此问卷, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          id: val.id,
          isRelease: '1',
          module: 'questionnaire'
        }
        this.$api.appManagement.voteAdd(data).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('取消发布成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    // 删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要删除的项')
      }
      this.$confirm('此操作将删除选中的问卷, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectionList.map(item => item.id).join(',')
        this.$api.appManagement.voteDelete(ids).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    // 参与投票人
    handleJoinMan (val) {
      this.isMan = true
      this.paperId = val.id
    },
    // 题目
    handleQuestion (val) {
      this.newTab({ name: '题目', menuId: val.id, to: '/zx-question', params: { id: val.id } })
    },
    // 打开详情
    openDetail (val) {
      this.newTab({ name: '问卷详情', menuId: val.id, to: '/zx-questionPurview', params: { id: val.id } })
    },
    // 统计
    handleCount (val) {
      this.newTab({ name: '结果查看和统计', menuId: val.id, to: '/zx-voteCount', params: { id: val.id } })
    },
    // 预览
    handlePreview (val) {
      this.isQrcode = true
      this.paperId = val.id
    }
  }
}
</script>
<style lang="scss">
@import "./vote-list.scss";
</style>
