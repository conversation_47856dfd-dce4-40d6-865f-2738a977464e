.zy-cascader {
  width: 100%;
  height: 40px;

  .is-disabled {
    .el-input__inner {
      color: #262626;
      background-color: #E6E5E8;
    }
  }

  .el-input__suffix {
    line-height: 40px;
  }

  .zy-cascader-icon {
    margin-right: 9px;
    transition-duration: .4s;
    transform: rotate(0);
  }

  .zy-cascader-icon-a {
    transition-duration: .4s;
    transform: rotate(-180deg);
  }
}


.zy-cascader-popover {
  padding: 6px 0;

  .zy-cascader-box {
    height: 220px;
    max-height: 220px;
    width: 100%;

    .el-scrollbar__wrap {
      overflow-x: hidden;

      .zy-tree-components {
        width: 100% !important;
      }
    }
  }
}