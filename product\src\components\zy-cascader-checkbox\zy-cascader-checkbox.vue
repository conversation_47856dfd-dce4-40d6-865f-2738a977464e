<template>
  <div class="zy-cascader-checkbox"
       :style="{ width: width + 'px' }">
    <el-popover popper-class="zy-cascader-checkbox-popover"
                :trigger="trigger"
                :disabled="disabled"
                :width="width"
                v-model="options_show">
      <div slot="reference"
           :class="['zy-cascader-checkbox-input',disabled?'zy-cascader-checkbox-input-disabled':'']">
        <div :class="['zy-cascader-checkbox-input-icon',options_show?'zy-cascader-checkbox-input-icon-a':'']">
          <i class="el-icon-arrow-down"></i>
        </div>
        <div v-if="!selecteds.length"
             class="zy-cascader-checkbox-input-text">{{placeholder}}</div>
        <el-tag closable
                size="medium"
                v-for="item in selecteds"
                :disable-transitions="false"
                :title="item[selfProps.label]"
                :key="item[nodeKey]"
                @close="tabClose(item[nodeKey])">{{ item[selfProps.label] }}</el-tag>
      </div>

      <el-scrollbar class="zy-cascader-checkbox-box-box">
        <div>
          <el-input placeholder="请输入关键字查询"
                    v-model="keyword"
                    clearable>
          </el-input>
        </div>
        <el-scrollbar class="zy-cascader-checkbox-box">
          <el-tree show-checkbox
                   :data="selfData"
                   ref="tree-select"
                   highlight-current
                   :props="selfProps"
                   :node-key="nodeKey"
                   @check="handleCheckChange"
                   :filter-node-method="filterNode"
                   class="zy-cascader-checkbox-tree"
                   :default-checked-keys="checked_keys"
                   :default-expand-all="defaultExpandAll"
                   :default-expanded-keys="defaultExpandedKeys"></el-tree>
        </el-scrollbar>
      </el-scrollbar>
    </el-popover>
  </div>
</template>
<script>
export default {
  name: 'zyCascaderCheckbox',
  data () {
    return {
      keyword: '',
      selecteds: [],
      options_show: false,
      checked_keys: []
    }
  },
  props: {
    // 数据
    data: {
      type: Array,
      default: () => []
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // node-key
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 选中数据
    value: [String, Number, Array, Object],
    // 是否只可选叶子节点
    leaf: {
      type: Boolean,
      default: true
    },
    // 宽度
    width: String,
    // 触发方式 click/focus/hover/manual
    trigger: {
      type: String,
      default: 'click'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否展开全部
    defaultExpandAll: {
      type: Boolean,
      default: false
    },
    // 默认展开的节点的 key 的数组
    defaultExpandedKeys: {
      type: Array,
      default: () => {
        return []
      }
    },
    max: {
      type: Number,
      default: 0
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  created () {
    this.chaeckDefaultValue()
  },
  watch: {
    keyword (val) {
      this.$refs['tree-select'].filter(val)
    },
    value (val) {
      this.chaeckDefaultValue()
    },
    data (val) {
      this.chaeckDefaultValue()
    }
  },
  computed: {
    selfData () {
      return this.data
    },
    selfProps () {
      return {
        label: 'label',
        children: 'children',
        disabled: data => {
          return data.disabled
        },
        ...this.props
      }
    }
  },
  methods: {
    filterNode (value, data) {
      if (!value) return true
      return data[this.selfProps.label].indexOf(value) !== -1
    },
    handleCheckChange () {
      const nodes = this.$refs['tree-select'].getCheckedNodes(this.leaf)
      this.selecteds = nodes
      this.$emit('change', nodes)
    },
    tabClose (Id) {
      if (this.disabled) return
      this.$refs['tree-select'].setChecked(Id, false, true)
      this.selecteds = this.$refs['tree-select'].getCheckedNodes()
      this.$emit('change', this.selecteds)
    },
    chaeckDefaultValue () {
      const val = this.value
      if (!val || (Array.isArray(val) && val.length === 0)) {
        this.selecteds = []
        this.checked_keys = []
        this.$nextTick(() => {
          this.$refs['tree-select'].setCheckedKeys([])
        })
        return
      }
      this.checked_keys = typeof val[0] === 'object' ? val.map(i => i[this.nodeKey]) : val
      this.$nextTick(() => {
        this.selecteds = this.$refs['tree-select'].getCheckedNodes(this.leaf)
      })
    }
  }
}
</script>
<style lang="scss">
@import "./zy-cascader-checkbox.scss";
</style>
