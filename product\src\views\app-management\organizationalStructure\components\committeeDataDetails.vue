<template>
  <div class="committeeDataDetails details">
    <div class="details-title">详情</div>
    <div class="details-item-box">
      <div class="details-item-title">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.title}}</div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">所属栏目</div>
          <div class="details-item-value">{{details.structureName}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">发布人</div>
          <div class="details-item-value">{{details.createBy}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">来源</div>
          <div class="details-item-value">{{details.source}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">发布时间</div>
          <div class="details-item-value">{{details.publishDate}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">附件</div>
        <div class="details-item-value">
          <div class="details-item-file"
               v-for="(item, index) in details.attachmentList"
               :key="index"
               @click="fileClick(item)">{{item.fileName}}</div>
        </div>
      </div>
      <div class="details-content"
           v-html="details.content"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'committeeDataDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.informationListInfo()
  },
  methods: {
    async informationListInfo () {
      const res = await this.$api.appManagement.informationListInfo(this.id)
      var { data } = res
      this.details = data
      console.log(this.details.attachList)
    },
    fileClick (row) {
      this.$api.proposal.downloadFile({ id: row.id }, row.fileName)
    }
  }
}
</script>
<style lang="scss">
.committeeDataDetails {
  height: 100%;
  padding: 24px;

  .details-content {
    width: 100%;
    padding: 40px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
