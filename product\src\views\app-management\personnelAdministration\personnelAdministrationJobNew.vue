<template>
  <div class="personnelAdministrationJobNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             :show-message="false"
             label-width="136px"
             class="newFormPaper">
      <div class="title">在职人员信息</div>
      <el-form-item label="姓名"
                    class="formInput"
                    prop="name">
          <el-input placeholder="请输入姓名"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="性别"
                    class="formInput"
                    prop="gender">
        <el-select v-model="form.gender"
                   filterable
                   clearable
                   placeholder="请选择性别">
          <el-option v-for="item in genderData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="民族"
                    class="formInput formBorder"
                    prop="nation">
         <el-select v-model="form.nation"
                   filterable
                   clearable
                   placeholder="请选择民族">
          <el-option v-for="item in nationData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出生年月"
                    class="formInput">
         <el-date-picker type="month"
                        v-model="form.birthday"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        placeholder="请选择出生年月">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="政治面貌"
                    class="formInput formBorder"
                    prop="name">
        <el-select v-model="form.politicCountenance"
                   filterable
                   clearable
                   placeholder="请选择政治面貌">
          <el-option v-for="item in politicCountenanceData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="入党时间"
                    class="formInput">
        <el-date-picker type="month"
                        v-model="form.joinPartyDate"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        placeholder="请选择入党时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="教育情况"
                    class="formInput formBorder"
                    prop="education">
        <el-select v-model="form.education"
                   filterable
                   clearable
                   placeholder="请选择教育情况">
          <el-option v-for="item in educationData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="参加工作时间"
                    class="formInput"
                    prop="joinWorkDate">
       <el-date-picker type="month"
                        v-model="form.joinWorkDate"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        placeholder="请选择参加工作时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="所在部门"
                    class="formInput formBorder"
                    prop="department">
        <el-select v-model="form.department"
                   filterable
                   clearable
                   placeholder="请选择所在部门">
          <el-option v-for="item in departmentData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="进入政协时间"
                    class="formInput"
                    prop="joinCppccDate">
        <el-date-picker type="month"
                      v-model="form.joinCppccDate"
                      format="yyyy-MM"
                      value-format="yyyy-MM"
                      placeholder="请选择进入政协时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="职务"
                    class="formInput formBorder">
          <el-select v-model="form.duty"
                   filterable
                   clearable
                   placeholder="请选择职务">
            <el-option v-for="item in dutyData"
                      :key="item.id"
                      :label="item.value"
                      :value="item.id">
            </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="职级"
                    class="formInput">
        <el-select v-model="form.rank"
                   filterable
                   clearable
                   placeholder="请选择职级">
          <el-option v-for="item in rankData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注"
                    class="formUpload">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入需要备注的内容"
          v-model="form.remarks">
        </el-input>
      </el-form-item>
      <div class="formButton">
        <el-button type="primary"
                   @click="submitForm('form')">提 交</el-button>
        <el-button @click="reset">{{id ? '取&nbsp;&nbsp;&nbsp;消' : '重&nbsp;&nbsp;&nbsp;置'}}</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        name: '',
        gender: '',
        nation: '',
        politicCountenance: '',
        joinPartyDate: '',
        education: '',
        joinWorkDate: '',
        joinCppccDate: '',
        department: '',
        duty: '',
        rank: '',
        remarks: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'blur' }
        ],
        nation: [
          { required: true, message: '请选择民族', trigger: 'blur' }
        ],
        politicCountenance: [
          { required: true, message: '请选择政治面貌', trigger: 'blur' }
        ],
        education: [
          { required: true, message: '请选择教育情况', trigger: 'blur' }
        ],
        joinWorkDate: [
          { required: true, message: '请选择参加工作时间', trigger: 'blur' }
        ],
        joinCppccDate: [
          { required: true, message: '请选择进入政协时间', trigger: 'blur' }
        ]
      },
      genderData: [],
      nationData: [],
      politicCountenanceData: [],
      educationData: [],
      departmentData: [],
      dutyData: [],
      rankData: [],
      id: this.$route.query.id
    }
  },
  inject: ['refresh'],
  mounted() {
    this.dictionaryPubkvs()
    if (this.id) {
      this.opinionexamineInfo()
    }
  },
  methods: {
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/incumbentinfo/add'
          if (this.id) {
            url = '/incumbentinfo/edit'
          }
          var form = this.form
          this.$api.systemSettings.generalAdd(url, {
            id: this.id,
            name: form.name,
            gender: form.gender,
            nation: form.nation,
            birthday: form.birthday,
            politicCountenance: form.politicCountenance,
            education: form.education,
            department: form.department,
            duty: form.duty,
            joinPartyDate: form.joinPartyDate,
            joinWorkDate: form.joinWorkDate,
            joinCppccDate: form.joinCppccDate,
            rank: form.rank,
            remarks: form.remarks
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
              this.refresh()
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    reset () {
      this.$confirm(`${this.id ? '取消' : '重置'}将不会保存当前已编辑的内容, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('callback')
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消${this.id ? '操作' : '重置'}`
        })
      })
    },
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'sex,nation_type,politic_countenance_type,education_type,department_type,duty_type,incumbent_rank_type'
      })
      var { data } = res
      this.genderData = data.sex
      this.nationData = data.nation_type
      this.politicCountenanceData = data.politic_countenance_type
      this.educationData = data.education_type
      this.departmentData = data.department_type
      this.dutyData = data.duty_type
      this.rankData = data.incumbent_rank_type
    },
    async opinionexamineInfo () {
      const res = await this.$api.appManagement.incumbentinfoInfo(this.id)
      var { data } = res
      this.form = data
    }
  }
}
</script>
<style lang="scss">
  .personnelAdministrationJobNew {
    width: 1080px;
    padding: 24px;
    margin: 0 auto;
    .formBorder {
      border-left: none!important;
    }
  }
</style>
