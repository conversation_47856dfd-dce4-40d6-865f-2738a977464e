<template>
  <div class="activityDataDetails details">
    <div class="details-title">活动资料详情</div>
    <div class="details-item-box">
      <div class="details-item">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.materialName}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">提交人</div>
        <div class="details-item-value">{{details.createName}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">提交时间</div>
        <div class="details-item-value">{{details.createDate}}</div>
      </div>
      <div class="details-item-img activityDataimg">
        <div class="details-item-label">封面图</div>
        <div class="details-item-value">
          <template v-if="details.photo">
            <img :src="details.photo[0].filePath"
                 v-if="details.photo.length"
                 alt="">
          </template>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">附件</div>
        <div class="details-item-value">
          <div class="details-item-file"
               v-for="(item, index) in details.attachId"
               :key="index"
               @click="fileClick(item)">{{item.fileName}}</div>
        </div>
      </div>
      <div class="details-content"
           v-html="details.content"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'activityDataDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.materiainfoInfo()
  },
  methods: {
    async materiainfoInfo () {
      const res = await this.$api.activity.materiainfoInfo(this.id)
      var { data } = res
      this.details = data
    },
    fileClick (data) {
      this.$api.proposal.downloadFile({ id: data.id }, data.fileName)
    }
  }
}
</script>
<style lang="scss">
.activityDataDetails {
  padding: 24px;
  .activityDataimg {
    .details-item-value {
      padding-left: 22px;
      justify-content: flex-start !important;
    }
  }
  .details-content {
    width: 100%;
    padding: 40px;
    line-height: 22px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
