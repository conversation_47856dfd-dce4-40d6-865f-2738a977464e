<template>
  <div class="join-man">
    <div class="search-box">
      <el-input placeholder="请输入关键字"
                size="mini"
                v-model="keyword"
                clearable
                @keyup.enter.native="search"></el-input>
      <!-- <el-select v-model="type"
        style="width:180px;margin:0 10px"
        size="mini"
        clearable
        placeholder="所属分类">
        <el-option v-for="item in typeList"
          :key="item.id"
          :label="item.value"
          :value="item.id">
        </el-option>
      </el-select> -->
      <!-- <zy-cascader width="222"
        node-key="id"
        clearable
        v-model="type"
        :data="typeList"
        placeholder="所属分类"
        style="margin-right:15px;">
      </zy-cascader> -->
      <el-select v-model="topic"
                 style="width:150px; margin-right:10px;"
                 size="mini"
                 clearable
                 placeholder="题型">
        <el-option v-for="item in topicList"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
      <el-button size="mini"
                 type="primary"
                 @click="search">搜索</el-button>
      <el-button size="mini"
                 @click="reset">重置</el-button>
    </div>
    <div class="tableData scrollBar">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fisVisibleixed="left"
                           width="60"></el-table-column>
          <el-table-column label="题目"
                           min-width="260"
                           prop="name"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="所属分类"
                           min-width="150"
                           prop="typeLabel"></el-table-column>
          <el-table-column label="题型"
                           width="160">
            <template slot-scope="scope">
              <span v-if="scope.row.topic === 'single'">单选</span>
              <span v-if="scope.row.topic === 'multi'">多选</span>
              <span v-if="scope.row.topic === 'judge'">判断</span>
              <span v-if="scope.row.topic === 'text'">文本</span>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="page-box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <div class="check-btn">
      <el-button size="small"
                 type="primary"
                 @click="handleEnter">确定</el-button>
      <el-button size="small"
                 @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import questionType from '@/mixins/questionType'
export default {
  props: {
    paperId: String,
    arr: Array
  },
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize()
    }
  },
  mounted () {
    this.getList()
  },
  mixins: [tableData, questionType],
  methods: {
    // 列表
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        isPublic: 0
      }
      if (this.keyword !== '') {
        data.keyword = this.keyword
      }
      if (this.type !== '') {
        data.type = this.type
      }
      if (this.topic !== '') {
        data.topic = this.topic
      }
      this.$api.appManagement.questionList(data).then(res => {
        const { errcode, total, data } = res
        if (errcode === 200) {
          this.total = total
          this.tableData = data
          this.$nextTick(function () {
            this.memoryChecked()
          })
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.type = ''
      this.topic = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 确认
    handleEnter () {
      if (this.choose.length === 0) {
        return this.$message.warning('请选择想要导出的项')
      }
      const ids = this.choose.join(',')
      this.$api.appManagement.voteImportQuestion({ id: this.paperId, ids: ids }).then(res => {
        if (res.errcode === 200) {
          this.$emit('cancel', true)
          this.$message.success('添加题目成功')
        }
      })
    },
    // 发布
    // handlePublish () {
    //   this.$api.appManagement.publicPaper({ id: this.paperId }).then(res => {
    //     if (res.errcode === 200) {
    //       this.$emit('cancel', true)
    //       this.$message.success('添加题目成功')
    //     }
    //   })
    // },
    // 取消
    handleCancel () {
      this.$emit('cancel')
    }
  }
}
</script>
<style lang="scss">
@import "./dialog-list.scss";
</style>
