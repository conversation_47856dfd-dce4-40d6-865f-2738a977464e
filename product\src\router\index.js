import Vue from 'vue'
import VueRouter from 'vue-router'
// import generalList from './module'
const files = require.context('./module', false, /\.js$/)
var pages = []
files.keys().forEach(key => {
  pages = pages.concat(files(key).default)
})
Vue.use(VueRouter)

const routes = [
  {
    path: '/app',
    name: 'app',
    component: () => import(/* app分享二维码 */'@/views/main/appShare/app')
  },
  {
    path: '/overdue',
    name: 'overdue',
    component: () => import(/* 过期页面 */'@/views/main/overdue/overdue')
  },
  {
    path: '/switchpage',
    name: 'switchpage',
    component: () => import(/* 入口切换页 */'@/views/main/home/<USER>')
  },
  {
    path: '/general',
    name: 'general',
    component: () => import(/* layout 布局页面 */'@/views/general/general'),
    children: pages
  },
  {
    path: '/nestedPage',
    name: 'nestedPage',
    component: () => import(/* 通用嵌入页面 */'@/views/nestedPage/nestedPage')
  }
]

const createRouter = () => new VueRouter({
  scrollBehavior: () => {
    history.pushState(null, null, document.URL)
  },
  routes
})

const router = createRouter()
export { routes }
export function resetRouter () {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
