const menuManagement = () => import('@/views/system-settings/menu-management/menu-management')
const permissionsManagement = () => import('@/views/system-settings/permissions-management/permissions-management')
const roleManagement = () => import('@/views/system-settings/role-management/role-management')
const userManagement = () => import('@/views/system-settings/user-management/user-management')
const dictionaryManagement = () => import('@/views/system-settings/dictionary-management/dictionary-management')
const treeManagement = () => import('@/views/system-settings/tree-management/tree-management')
const logManagement = () => import('@/views/system-settings/log-management/log-management')
const loginLogManagement = () => import('@/views/system-settings/log-management/login-log-management')
const errorLogManagement = () => import('@/views/system-settings/log-management/error-log-management')
const taskManagement = () => import('@/views/system-settings/task-management/task-management')
const systemParameters = () => import('@/views/system-settings/system-parameters/system-parameters')
const candidatesManagement = () => import('@/views/system-settings/candidates-management/candidates-management')
const labelManagement = () => import('@/views/system-settings/label-management/label-management')
const userList = () => import('@/views/system-settings/user-list/user-list')
const userRelationship = () => import('@/views/system-settings/user-relationship/user-relationship')
const historySmsList = () => import('@/views/system-settings/sms-management/history-sms-list/history-sms-list')
const historySms = () => import('@/views/system-settings/sms-management/history-sms/history-sms')
const historySmsc = () => import('@/views/system-settings/sms-management/history-sms/history-sms-c')
const sendSms = () => import('@/views/system-settings/sms-management/sms-new/sendSms')
const smstemplate = () => import('@/views/system-settings/sms-management/sms-template/sms-template')
const upsidesms = () => import('@/views/system-settings/sms-management/upside-sms/upside-sms')
const TheKeyWork = () => import('@/views/general/TheKeyWork.vue')
const postman = () => import('@/views/system-settings/postman/postman')
const ShufflingFigure = () => import('@/views/system-settings/ShufflingFigure/ShufflingFigure')
const undertakeUnitUser = () => import('@/views/system-settings/user-management/undertakeUnitUser')
const editorRecordList = () => import('@/views/system-settings/user-management/editorRecord/editorRecordList')
const testPrint = () => import('@/views/system-settings/testPrint/testPrint')
const apkManage = () => import('@/views/system-settings/apkManage/apkManage')
const ValidationControl = () => import('@/views/system-settings/ValidationControl/ValidationControl')
const systemSettings = [
  { // 菜单管理
    path: '/ShufflingFigure',
    name: 'ShufflingFigure',
    component: ShufflingFigure
  },
  { // 菜单管理
    path: '/postman',
    name: 'postman',
    component: postman
  },
  { // 菜单管理
    path: '/TheKeyWork',
    name: 'TheKeyWork',
    component: TheKeyWork
  },
  { // 菜单管理
    path: '/menuManagement',
    name: 'menuManagement',
    component: menuManagement
  },
  { // 权限管理
    path: '/permissionsManagement',
    name: 'permissionsManagement',
    component: permissionsManagement
  },
  { // 字典管理
    path: '/dictionaryManagement',
    name: 'dictionaryManagement',
    component: dictionaryManagement
  },
  { // 角色管理
    path: '/roleManagement',
    name: 'roleManagement',
    component: roleManagement
  },
  { // 树管理
    path: '/treeManagement',
    name: 'treeManagement',
    component: treeManagement
  },
  { // 办理单位用户
    path: '/undertakeUnitUser',
    name: 'undertakeUnitUser',
    component: undertakeUnitUser
  },
  { // 普通用户管理
    path: '/userManagement',
    name: 'userManagement',
    component: userManagement
  },
  { // 日志管理
    path: '/logManagement',
    name: 'logManagement',
    component: logManagement
  },
  { // 日志管理
    path: '/loginLogManagement',
    name: 'loginLogManagement',
    component: loginLogManagement
  },
  { // 日志管理
    path: '/errorLogManagement',
    name: 'errorLogManagement',
    component: errorLogManagement
  },
  { // 任务管理
    path: '/taskManagement',
    name: 'taskManagement',
    component: taskManagement
  },
  { // 配置管理
    path: '/systemParameters',
    name: 'systemParameters',
    component: systemParameters
  },
  { // 选人管理
    path: '/candidatesManagement',
    name: 'candidatesManagement',
    component: candidatesManagement
  },
  { // 标签管理
    path: '/labelManagement',
    name: 'labelManagement',
    component: labelManagement
  },
  { // 用户列表
    path: '/userList',
    name: 'userList',
    component: userList
  },
  { // 用户关系管理
    path: '/userRelationship',
    name: 'userRelationship',
    component: userRelationship
  },
  { // 已发集合
    path: '/historySmsList',
    name: 'historySmsList',
    component: historySmsList
  },
  { // 已发集xian
    path: '/historySmsc',
    name: 'historySmsc',
    component: historySmsc
  },
  { // 已发集xian
    path: '/sendSms',
    name: 'sendSms',
    component: sendSms
  },
  { // 已发
    path: '/historySms',
    name: 'historySms',
    component: historySms
  },
  { // 上集
    path: '/upsidesms',
    name: 'upsidesms',
    component: upsidesms
  },
  { // 短信模板
    path: '/smstemplate',
    name: 'smstemplate',
    component: smstemplate
  },
  { // 用户修改记录
    path: '/editorRecordList',
    name: 'editorRecordList',
    component: editorRecordList
  },
  { // 测试打印
    path: '/testPrint',
    name: 'testPrint',
    component: testPrint
  },
  { // apk管理
    path: '/apkManage',
    name: 'apkManage',
    component: apkManage
  },
  { // 免验证管理
    path: '/ValidationControl',
    name: 'ValidationControl',
    component: ValidationControl
  }
]
export default systemSettings
