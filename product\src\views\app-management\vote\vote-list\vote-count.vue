<template>
  <div class="vote-count">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入内容"
                v-model="keyword"
                clearable
                @keyup.enter.native="search"></el-input>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="handleExport">导出</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="参与人员"
                           min-width="120"
                           prop="userName"></el-table-column>
          <el-table-column label="考试时间"
                           min-width="200"
                           prop="createDate"></el-table-column>
          <el-table-column label="耗时"
                           min-width="120"
                           prop="duration"></el-table-column>
          <el-table-column label="分数"
                           min-width="100"
                           prop="result"></el-table-column>
          <el-table-column label="成绩"
                           min-width="100"
                           prop="rank"></el-table-column>
          <el-table-column label="考试详情"
                           min-width="200">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="OpenDetail(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :excelId="excelId"
                 :params="params"
                 :type="601"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  inject: ['newTab'],
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      selectionList: [],
      params: null,
      exportShow: false,
      excelId: null
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 列表
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        id: this.$route.query.id
      }
      if (this.keyword !== '') {
        data.keyword = this.keyword
      }
      this.$api.appManagement.joinVoteUserList(data).then(res => {
        const { data, errcode, total } = res
        if (errcode === 200) {
          this.total = total
          this.tableData = data
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 查看详情
    OpenDetail (val) {
      console.log(val)
      this.newTab({ name: '考试详情', menuId: val.dataId, to: '/questionPurview', params: { id: this.$route.query.id, user: { userId: val.userId, userName: val.userName } } })
    },
    // 导出
    handleExport () {
      // if (this.selectionList.length === 0) {
      //   return this.$message.warning('请选中想要导出的项')
      // }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.params = {
        id: this.$route.query.id
      }
      this.exportShow = true
    }
  }
}
</script>
<style lang="scss" scoped>
.vote-count {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 192px);
  }
}
</style>
