<template>
  <div class="associatedActivity">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="associated">关联</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="标题"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.title}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="活动类型"
                           width="120"
                           prop="schemeType"></el-table-column>
          <el-table-column label="组织部门"
                           width="160"
                           prop="officeName"></el-table-column>
          <el-table-column label="活动状态"
                           width="120"
                           prop="schemeStatus"></el-table-column>
          <el-table-column label="活动时间"
                           width="360">
            <template slot-scope="scope">{{scope.row.startTime|datefmt('YYYY-MM-DD HH:mm:ss')}} — {{scope.row.endTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="detailsShow"
                      title="读书活动详情">
      <readingActivityDetails :id="uid"> </readingActivityDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import readingActivityDetails from '../readingActivity/readingActivityDetails'
export default {
  name: 'associatedActivity',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      uid: '',
      detailsShow: false
    }
  },
  props: ['id'],
  mixins: [tableData],
  components: {
    readingActivityDetails
  },
  mounted () {
    this.readschemeLists()
  },
  methods: {
    search () {
      this.page = 1
      this.readschemeList()
    },
    reset () {
      this.keyword = ''
      this.readschemeList()
    },
    details (row) {
      this.uid = row.id
      this.detailsShow = true
    },
    async readschemeLists () {
      const res = await this.$api.academy.readschemeList({
        noticeId: this.id
      })
      var { data } = res
      var ids = []
      data.forEach(item => {
        ids.push(item.id)
        this.selectObj[item.id] = item.id
      })
      this.selectObjData = data
      this.choose = ids
      this.readschemeList()
    },
    async readschemeList () {
      const res = await this.$api.academy.readschemeList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle () {
      this.readschemeList()
    },
    whatPage () {
      this.readschemeList()
    },
    associated () {
      if (this.choose.length) {
        this.$confirm('此操作将关联当前选中的读书活动, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.syNoticeJoinScheme(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消关联'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async syNoticeJoinScheme (id) {
      const res = await this.$api.academy.syNoticeJoinScheme({
        id: this.id,
        schemeIds: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$emit('callback')
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.associatedActivity {
  width: 990px;
  height: calc(85vh - 52px);
  padding: 0 22px;
  .tableData {
    height: calc(100% - 116px) !important;
  }
}
</style>
