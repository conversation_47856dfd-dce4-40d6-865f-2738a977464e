<template>
  <div class="question-bank">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入关键字"
                v-model="keyword"
                clearable
                @keyup.enter.native="search"></el-input>
      <!-- <el-select v-model="type"
        clearable
        placeholder="所属分类">
        <el-option v-for="item in typeList"
          :key="item.id"
          :label="item.value"
          :value="item.id">
        </el-option>
      </el-select> -->
      <zy-cascader width="222"
                   node-key="id"
                   clearable
                   v-model="type"
                   :data="typeList"
                   placeholder="所属分类"
                   style="margin-right:15px;">
      </zy-cascader>
      <el-select v-model="topic"
                 clearable
                 placeholder="题型">
        <el-option v-for="item in topicList"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="handleAdd">新增</el-button>
      <el-button type="primary"
                 @click="handleImport">导入</el-button>
      <el-button type="primary"
                 @click="handleExport">导出</el-button>
      <el-button type="primary"
                 @click="handleDelete">删除</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="题目"
                           min-width="260"
                           prop="name"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="openInfo(scope.row)">{{scope.row.name}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="所属分类"
                           min-width="150"
                           prop="typeLabel"></el-table-column>
          <el-table-column label="题型"
                           width="160">
            <template slot-scope="scope">
              <span v-if="scope.row.topic === 'single'">单选</span>
              <span v-if="scope.row.topic === 'multi'">多选</span>
              <span v-if="scope.row.topic === 'judge'">判断</span>
              <span v-if="scope.row.topic === 'text'">文本</span>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="handleEdit(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="isAddShow"
               :title="addTitle">
      <question-add @cancel="handleAddCancel"
                    :editData="editData"
                    :paperId="$route.query.id"></question-add>
    </zy-pop-up>
    <zy-pop-up title="题目详情"
               v-model="isInfo">
      <div class="question-info">
        <p class="title">{{this.detail.name}}(
          <span v-if="this.detail.topic === 'single'">单选</span>
          <span v-if="this.detail.topic === 'multi'">多选</span>
          <span v-if="this.detail.topic === 'judge'">判断</span>
          <span v-if="this.detail.topic === 'text'">文本</span>)
        </p>
        <div class="question-option"
             v-if="this.detail.options">
          <p v-for="(item,index) in this.detail.option"
             :key="index">{{index+1}}、{{item}}</p>
        </div>
        <div class="question-blank"
             v-if="!this.detail.options"></div>
      </div>
    </zy-pop-up>
    <zy-pop-up v-model="isImport"
               title="导入">
      <xyl-template-import templateUrl="/paperquestion/importemplate"
                           uploadUrl="/paperquestion/import"></xyl-template-import>
    </zy-pop-up>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :excelId="excelId"
                 :type="107"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
  </div>
</template>
<script>
import questionAdd from '../components/question-add'
import questionType from '@/mixins/questionType'
// import { baseURL } from '@/api/http'
export default {
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      isAddShow: false,
      selectionList: [],
      editData: null,
      detail: {},
      isInfo: false,
      isImport: false,
      addTitle: '新增',
      excelId: '',
      exportShow: false
    }
  },
  components: { questionAdd },
  mixins: [questionType],
  mounted () {
    this.getList()
  },
  methods: {
    // 列表
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        isPublic: 0
      }
      if (this.keyword !== '') {
        data.keyword = this.keyword
      }
      if (this.type !== '') {
        data.type = this.type
      }
      if (this.topic !== '') {
        data.topic = this.topic
      }
      this.$api.appManagement.questionList(data).then(res => {
        const { errcode, total, data } = res
        if (errcode === 200) {
          this.total = total
          this.tableData = data
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.type = ''
      this.topic = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 新增
    handleAdd () {
      this.isAddShow = true
      this.addTitle = '新增'
      this.editData = null
    },
    // 编辑
    handleEdit (val) {
      this.$api.appManagement.questionInfo(val.id).then(res => {
        if (res.errcode === 200) {
          this.editData = res.data
          this.isAddShow = true
          this.addTitle = '编辑'
        }
      })
    },
    // 编辑和新增取消处理
    handleAddCancel (val) {
      this.editData = null
      this.isAddShow = false
      if (val) {
        this.getList()
      }
    },
    // 导入
    handleImport () {
      this.isImport = true
    },
    // 导出
    handleExport () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.exportShow = true
    },
    // 删
    handleDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要删除的项')
      }
      this.$confirm('此操作将永久删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectionList.map(item => item.id).join(',')
        this.$api.appManagement.questionDelete(ids).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    // 打开详情
    openInfo (val) {
      this.$api.appManagement.questionInfo(val.id).then(res => {
        if (res.errcode === 200) {
          this.detail = res.data
          if (res.data.options) {
            this.detail.option = res.data.options.split('|')
          }
          this.isInfo = true
        }
      })
    }
  }
}
</script>
<style lang="scss">
@import "./question-bank.scss";
</style>
