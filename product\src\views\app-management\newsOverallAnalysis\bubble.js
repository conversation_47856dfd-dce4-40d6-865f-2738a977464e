// 入参说明:
// 1. data 原始气泡数据，是一个对象数组，形如[{name: '可乐', amount: 49}]
// 2. format 数组依次指定气泡中展示的名称以及影响气泡大小的数据值, 形如['name', 'amount']
// 3. dom 气泡图绘制所需要的dom id名
import echarts from 'echarts'
require('echarts/lib/chart/graph')
// 获取颜色的rgba值
const getColor = sColor => {
  sColor.toLowerCase()
  var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
  // 如果是16进制颜色
  if (sColor && reg.test(sColor)) {
    if (sColor.length === 4) {
      var sColorNew = '#'
      for (let i = 1; i < 4; i += 1) {
        sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))
      }
      sColor = sColorNew
    }
    // 处理六位的颜色值
    var sColorChange = []
    for (let i = 1; i < 7; i += 2) {
      sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)))
    }
    return 'RGBA(' + sColorChange.join(',') + ',1)'
  }
  return sColor
}

export function initBubbleChart (data = [], format = [], dom) {
  let [maxValue, temp] = [0, []]
  data.forEach(item => {
    temp.push(item[format[1]])
  })
  maxValue = Math.max.apply(null, temp)

  // 气泡颜色数组
  const color = [
    '#FFB600', '#886CFF', '#0084FF',
    '#4CB690', '#58B458', '#6C6C6C',
    '#F56161', '#FC754C', '#5F5EEC'
  ]
  // 气泡颜色备份
  let bakeColor = [...color]
  // 气泡数据
  const bubbleData = []
  // 气泡基础大小
  let basicSize = 90
  // 节点之间的斥力因子,值越大,气泡间距越大
  let repulsion = 210
  // 根据气泡数量配置基础大小和斥力因子（以实际情况进行适当调整，使气泡合理分布）
  if (data.length >= 5 && data.length < 10) {
    basicSize = 80
    repulsion = 180
  }
  if (data.length >= 10 && data.length < 20) {
    basicSize = 70
    repulsion = 150
  } else if (data.length >= 20) {
    basicSize = 60
    repulsion = 90
  }

  // 填充气泡数据数组bubbleData
  for (const item of data) {
    // 确保气泡数据条数少于或等于气泡颜色数组大小时，气泡颜色不重复
    if (!bakeColor.length) bakeColor = [...color]
    const colorSet = new Set(bakeColor)
    const curIndex = Math.round(Math.random() * (colorSet.size - 1))
    const curColor = bakeColor[curIndex]
    colorSet.delete(curColor)
    bakeColor = [...colorSet]
    // 气泡大小设置
    let size = (item[format[1]] * basicSize * 2) / maxValue
    if (size < basicSize) size = basicSize

    bubbleData.push({
      name: item[format[0]],
      value: item[format[1]],
      symbolSize: size,
      draggable: true,
      itemStyle: {
        normal: { color: `${getColor(curColor)}` }
      }
    })
  }

  const bubbleId = document.getElementById(dom)
  const bubbleChart = echarts.init(bubbleId)
  const bubbleOptions = {
    backgroundColor: 'transparent',
    animationEasingUpdate: 'bounceIn',
    series: [{
      type: 'graph',
      layout: 'force',
      force: {
        repulsion: repulsion,
        edgeLength: 10
      },
      xAxisIndex: 0,
      yAxisIndex: 0,
      // 是否开启鼠标缩放和平移漫游
      roam: false,
      label: {
        normal: {
          show: true,
          formatter: function (val) {
            return `${val.name} \n ${val.value}`
          },
          color: '#fff'
        }
      },
      data: bubbleData
    }]
  }
  bubbleChart.setOption(bubbleOptions)
  return bubbleChart
}
