// 导入封装的方法
import { post, filedownload } from '../http'

const motion = {
  // 届次编号
  circleBoutInfoList (params) {
    return post('/submission/motion/circleBoutInfo/list', params)
  },
  circleBoutInfoInfo (params) {
    return post(`/submission/motion/circleBoutInfo/info/${params}`)
  },
  // 届次年
  yearRelationList (params) {
    return post('/submission/motion/circleboutyear/list', params)
  },
  yearRelationInfo (params) {
    return post(`/submission/motion/circleboutyear/info/${params}`)
  },
  yearRelationDel (params) {
    return post('/submission/motion/circleboutyear/dels?', params)
  },
  // 提案分类
  motionTypeList (params) {
    return post('/submission/motion/topic/list?', params)
  },
  motionTypeInfo (params) {
    return post(`/submission/motion/topic/info/${params}`)
  },
  // 提案业务用户关系
  businessUser (params) {
    return post('/submission/motion/businessUser/list', params)
  },
  businessUserdels (params) {
    return post('/submission/motion/businessUser/dels', params)
  },
  enumData (params) {
    return post('/submission/motion/businessUser/enumData', params)
  },
  enumDatainfo (params) {
    return post(params)
  },
  businessUserInfo (params) {
    return post(`/submission/motion/businessUser/info/${params}`)
  },
  // 获取提案分类和主题词
  chooseList (params) {
    return post('/submission/motion/topic/chooseList', params)
  },
  // 获取当前届次
  currentCircleAndBout (params) {
    return post('/submission/motion/circleBoutInfo/currentCircleAndBout', params)
  },
  // 流程配置列表
  flowconfigList (params) {
    return post('/flowconfig/motion/list', params)
  },
  // 流程配置详情
  flowconfigInfo (params) {
    return post(`/flowconfig/motion/info/${params}`)
  },
  // 流程配置删除
  flowconfigDel (params) {
    return post(`/flowconfig/motion/del/${params}`)
  },
  // 流程配置获取
  getConfigMap (params) {
    return post('/flowconfig/motion/getConfigMap', params)
  },

  // 所有提案列表
  motionList (params) {
    return post('/motion/list', params)
  },
  // 提案委删除提案
  powerDelete (params) {
    return post('/motion/powerDelete', params)
  },
  // 提案基础详情
  motionInfo (params) {
    return post(`/motion/info/${params}`)
  },
  // 我领衔的提案
  myMotionList (params) {
    return post('/motion/myMotionList', params)
  },
  // 我联名的提案
  myJoinMotionList (params) {
    return post('/motion/myJoinMotionList', params)
  },
  // 我的草稿提案
  myDraftsMotionList (params) {
    return post('/motion/myDraftsMotionList', params)
  },
  // 委员删除提案
  representDelete (params) {
    return post('/motion/representDelete', params)
  },
  // 解锁审查提案
  unlockMotion (params) {
    return post('/motion/unlockMotion', params)
  },
  // 获取提案审查详情
  auditDetail (params) {
    return post('/motion/auditDetail', params)
  },
  // 待审查提案
  examineList (params) {
    return post('/motion/examineList', params)
  },
  // 交办详情
  assignDetail (params) {
    return post('/motion/assignDetail', params)
  },
  // 人大交办中
  RDAssignList (params) {
    return post('/motion/RDAssignList', params)
  },
  // 提案批量二次交办
  batchSecondAssign (params) {
    return post('/motion/batchSecondAssign', params)
  },
  // 提案批量交办办理单位
  batchAssignMotion (params) {
    return post('/motion/batchAssignMotion', params)
  },
  // 提案委办理中提案
  allTransactList (params) {
    return post('/motion/allTransactList', params)
  },
  // 办理中提案详情
  transactMotionDetail (params) {
    return post('/motion/transactMotionDetail', params)
  },
  // 办理单位办理中提案
  groupTransactList (params) {
    return post('/motion/groupTransactList', params)
  },
  // 办理单位更新内部流程
  updateTransactInnerStatus (params) {
    return post('/motion/updateTransactInnerStatus', params)
  },
  // 办理单位沟通情况列表
  flowContactList (params) {
    return post('/motion/flowContactList', params)
  },
  // 办理单位新增沟通情况获取沟通联系人
  contactPersonList (params) {
    return post('/motion/contactPersonList', params)
  },
  // 提案委删除办理单位沟通交流
  superDeleteFlowContact (params) {
    return post('/motion/superDeleteFlowContact', params)
  },
  // 办理单位办理中提案申请延期
  saveFlowDelay (params) {
    return post('/motion/saveFlowDelay', params)
  },
  // 办理单位申请调整办理单位
  exchangeTransact (params) {
    return post('/motion/exchangeTransact', params)
  },
  // 通过办理记录id获取答复件详情
  flowAnswerDetail (params) {
    return post('/motion/flowAnswerDetail', params)
  },
  // 办理单位保存答复件
  saveFlowAnswer (params) {
    return post('/motion/saveFlowAnswer', params)
  },
  // 根据答复件id获取答复件详情
  motionAnswerDetail (params) {
    return post('/motion/motionAnswerDetail', params)
  },
  // 提案委已答复列表
  allAnsweredMotionList (params) {
    return post('/motion/allAnsweredMotionList', params)
  },
  // 办理单位已答复列表
  groupAnsweredMotionList (params) {
    return post('/motion/groupAnsweredMotionList', params)
  },
  // 提案重新办理
  reTransact (params) {
    return post('/motion/reTransact', params)
  },
  // 提案办结
  finishMotion (params) {
    return post('/motion/finishMotion', params)
  },
  // 提案委批量办结提案
  batchFinishMotion (params) {
    return post('/motion/batchFinishMotion', params)
  },
  // 提案委已办结提案列表
  allFinishMotionList (params) {
    return post('/motion/allFinishMotionList', params)
  },
  // 办理单位已办结提案列表
  groupFinishMotionList (params) {
    return post('/motion/groupFinishMotionList', params)
  },
  // 满意度测评详情
  flowEvaluateDetail (params) {
    return post('/motion/flowEvaluateDetail', params)
  },
  // 保存满意度测评
  saveFlowEvaluate (params) {
    return post('/motion/saveFlowEvaluate', params)
  },
  // 办理中历史申请调整
  groupChangeList (params) {
    return post('/motion/groupChangeList', params)
  },
  // 提案委跟踪办理列表
  allTrackMotionList (params) {
    return post('/motion/allTrackMotionList', params)
  },
  // 提案委申请延期列表
  flowDelayList (params) {
    return post('/motion/flowDelayList', params)
  },
  // 提案委申请调整办理单位列表
  flowBackList (params) {
    return post('/motion/flowBackList', params)
  },
  // 申请调整办理单位审查
  auditExchangeTransact (params) {
    return post('/motion/auditExchangeTransact', params)
  },
  // 申请延期审查
  flowDelayAudit (params) {
    return post('/motion/flowDelayAudit', params)
  },
  // 办理单位申请跟踪办理
  requestTractAnswer (params) {
    return post('/motion/requestTractAnswer', params)
  },
  // 办理单位跟踪办理列表
  groupTrackMotionList (params) {
    return post('/motion/groupTrackMotionList', params)
  },
  // 提案委审查跟踪办理
  auditRequestTractAnswer (params) {
    return post('/motion/auditRequestTractAnswer', params)
  },
  // 管理员/提案委 真实删除联名关系
  realDeleteJoinUser (params) {
    return post('/motion/realDeleteJoinUser', params)
  },
  // 管理员/提案委 添加联名关系
  addAgreeJoinUser (params) {
    return post('/motion/addAgreeJoinUser', params)
  },
  // 管理员修改办理单位
  superEditTransactGroup (params) {
    return post('/motion/superEditTransactGroup', params)
  },
  // 设置/取消 重点提案/公开提案/优秀提案
  batchUpdateMotion (params) {
    return post('/motion/batchUpdateMotion', params)
  },
  // 超管获取办理单位列表
  flowTransactList (params) {
    return post('/motion/flowTransact/list', params)
  },
  // 超管获取办理单位列表详情
  flowTransactInfo (params) {
    return post(`/motion/flowTransact/info/${params}`)
  },
  // 超管删除办理单位
  flowTransactDel (params) {
    return post(`/motion/flowTransact/del/${params}`)
  },
  // 超管获取答复件列表
  answerList (params) {
    return post('/motion/answerList', params)
  },
  // 可添加办理答复件的办理单位
  answerTransactList (params) {
    return post('/motion/answerTransactList', params)
  },
  // 超管保存和编辑答复件
  superSaveFlowAnswer (params) {
    return post('/motion/superSaveFlowAnswer', params)
  },
  // 超管删除答复件
  superDeleteFlowAnswer (params) {
    return post('/motion/superDeleteFlowAnswer', params)
  },
  // 可添加跟踪办理答复件的办理单位
  tractAnswerTransactList (params) {
    return post('/motion/tractAnswerTransactList', params)
  },
  // 提案word批量导出数据
  wordExport (params) {
    return post('/motion/wordExport', params)
  },
  // 根据条件查询ids
  findQueryIds (params) {
    return post('/motion/findQueryIds', params)
  },
  // 导出word的答复件
  exportReplyFile (params) {
    return filedownload('/motion/exportFlowAnswerByMotion?', params, 'arraybuffer')
  },
  // 统计分析
  motionCount (params) {
    return post('/motion/motionCount', params)
  },
  // 办理中催办查看
  batchUrgeView (params) {
    return post('/motion/batchUrgeView', params)
  },
  // 办理中催办答复
  batchUrgeAnswer (params) {
    return post('/motion/batchUrgeAnswer', params)
  },
  // 催办满意度测评(已答复建议催代表)
  batchUrgeEvaluate (params) {
    return post('/motion/batchUrgeEvaluate', params)
  }
}
export default motion
