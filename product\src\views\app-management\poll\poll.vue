<template>
  <div class="poll">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input v-model="keyword"
                placeholder="请输入内容"></el-input>
      <el-date-picker v-model="time"
                      class="ml20"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd HH:mm:ss">
      </el-date-picker>
      <el-select v-model="release"
                 placeholder="是否发布">
        <el-option v-for="item in releaseList"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="handleAdd">新增</el-button>
      <el-button type="primary"
                 @click="handleExport">导出</el-button>
      <el-button type="primary"
                 @click="handleBatchDelete">删除</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="主题"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="openDetail(scope.row)">{{scope.row.name}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="投票时间"
                           width="400">
            <template slot-scope="scope">{{scope.row.startTime}}至{{scope.row.endTime}}</template>
          </el-table-column>
          <el-table-column label="发起人"
                           prop="createName"
                           min-width="120"></el-table-column>
          <!-- <el-table-column label="参与人数" width="120">
            <template slot-scope="scope">
              <el-button type="text" @click="handleJoinMan(scope.row)">{{scope.row.participationNum}}</el-button>
            </template>
          </el-table-column> -->
          <el-table-column label="参与人数"
                           width="120"
                           prop="participationNum"></el-table-column>
          <el-table-column label="类型"
                           width="160"
                           prop="isRelease">
            <template slot-scope="scope">{{scope.row.type === 1 ? '单选':'多选'}}</template>
          </el-table-column>
          <el-table-column label="是否发布"
                           width="160"
                           prop="isRelease">
            <template slot-scope="scope">{{scope.row.isRelease === 1 ? '是':'否'}}</template>
          </el-table-column>
          <el-table-column label="状态"
                           prop="status"
                           width="160"></el-table-column>
          <el-table-column label="操作"
                           min-width="520">
            <template slot-scope="scope">
              <el-button @click="handlePublish(scope.row)"
                         v-if="scope.row.isRelease === 0"
                         type="primary"
                         plain
                         size="mini">发布</el-button>
              <el-button @click="handlePublishCancel(scope.row)"
                         v-if="scope.row.isRelease === 1"
                         type="primary"
                         plain
                         size="mini">取消发布</el-button>
              <el-button @click="handleEdit(scope.row.id)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
              <el-button @click="handleDelete(scope.row.id)"
                         type="primary"
                         plain
                         size="mini">删除</el-button>
              <el-button @click="handleQuestion(scope.row)"
                         type="primary"
                         plain
                         size="mini">选项</el-button>
              <el-button @click="handleCount(scope.row)"
                         type="primary"
                         plain
                         size="mini">统计详情</el-button>
              <el-button @click="handlePreview(scope.row)"
                         type="primary"
                         plain
                         size="mini">预览</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="isAdd"
               :title="addTitle">
      <poll-Add :id="id"
                @callback="handleCallBack"></poll-Add>
    </zy-pop-up>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :excelId="excelId"
                 :type="201"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
    <qr-Code v-if="isQrcode"
             @cancel="isQrcode = false"
             :id="id"></qr-Code>
  </div>
</template>

<script>
import table from '@/mixins/table.js'
import pollAdd from './widget/poll-add.vue'
import qrCode from './widget/qr-code'
export default {
  components: { pollAdd, qrCode },
  mixins: [table],
  inject: ['newTab'],
  data () {
    return {
      keyword: '',
      time: [],
      release: '',
      releaseList: [
        { label: '已发布', value: '1' },
        { label: '未发布', value: '0' }
      ],
      isAdd: false,
      addTitle: '新增',
      id: null,
      exportShow: false,
      excelId: null,
      isQrcode: false
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      const params = {
        pageNo: this.page,
        pageSize: this.pageSize
      }
      if (this.keyword !== '') {
        params.keyword = this.keyword
      }
      if (this.time.length > 0) {
        params.startTime = this.time[0]
        params.endTime = this.time[1]
      }
      if (this.release !== '') {
        params.isRelease = this.release
      }
      this.$api.appManagement.pollList(params).then(res => {
        const { errcode, total, data } = res
        if (errcode === 200) {
          this.total = total
          this.list = data
        }
      })
    },
    search () {
      if (this.keyword === '' && this.time.length === 0 && this.release === '') {
        return this.$message.warning('请输入或选择想要的搜索')
      }
      this.page = 1
      this.getList()
    },
    reset () {
      if (this.keyword) this.keyword = ''
      if (this.time.length > 0) this.time = []
      if (this.release) this.release = ''
      this.getList()
    },
    handleAdd () {
      this.id = null
      this.addTitle = '新增投票'
      this.isAdd = true
    },
    handleEdit (id) {
      this.id = id
      this.addTitle = '编辑投票'
      this.isAdd = true
    },
    handleExport () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.exportShow = true
    },
    handleDelete (ids) {
      this.$confirm('此操作将删除选中的投票, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.pollDels(ids).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除投票成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    handleBatchDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要删除的项')
      }
      const ids = this.selectionList.map(item => item.id).join(',')
      this.handleDelete(ids)
    },
    /**
     * TDD 发布 或者取消发布 调用编辑接口 导致选择用户数据丢失
     */
    handlePublish (val) {
      this.$confirm('此操作将发布此投票, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          ids: val.id,
          isRelease: 1
        }
        this.$api.appManagement.pollRelease(data).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('发布成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    handlePublishCancel (val) {
      this.$confirm('此操作将取消发布此投票, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          ids: val.id,
          isRelease: 0
        }
        this.$api.appManagement.pollRelease(data).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('取消发布成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    handleQuestion (val) {
      this.newTab({ name: '投票选项', menuId: val.id, to: '/poll-options', params: { id: val.id } })
    },
    handleCount (val) {
      this.newTab({ name: '投票统计', menuId: val.id, to: '/poll-count', params: { id: val.id } })
    },
    handlePreview (val) {
      this.id = val.id
      this.isQrcode = true
    },
    handleJoinMan (val) {
      this.newTab({ name: '投票参与人', menuId: val.id, to: '/poll-man', params: { id: val.id } })
    },
    openDetail (val) {
      this.newTab({ name: '投票详情', menuId: val.id, to: '/poll-info', params: { id: val.id } })
    },
    handleCallBack () {
      this.getList()
      this.isAdd = false
    }
  }
}
</script>

<style lang="scss" scoped>
.poll {
  width: 100%;
  height: 100%;
  .ml20 {
    margin-left: 20px;
  }
  .tableData {
    height: calc(100% - 180px);
    width: 100%;
  }
}
</style>
