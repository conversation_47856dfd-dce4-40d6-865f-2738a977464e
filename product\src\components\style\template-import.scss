.xyl-template-import {
  width: 666px;
  background-color: #fff;
  margin: auto;
  padding: 52px;
  .xyl-template-import-text {
    height: 52px;
    .el-button {
      margin-left: 22px;
      padding: 6px 22px;
    }
  }
  .xyl-template-import-upload {
    .el-upload {
      margin-left: 66px;
    }
  }
  .TransitionText {
    line-height: 36px;
  }
  .xyl-template-import-success {
    padding-top: 28px;
    .el-button {
      margin-left: 22px;
      padding: 6px 22px;
    }
    i {
      color: green;
      font-size: 20px;
      margin-right: 6px;
      transform: translateY(2px);
    }
  }
  .xyl-template-import-error {
    padding-top: 28px;
    padding-left: 66px;
    line-height: 22px;
    i {
      color: red;
      font-size: 20px;
      margin-right: 6px;
      transform: translateY(2px);
    }
  }
}
