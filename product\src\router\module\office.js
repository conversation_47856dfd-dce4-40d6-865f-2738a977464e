const office = [
  {
    path: '/officeSendFileManage',
    name: 'officeSendFileManage',
    component: () => import(/** 发文审批-发文管理 */ '@/views/office/File-center/send-file-approval/send-file-manage')
  },
  {
    path: '/officeSendFileManage-initiation',
    name: 'officeSendFileManage-initiation',
    component: () => import(/** 发文审批-我发起的 */ '@/views/office/File-center/send-file-approval/my-initiation')
  },
  {
    path: '/officeSendFileManage-draft',
    name: 'officeSendFileManage-draft',
    component: () => import(/** 发文审批-草稿箱 */ '@/views/office/File-center/send-file-approval/my-apply-draft')
  },
  {
    path: '/officeSendFileManage-approval',
    name: 'officeSendFileManage-approval',
    component: () => import(/** 发文审批-我审核的 */ '@/views/office/File-center/send-file-approval/my-approval')
  },
  {
    path: '/officePublishInfoManage',
    name: 'officePublishInfoManage',
    component: () => import(/** 信息发布审批-信息发布管理 */ '@/views/office/File-center/info-publish-approval/info-publish-manage')
  },
  {
    path: '/officePublishInfoManage-initiation',
    name: 'officePublishInfoManage-initiation',
    component: () => import(/** 信息发布审批-我发起的 */ '@/views/office/File-center/info-publish-approval/my-initiation')
  },
  {
    path: '/officePublishInfoManage-approval',
    name: 'officePublishInfoManage-approval',
    component: () => import(/** 信息发布审批-我审核的 */ '@/views/office/File-center/info-publish-approval/my-approval')
  },
  {
    path: '/officePublishInfoManage-draft',
    name: 'officePublishInfoManage-draft',
    component: () => import(/** 信息发布审批-草稿箱 */ '@/views/office/File-center/info-publish-approval/my-draft')
  },
  {
    path: '/officePublishInfoManage-column',
    name: 'officePublishInfoManage-column',
    component: () => import(/** 信息发布平台栏目 */ '@/views/office/File-center/info-publish-column/index')
  },
  {
    path: '/officeIncomingCallManage',
    name: 'officeIncomingCallManage',
    component: () => import(/** 来文来电审批-来文来电管理 */ '@/views/office/File-center/incoming-call/incoming-call-manage')
  },
  {
    path: '/officeIncomingCallManage-initiation',
    name: 'officeIncomingCallManage-initiation',
    component: () => import(/** 来文来电审批-我发起的 */ '@/views/office/File-center/incoming-call/my-initiation')
  },
  {
    path: '/officeIncomingCallManage-draft',
    name: 'officeIncomingCallManage-draft',
    component: () => import(/** 来文来电审批-草稿箱 */ '@/views/office/File-center/incoming-call/my-draft')
  },
  {
    path: '/officeIncomingCallManage-approval',
    name: 'officeIncomingCallManage-approval',
    component: () => import(/** 来文来电审批-我审核的 */ '@/views/office/File-center/incoming-call/my-approval')
  },
  {
    path: '/officeInternalRequest-apply',
    name: 'officeInternalRequest-apply',
    component: () => import(/** 内部请示审批-我申请的 */ '@/views/office/File-center/internal-request/my-apply')
  },
  {
    path: '/officeInternalRequest-draft',
    name: 'officeInternalRequest-draft',
    component: () => import(/** 内部请示审批-草稿箱 */ '@/views/office/File-center/internal-request/my-draft')
  },
  {
    path: '/officeInternalRequest-approval',
    name: 'officeInternalRequest-approval',
    component: () => import(/** 内部请示审批-我审核的 */ '@/views/office/File-center/internal-request/my-approval')
  },
  {
    path: '/officeInternalRequest-manage',
    name: 'officeInternalRequest-manage',
    component: () => import(/** 内部请示审批- 管理 */ '@/views/office/File-center/internal-request/manage')
  },

  {
    path: '/jghomePage',
    name: 'jghomePage',
    component: () => import(/* 机关办公首页 */'@/views/main/office-home/home.vue')
  },
  {
    path: '/jgConfigProcess',
    name: 'jgConfigProcess',
    component: () => import(/* 综合管理流程配置 */'@/views/office/system-setting/config-process.vue')
  },
  {
    path: '/oaUserSignature',
    name: 'oaUserSignature',
    component: () => import(/* 综合管理流程配置 */'@/views/office/system-setting/user-signature.vue')
  },
  {
    path: '/oaMyUserSignature',
    name: 'oaMyUserSignature',
    component: () => import(/* 综合管理流程配置 */'@/views/office/system-setting/my-user-signature.vue')
  },
  {
    path: '/jgConfigProcess-operate',
    name: 'jgConfigProcess-operate',
    component: () => import(/* 综合管理流程配置 节点操作 */'@/views/office/system-setting/config-process-operate.vue')
  },
  {
    path: '/jgConfig-schedule',
    name: 'jgConfig-schedule',
    component: () => import(/* 综合管理流程配置 日程提醒 */'@/views/office/system-setting/config-schedule.vue')
  },
  {
    path: '/jgConfig-home-module',
    name: 'jgConfig-home-module',
    component: () => import(/* 综合管理流程配置 待办模块配置 */'@/views/office/system-setting/config-home-module.vue')
  },
  {
    path: '/oaTransactionManagement-apply-car',
    name: 'oaTransactionManagement-apply-car',
    component: () => import(/* 事务管理 用车申请 */'@/views/office/Transaction-management/use-car/apply-car.vue')
  },
  {
    path: '/oaTransactionManagement-apply-car-add',
    name: 'oaTransactionManagement-apply-car-add',
    component: () => import(/* 事务管理 用车申请 新增 */'@/views/office/Transaction-management/use-car/apply-car-add/apply-car-add.vue')
  },
  {
    path: '/oaTransactionManagement-apply-car-audit',
    name: 'oaTransactionManagement-apply-car-audit',
    component: () => import(/* 事务管理 用车申请 审批 */'@/views/office/Transaction-management/use-car/apply-car-add/apply-car-audit.vue')
  },
  {
    path: '/oaTransactionManagement-apply-car-goOut',
    name: 'oaTransactionManagement-apply-car-goOut',
    component: () => import(/* 事务管理 用车申请 审批 */'@/views/office/Transaction-management/use-car/apply-car-add/apply-car-get-out.vue')
  },
  {
    path: '/oaTransactionManagement-approval-car',
    name: 'oaTransactionManagement-approval-car',
    component: () => import(/* 事务管理 用车审批 */'@/views/office/Transaction-management/use-car/approval-car.vue')
  },
  {
    path: '/oaTransactionManagement-deploy-car',
    name: 'oaTransactionManagement-deploy-car',
    component: () => import(/* 事务管理 车辆调配 */'@/views/office/Transaction-management/use-car/deploy-car.vue')
  },
  {
    path: '/oaTransactionManagement-drive',
    name: 'oaTransactionManagement-drive',
    component: () => import(/* 事务管理 车辆出车 */'@/views/office/Transaction-management/use-car/drive.vue')
  },
  {
    path: '/oaTransactionManagement-manage-car',
    name: 'oaTransactionManagement-manage-car',
    component: () => import(/* 事务管理 用车管理 */'@/views/office/Transaction-management/use-car/manage-car.vue')
  },
  {
    path: '/oaTransactionManagement-apply-car-draft',
    name: 'oaTransactionManagement-apply-car-draft',
    component: () => import(/* 事务管理 用车申请 草稿箱 */'@/views/office/Transaction-management/use-car/apply-car-draft.vue')
  },
  {
    path: '/oaTransactionManagement-useCar-manage',
    name: 'oaTransactionManagement-useCar-manage',
    component: () => import(/* 事务管理 用车申请管理 */'@/views/office/Transaction-management/use-car/apply-car-manage.vue')
  },
  {
    path: '/oaTransactionManagement-manage-driver',
    name: 'oaTransactionManagement-manage-driver',
    component: () => import(/* 事务管理 驾驶员管理 */'@/views/office/Transaction-management/use-car/manage-driver.vue')
  },
  {
    path: '/oaTransactionManagement-goOut-apply',
    name: 'oaTransactionManagement-goOut-apply',
    component: () => import(/* 事务管理 外出审批 我申请的 */'@/views/office/Transaction-management/go-out/my-apply.vue')
  },
  {
    path: '/oaTransactionManagement-goOut-draft',
    name: 'oaTransactionManagement-goOut-draft',
    component: () => import(/* 事务管理 外出审批 草稿箱 */'@/views/office/Transaction-management/go-out/my-draft.vue')
  },
  {
    path: '/oaTransactionManagement-goOut-approval',
    name: 'oaTransactionManagement-goOut-approval',
    component: () => import(/* 事务管理 外出审批 我审核的 */'@/views/office/Transaction-management/go-out/my-approval.vue')
  },
  {
    path: '/oaTransactionManagement-goOut-manage',
    name: 'oaTransactionManagement-goOut-manage',
    component: () => import(/* 事务管理 外出审批 管理员 */'@/views/office/Transaction-management/go-out/manage.vue')
  },
  {
    path: '/oaTransactionManagement-Reception-apply',
    name: 'oaTransactionManagement-Reception-apply',
    component: () => import(/* 事务管理 接待 我申请的 */'@/views/office/Transaction-management/Reception/my-apply.vue')
  },
  {
    path: '/oaTransactionManagement-Reception-draft',
    name: 'oaTransactionManagement-Reception-draft',
    component: () => import(/* 事务管理 接待 草稿箱 */'@/views/office/Transaction-management/Reception/my-draft.vue')
  },
  {
    path: '/oaTransactionManagement-Reception-approval',
    name: 'oaTransactionManagement-Reception-approval',
    component: () => import(/* 事务管理 接待 我审核的 */'@/views/office/Transaction-management/Reception/my-approval.vue')
  },
  {
    path: '/oaTransactionManagement-Reception-manage',
    name: 'oaTransactionManagement-Reception-manage',
    component: () => import(/* 事务管理 接待 管理员 */'@/views/office/Transaction-management/Reception/manage.vue')
  },
  {
    path: '/oaTransactionManagement-Repair-apply',
    name: 'oaTransactionManagement-Repair-apply',
    component: () => import(/* 事务管理 维修 我申请的 */'@/views/office/Transaction-management/repair/my-apply.vue')
  },
  {
    path: '/oaTransactionManagement-Repair-draft',
    name: 'oaTransactionManagement-Repair-draft',
    component: () => import(/* 事务管理 维修 草稿箱 */'@/views/office/Transaction-management/repair/my-draft.vue')
  },
  {
    path: '/oaTransactionManagement-Repair-approval',
    name: 'oaTransactionManagement-Repair-approval',
    component: () => import(/* 事务管理 维修 我审核的 */'@/views/office/Transaction-management/repair/my-approval.vue')
  },
  {
    path: '/oaTransactionManagement-Repair-manage',
    name: 'oaTransactionManagement-Repair-manage',
    component: () => import(/* 事务管理 维修 管理员 */'@/views/office/Transaction-management/repair/manage.vue')
  },
  {
    path: '/oaKnowWell-Memorabilia',
    name: 'oaKnowWell-Memorabilia',
    component: () => import(/* 知情明事 大事记 */'@/views/office/know-well/Memorabilia.vue')
  },
  {
    path: '/oaKnowWell-Learn-a-day',
    name: 'oaKnowWell-Learn-a-day',
    component: () => import(/* 知情明事 每日一学 */'@/views/office/know-well/learn-a-day.vue')
  },
  {
    path: '/oaKnowWell-Learn-a-day-column',
    name: 'oaKnowWell-Learn-a-day-column',
    component: () => import(/* 知情明事 每日一学 栏目 */'@/views/office/know-well/learn-a-day-column.vue')
  },
  {
    path: '/oaKnowWell-document',
    name: 'oaKnowWell-document',
    component: () => import(/* 知情明事 文件资料 */'@/views/office/know-well/document.vue')
  },
  {
    path: '/oaKnowWell-document-column',
    name: 'oaKnowWell-document-column',
    component: () => import(/* 知情明事 文件资料 栏目 */'@/views/office/know-well/document-column.vue')
  },
  {
    path: '/oaAttendance',
    name: 'oaAttendance',
    component: () => import(/* 值班 */'@/views/office/attendance/attendance.vue')
  },
  {
    path: '/oaAttendance-log',
    name: 'oaAttendance-log',
    component: () => import(/* 值班 日志 */'@/views/office/attendance/attendance-log.vue')
  },
  {
    path: '/oaWorkReport-workLog',
    name: 'oaWorkReport-workLog',
    component: () => import(/* 工作汇报 工作日志 */'@/views/office/work-report/work-log.vue')
  },
  {
    path: '/oaWorkReport-monthWork',
    name: 'oaWorkReport-monthWork',
    component: () => import(/* 工作汇报 每月工作 */'@/views/office/work-report/month-work.vue')
  },
  {
    path: '/oaWorkReport-monthWorkPlan',
    name: 'oaWorkReport-monthWorkPlan',
    component: () => import(/* 工作汇报 每月工作 工作计划 */'@/views/office/work-report/month-work-plan.vue')
  },
  {
    path: '/oaWorkReport-monthWorkSummary',
    name: 'oaWorkReport-monthWorkSummary',
    component: () => import(/* 工作汇报 每月工作 工作总结 */'@/views/office/work-report/month-work-summary.vue')
  },
  {
    path: '/oaCivilizationalCreation-main-object',
    name: 'oaCivilizationalCreation-main-object',
    component: () => import(/* 文件创建 考核主体对象 */'@/views/office/civilizational-creation/main-object.vue')
  },
  {
    path: '/oaCivilizationalCreation-type-of-indicator',
    name: 'oaCivilizationalCreation-type-of-indicator',
    component: () => import(/* 文件创建 考核指标类型 */'@/views/office/civilizational-creation/type-of-indicator.vue')
  },
  {
    path: '/oaCivilizationalCreation-objective-tasks',
    name: 'oaCivilizationalCreation-objective-tasks',
    component: () => import(/* 文件创建 考核目标任务 */'@/views/office/civilizational-creation/objective-tasks.vue')
  },
  {
    path: '/oaCivilizationalCreation-result',
    name: 'oaCivilizationalCreation-result',
    component: () => import(/* 文件创建 考核结果 */'@/views/office/civilizational-creation/result.vue')
  },
  {
    path: '/oaClaimOfGoods-type',
    name: 'oaClaimOfGoods-type',
    component: () => import(/* 办公用品 物品分类 */'@/views/office/Claim-of-goods/Type-of-goods.vue')
  },
  {
    path: '/oaClaimOfGoods-manage',
    name: 'oaClaimOfGoods-manage',
    component: () => import(/* 办公用品 物品管理 */'@/views/office/Claim-of-goods/manage-goods.vue')
  },
  {
    path: '/oaClaimOfGoods-apply',
    name: 'oaClaimOfGoods-apply',
    component: () => import(/* 办公用品 物品申领-申请列表 */'@/views/office/Claim-of-goods/apply-goods.vue')
  },
  {
    path: '/oaClaimOfGoods-my-apply',
    name: 'oaClaimOfGoods-my-apply',
    component: () => import(/* 办公用品 物品申领-我的申请列表 */'@/views/office/Claim-of-goods/my-apply-goods.vue')
  },
  {
    path: '/oaClaimOfGoods-apply-goods-add',
    name: 'oaClaimOfGoods-apply-goods-add',
    component: () => import(/* 办公用品 物品申领-新增物品申领 */'@/views/office/Claim-of-goods/components/apply-goods-add.vue')
  },
  {
    path: '/oaClaimOfGoods-my-apply-draft',
    name: 'oaClaimOfGoods-my-apply-draft',
    component: () => import(/* 办公用品 物品申领-我的申请列表草稿 */'@/views/office/Claim-of-goods/my-apply-goods-draft.vue')
  },
  {
    path: '/oaClaimOfGoods-approval-apply',
    name: 'oaClaimOfGoods-approval-apply',
    component: () => import(/* 办公用品 物品申领-我审批的 */'@/views/office/Claim-of-goods/approval-apply-goods.vue')
  },
  {
    path: '/oaClaimOfGoods-apply-goods-audit',
    name: 'oaClaimOfGoods-apply-goods-audit',
    component: () => import(/* 办公用品 物品申领-物品申领审批 */'@/views/office/Claim-of-goods/components/apply-goods-audit.vue')
  },
  {
    path: '/oaReimbursement-manage',
    name: 'oaReimbursement-manage',
    component: () => import(/* 报账审批 报账管理 */'@/views/office/Reimbursement/manage.vue')
  },
  {
    path: '/oaReimbursement-apply',
    name: 'oaReimbursement-apply',
    component: () => import(/* 报账审批 我申请的 */'@/views/office/Reimbursement/my-apply.vue')
  },
  {
    path: '/oaReimbursement-draft',
    name: 'oaReimbursement-draft',
    component: () => import(/* 报账审批 我的草稿箱 */'@/views/office/Reimbursement/my-draft.vue')
  },
  {
    path: '/oaReimbursement-approval',
    name: 'oaReimbursement-approval',
    component: () => import(/* 报账审批 我审批的 */'@/views/office/Reimbursement/my-approval.vue')
  },
  {
    path: '/video-conference',
    name: 'video-conference',
    component: () => import(/* 视频会议 */'@/views/office/video-conference/video-conference.vue')
  },
  {
    path: '/huawei-account',
    name: 'huawei-account',
    component: () => import(/* 华为云账号 */'@/views/office/video-conference/huawei-account.vue')
  },
  {
    path: '/documentCorrection',
    name: 'documentCorrection',
    component: () => import(/* 公文校正 */'@/views/office/documentCorrection/documentCorrection.vue')
  }

]

export default office
