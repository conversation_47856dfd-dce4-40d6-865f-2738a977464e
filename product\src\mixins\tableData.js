export default {
  data () {
    return {
      value: 'id',
      choose: [],
      selectObj: [],
      selectObjData: []
    }
  },
  methods: {
    selected (selection, row) {
      var arr = this.choose
      var obj = this.selectObjData
      if (this.selectObj[row[this.value]]) {
        arr.forEach((item, index) => {
          if (item === row[this.value]) {
            arr.splice(index, 1)
          }
        })
        obj.forEach((item, index) => {
          if (item[this.value] === row[this.value]) {
            obj.splice(index, 1)
          }
        })
        delete this.selectObj[row[this.value]]
      } else {
        this.selectObj[row[this.value]] = row[this.value]
        obj.push(row)
        arr.push(row[this.value])
        this.selectObjData = obj
        this.choose = arr
      }
    },
    selectedAll (selection) {
      var arr = this.choose
      var obj = this.selectObjData
      this.tableData.forEach((row, index) => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, row[this.value])) {
          if (selection.length === 0) {
            arr.forEach((item, index) => {
              if (item === row[this.value]) {
                arr.splice(index, 1)
              }
            })
            obj.forEach((item, index) => {
              if (item[this.value] === row[this.value]) {
                obj.splice(index, 1)
              }
            })
          }
          selection.length ? null : delete this.selectObj[row[this.value]]; // eslint-disable-line
        } else {
          this.selectObj[row[this.value]] = row[this.value]
          obj.push(row)
          arr.push(row[this.value])
          this.selectObjData = obj
          this.choose = arr
        }
      })
    },
    memoryChecked () {
      if (this.$refs.table) {
        this.tableData.forEach((row, index) => {
          if (Object.prototype.hasOwnProperty.call(this.selectObj, row[this.value])) {
            this.$refs.table.toggleRowSelection(row, true)
            if (this.selectObj[row[this.value]]) {
            } else {
              this.selectObj[row[this.value]] = row[this.value]
              var arr = this.choose
              var obj = this.selectObjData
              obj.push(row)
              arr.push(row[this.value])
              this.selectObjData = obj
              this.choose = arr
            }
          } else {
            this.$refs.table.toggleRowSelection(row, false)
          }
        })
      }
    }
  }
}
