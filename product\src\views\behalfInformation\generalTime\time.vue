<template>
  <div class="forthetime">
    <generalTime :memberType="memberType"
                 has="auth:historycircles:"></generalTime>
  </div>
</template>
<script>
import generalTime from './generalTime'
export default {
  name: 'timeT',
  components: {
    generalTime
  },
  data () {
    return {
      memberType: this.$route.query.memberType || 3
    }
  }
}
</script>
<style lang="scss">
.forthetime {
  width: 100%;
  height: 100%;
}
</style>
