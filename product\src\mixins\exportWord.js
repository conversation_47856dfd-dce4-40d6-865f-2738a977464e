
import JSZipUtils from 'jszip-utils'
import docxtemplater from 'docxtemplater'
import { saveAs } from 'file-saver'
import J<PERSON><PERSON><PERSON> from 'jszip'
export default {
  methods: {
    exportWord (url, docName, data) {
      JSZipUtils.getBinaryContent(url, (error, content) => {
        if (error) {
          throw error
        }
        const zip = new JSZip(content)
        let doc = new docxtemplater().loadZip(zip) // eslint-disable-line
        doc.setData({
          ...data
        })
        try {
          doc.render()
        } catch (error) {
          const e = {
            message: error.message,
            name: error.name,
            stack: error.stack,
            properties: error.properties
          }
          console.log(JSON.stringify({ error: e }))
          throw error
        }
        const out = doc.getZip().generate({ type: 'blob' })
        saveAs(out, `${docName}.docx`)
      })
    }
  }
}
