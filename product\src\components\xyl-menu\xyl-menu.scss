.xyl-menu {
  width: 180px;
  height: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden !important;
  }

  .is-vertical {
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.4);
    }
  }

  .el-menu {
    width: 180px;
    border: 0;
    background-color: #282C33;

    .is-opened {
      .el-submenu__title {
        font-size: 16px;
        font-weight: 600;
        color: #fff;
      }
      .menu-color {
        font-weight: 600;
        color: #fff;
      }
      .el-menu--inline {
       background: #626872;
      }
      i {
        color: rgba($color:#fff, $alpha: .8);
      }
    }

    .el-menu-item {
      width: 100%;
      font-size: 16px;
      min-height: 46px;
      height: auto;
      line-height: normal;
      color: rgba($color:#fff, $alpha: .8);
      position: relative;
      border-left: 2px solid transparent;
      padding-top: 12px;
      padding-bottom: 12px;
      padding-right: 20px;
      min-width: 180px;
      span {
        display: inline-block;
        width: calc(100%);
        line-height: 24px;
        text-overflow: clip !important;
        white-space: normal !important;
      }
      &:hover{
        background: #54525F;
      }
      &.is-active {
        font-weight: 600;
        background: rgba($color:#9F9EA7, $alpha: .6);
        border-color: #fff;
        

        .menu-color {
          font-weight: 600;
          color: #fff;
        }
      }
    }

    .el-submenu__title {
      font-size: 16px;
      color: #fff;
      border-left: 2px solid transparent;
      padding-left: 16px !important;
      min-height: 46px;
      height: auto;
      line-height: normal;
      padding-top: 12px;
      padding-bottom: 12px;
      .menu-color {
        display: inline-block;
        width: calc(100% - 22px);
        line-height: 24px;
        text-overflow: clip !important;
        white-space: normal !important;
      }
      i {
        color: #fff;
      }
      &:hover {
        background: #54525F;
      }
    }

    .menu-color {
      color: #fff;
    }

    .xyl-menu-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      vertical-align: baseline;
      margin-right: 9px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .el-popover__reference-wrapper {
    height: 46px;
    line-height: 46px;
    width: 180px;
  }
}
.xyl-menu-popover-div-box {
  width: 100%;
  &>span {
    display: inline-block;
    width: 100%;
    height: 100%;
  }
  .xyl-menu-popover-div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 46px;
    color: rgba($color:#fff, $alpha: .8);
    padding-left: 40px;
    padding-right: 20px;
    cursor: pointer;
    span {
      display: inline-block;
      width: calc(100% - 12px);
      line-height: 24px;
      padding: 12px 0;
      font-size: 16px;
    }
    .el-icon-arrow-right {
      font-size: 12px;
      color: rgba($color:#fff, $alpha: .8);
      width: auto;
      margin: 0;
      margin-bottom: 2px;
      transition: transform 0.6s;
      transform: rotateZ(0deg);
    }
    .el-icon-arrow-right-a {
      transition: transform 0.6s;
      transform: rotateZ(90deg);
    }
    &:hover{
      background: #54525F;
    }
  }
}
.xyl-menu-popover {
  margin: 0px !important;
  padding: 0px !important;
  .xyl-menu-list {
    background-color: #9CA2AA;
    .xyl-menu-item {
      height: 46px;
      line-height: 46px;
      color: rgba($color:#fff, $alpha: .9);
      cursor: pointer;
      padding-left: 20px;
      font-size: 16px;
      border-left: 2px solid transparent;
      &:hover{
        background: #C0C5CB;
      }
    }
    .xyl-menu-item-a {
      font-weight: 600;
      border-color: #fff;
      background: #C0C5CB;
    }
    .xyl-menu-popover-div {
      min-height: 46px;
      color: rgba($color:#fff, $alpha: .9);
      padding-left: 20px;
      padding-right: 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      span {
        display: inline-block;
        width: calc(100% - 22px);
        line-height: 24px;
        font-size: 16px;
        padding: 12px 0;
      }
      .el-icon-arrow-right {
        font-size: 12px;
        color: rgba($color:#fff, $alpha: .9);
        width: auto;
        margin: 0;
        margin-bottom: 2px;
        transition: transform 0.6s;
        transform: rotateZ(0deg);
      }
      .el-icon-arrow-right-a {
        transition: transform 0.6s;
        transform: rotateZ(90deg);
      }
      &:hover{
        background: #C0C5CB;
      }
    }
  }
}