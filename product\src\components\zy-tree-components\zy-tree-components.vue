<template>
  <div class="zy-tree-components">
    <div v-for="(treeItem) in filterTreeData(treeData)"
         :key="treeItem.id">
      <div @click="selected(treeItem)"
           v-if="!treeItem[props.children].length"
           :style="{ paddingLeft: padding(hierarchy) + 'px' }"
           :class="['zy-tree-components-item', treeItem.selected?'zy-tree-components-item-selected':'']">
        <div class="zy-tree-components-item-icon"></div>
        <div class="zy-tree-components-item-text">{{treeItem[props.label]}}</div>
      </div>
      <div v-else>
        <div @click="subTree(treeItem)"
             :style="{ paddingLeft: padding(hierarchy) + 'px' }"
             :class="['zy-tree-components-item', treeItem.active?'zy-tree-components-item-active':'', treeItem.selected?'zy-tree-components-item-selected':'']">
          <div class="zy-tree-components-item-icon"
               @click.stop="subTreeicon(treeItem)"><i class="el-icon-caret-right"></i></div>
          <div class="zy-tree-components-item-text">{{treeItem[props.label]}}</div>
        </div>
        <el-collapse-transition>
          <zy-tree-components v-if="treeItem.active"
                              :child="child"
                              :props="props"
                              v-model="treeId"
                              :nodeKey="nodeKey"
                              :hierarchy="hierarchy+1"
                              :tree="treeItem[props.children]"></zy-tree-components>
        </el-collapse-transition>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'zyTreeComponents',
  data () {
    return {
      treeId: this.value,
      treeData: []
    }
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    keyword: {
      type: String,
      default: ''
    },
    tree: {
      type: Array,
      default: function () {
        return []
      }
    },
    hierarchy: {
      type: Number,
      default: 0
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label'
        }
      }
    },
    determine: {
      type: Boolean,
      default: false
    },
    child: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'id'
  },
  watch: {
    value (val) {
      this.treeId = val
      this.selectedMethods()
    },
    treeId (val) {
      this.$emit('id', val)
    },
    tree (val) {
      this.treeDataCope(this.deepCopy(this.tree))
    }
  },
  created () {
    this.treeDataCope(this.deepCopy(this.tree))
  },
  methods: {
    padding (index) {
      var hierarchy = 18 * index
      return hierarchy
    },
    treeDataCope (data) {
      data.forEach(item => {
        item.selected = false
        if (this.treeId === item[this.nodeKey]) {
          item.selected = true
        }
        if (item[this.props.children].length) {
          if ((typeof item.active) === 'undefined') { // eslint-disable-line
            item.active = false
          }
        }
      })
      this.treeData = data
      this.selectedMethods()
    },
    treehierarchy (data) {
      data.forEach(item => {
        if (item[this.nodeKey] === this.treeId) {
          const result = this.makeData(item)
          this.$emit('on-tree-click', result)
        }
        if (item[this.props.children].length) {
          this.treehierarchy(item[this.props.children])
        }
      })
    },
    selected (data) {
      this.treeId = data[this.nodeKey]
    },
    selectedMethods () {
      if (this.hierarchy === 0) {
        this.treehierarchy(this.treeData)
      }
      const treeData = this.treeData
      treeData.forEach(item => {
        item.selected = false
        if (this.treeId === item[this.nodeKey]) {
          item.selected = true
        }
      })
      this.treeData = treeData
    },
    subTree (data) {
      if (!this.child) {
        this.treeId = data[this.nodeKey]
      }
      const treeData = this.treeData
      treeData.forEach(item => {
        if (item[this.nodeKey] === data[this.nodeKey]) {
          item.active = !item.active
        }
      })
      this.treeData = treeData
    },
    subTreeicon (data) {
      const treeData = this.treeData
      treeData.forEach(item => {
        if (item[this.nodeKey] === data[this.nodeKey]) {
          item.active = !item.active
        }
      })
      this.treeData = treeData
    },
    deepCopy (data) {
      var t = this.type(data)
      var o
      var i
      var ni
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (i = 0, ni = data.length; i < ni; i++) {
          o.push(this.deepCopy(data[i]))
        }
        return o
      } else if (t === 'object') {
        for (i in data) {
          o[i] = this.deepCopy(data[i])
        }
        return o
      }
    },
    type (obj) {
      var toString = Object.prototype.toString
      var map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object'
      }
      return map[toString.call(obj)]
    },
    makeData (data) {
      const t = this.type(data)
      let o
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (let i = 0; i < data.length; i++) {
          o.push(this.makeData(data[i]))
        }
      } else if (t === 'object') {
        for (const i in data) {
          if (i != 'active' && i != 'selected') {// eslint-disable-line
            o[i] = this.makeData(data[i])
          }
        }
      }
      return o
    },
    filterTreeData (data) {
      if (this.determine) {
        return data
      }
      if (this.keyword === '') {
        return data
      }
      return this.filterTree(data, this.shopfilterNode) || []
    },
    filterTree (nodes, predicate) {
      if (this.hierarchy !== 0) {
        return nodes
      }
      if (!nodes || !nodes.length) return void 0 // eslint-disable-line
      const children = []
      for (let node of nodes) {
        node = Object.assign({}, node)
        const sub = this.filterTree(node[this.props.children], predicate)
        if ((sub && sub.length) || predicate(node)) {
          sub && (node[this.props.children] = sub)
          if (this.keyword) {
            node.active = true
          }
          children.push(node)
        }
      }
      return children.length ? children : void 0 // eslint-disable-line
    },
    shopfilterNode (data) {
      return data[this.props.label].includes(this.keyword)
    }
  }
}
</script>
<style lang="scss">
@import "./zy-tree-components.scss";
</style>
