<template>
  <div class="readingNotes">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="isOpen"
                   filterable
                   clearable
                   placeholder="请选择是否选登">
          <el-option v-for="item in isOpenData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-date-picker v-model="time"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
      </template>
      <template slot="button">
        <el-button @click="addeditors('0')"
                   type="primary">批量选登</el-button>
        <el-button @click="addeditors('1')"
                   type="primary">批量撤销</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="选登标记"
                           width="120"
                           prop="sort">
            <template slot-scope="scope">
              <div class="Anchoose"
                   v-if="scope.row.isOpen == 1">
                <i class="el-icon-medal-1"></i>
                <div>登选中</div>
              </div>
              <div class="undo"
                   v-else>
                <i class="el-icon-medal"></i>
                <div>未登选</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="笔记内容"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.noteContent}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="点赞数"
                           width="100"
                           prop="likenum"></el-table-column>
          <el-table-column label="关联书籍"
                           width="190">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="library(scope.row.bookId)">{{scope.row.bookName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="笔记作者"
                           width="160"
                           prop="createBy"></el-table-column>
          <el-table-column label="笔记截图"
                           width="160">
            <template slot-scope="scope">
              <div class="table-img">
                <el-image style="width: 100%; height: 100%"
                          :src="scope.row.screenshotUrl"
                          :preview-src-list="[scope.row.screenshotUrl]">
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="笔记创建时间"
                           width="190"
                           prop="createDate"></el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="addeditor(scope.row)"
                         type="primary"
                         plain
                         size="mini">{{scope.row.isOpen=='1'?'撤下':'选登'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="libraryShow"
                      title="书籍详情">
      <libraryDetails :id="id"> </libraryDetails>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      title="笔记详情">
      <readingNotesDetails :id="id"> </readingNotesDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import libraryDetails from '../library/libraryDetails'
import readingNotesDetails from './readingNotesDetails'
export default {
  name: 'readingNotes',
  data () {
    return {
      keyword: '',
      isOpen: '',
      isOpenData: [
        { id: '1', value: '是' },
        { id: '0', value: '否' }
      ],
      time: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      detailsShow: false,
      libraryShow: false
    }
  },
  mixins: [tableData],
  components: {
    libraryDetails,
    readingNotesDetails
  },
  mounted () {
    this.syReadingNotesList()
  },
  methods: {
    search () {
      this.page = 1
      this.syReadingNotesList()
    },
    reset () {
      this.keyword = ''
      this.isOpen = ''
      this.time = ''
      this.syReadingNotesList()
    },
    library (row) {
      this.id = row
      this.libraryShow = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    async syReadingNotesList () {
      const res = await this.$api.academy.syReadingNotesList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        isOpen: this.isOpen,
        beginDate: this.time ? this.time[0] : '',
        endDate: this.time ? this.time[1] : ''
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
    },
    howManyArticle () {
      this.syReadingNotesList()
    },
    whatPage () {
      this.syReadingNotesList()
    },
    addeditor (row) {
      if (row.isOpen === 1) {
        this.syReadingNotesUnDigest(row.id)
      } else {
        this.syReadingNotesDigest(row.id)
      }
    },
    addeditors (type) {
      if (this.choose.length) {
        this.$confirm(`此操作将${type === '1' ? '撤销' : '选登'}当前选中的笔记, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (type === '1') {
            this.syReadingNotesUnDigest(this.choose.join(','))
          } else {
            this.syReadingNotesDigest(this.choose.join(','))
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async syReadingNotesDigest (id) {
      const res = await this.$api.academy.syReadingNotesDigest({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.syReadingNotesList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async syReadingNotesUnDigest (id) {
      const res = await this.$api.academy.syReadingNotesUnDigest({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.syReadingNotesList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.readingNotes {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
    .Anchoose {
      width: 52px;
      text-align: center;
      i,
      div {
        color: $zy-color;
      }
    }
    .undo {
      width: 52px;
      text-align: center;
      i,
      div {
        color: #666;
      }
    }
    .table-img {
      height: 38px;
      overflow: hidden;

      .el-image__inner {
        width: auto;
      }
      .el-icon-circle-close {
        color: #fff;
      }
    }
  }
}
</style>
