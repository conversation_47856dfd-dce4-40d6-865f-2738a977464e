export const getWeatherType = dayWeather => {
  let weatherType
  if (dayWeather === '晴') {
    weatherType = 0
  } else if (dayWeather === '多云') {
    weatherType = 1
  } else if (dayWeather === '阴') {
    weatherType = 2
  } else if (
    dayWeather === '雷阵雨' ||
    dayWeather === '雷阵雨并伴有冰雹' ||
    dayWeather === '雨夹雪' ||
    dayWeather === '小雨' ||
    dayWeather === '中雨' ||
    dayWeather === '大雨' ||
    dayWeather === ' 暴雨' ||
    dayWeather === ' 大暴雨' ||
    dayWeather === ' 特大暴雨' ||
    dayWeather === '冻雨' ||
    dayWeather === '小雨-中雨' ||
    dayWeather === '中雨-大雨' ||
    dayWeather === '大雨-暴雨' ||
    dayWeather === '暴雨-大暴雨' ||
    dayWeather === '大暴雨-特大暴雨') {
    weatherType = 3
  } else if (
    dayWeather === '阵雪' ||
    dayWeather === '小雪' ||
    dayWeather === '中雪' ||
    dayWeather === '大雪' ||
    dayWeather === '暴雪' ||
    dayWeather === '小雪-中雪' ||
    dayWeather === '中雪-大雪' ||
    dayWeather === '大雪-暴雪' ||
    dayWeather === '弱高吹雪') {
    weatherType = 4
  } else {
    weatherType = 5
  }
  return weatherType
}
