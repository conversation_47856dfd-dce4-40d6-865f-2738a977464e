<template>
  <div class="qr-mark">
    <div class="icon-box" @click="handleclose">
      <i class="el-icon-close"></i>
    </div>
    <div class="qr-box">
      <vueQr :text="text" :size="200"></vueQr>
    </div>
    <p class="tips">打开APP扫一扫，直接预览问卷</p>
  </div>
</template>
<script>
import vueQr from 'vue-qr'
export default {
  components: { vueQr },
  props: {
    id: String
  },
  data () {
    return {
      text: ''
    }
  },
  mounted () {
    this.$api.appManagement.getAppconfigCode().then(res => {
      this.text = `http://aa.com/api?code=${res.data.rongCloudIdPrefix}|vote|${this.id}`
    })
  },
  methods: {
    handleclose () {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.qr-mark {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .icon-box {
    width: 24px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    border-radius: 24px;
    cursor: pointer;
    margin-left: 275px;
    margin-bottom: 25px;
    font-size: 18px;
    background-color: #fff;
  }
  .tips {
    font-size: 20px;
    line-height: 36px;
    color: #fff;
    margin-top: 25px;
  }
}
</style>
