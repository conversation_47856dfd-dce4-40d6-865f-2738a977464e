<template>
  <div class="activityTripNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item class="form-title"
                    prop="title">
        <span slot="label"
              class="label-button">日程行程标题 <span style="color: red;">(最多25字)</span>
        </span>
        <el-input placeholder="请输入日程行程标题"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上传行程资料"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleFile"
                   :http-request="fileUpload"
                   :file-list="fileData"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-ue">
        <UEditor v-model="form.content"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'activityTripNew',
  data () {
    return {
      form: {
        title: '',
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入日程行程标题', trigger: 'blur' }
        ]
      },
      fileData: []
    }
  },
  props: ['id', 'meetId'],
  mounted () {
    if (this.id) {
      this.activityscheduleInfo()
    }
  },
  methods: {
    handleFile (file, fileList) {
    },
    fileUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'activityschedule')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.fileData.push(data[0])
      }).catch(() => {
        files.onError()
      })
    },
    beforeRemove (file, fileList) {
      var fileData = this.fileData
      this.fileData = fileData.filter(item => item.id !== file.id)
    },
    async activityscheduleInfo () {
      const res = await this.$api.activity.activityscheduleInfo(this.id)
      var { data } = res
      this.form.title = data.title
      this.form.content = data.content
      if (data.attachmentList) {
        data.attachmentList.forEach((item, index) => {
          item.uid = item.id
          item.name = item.fileName
        })
        this.fileData = data.attachmentList
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var attachmentIds = []
          this.fileData.forEach(item => {
            attachmentIds.push(item.id)
          })
          this.form.attachmentIds = attachmentIds.join(',') // 标签
          this.form.meetId = this.meetId
          let url = '/activityschedule/add?'
          if (this.id) {
            url = '/activityschedule/edit?'
          }
          this.$api.activity.activityscheduleAddorEdit(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            title: this.form.title,
            content: this.form.content,
            meetId: this.meetId,
            attachmentIds: attachmentIds.join(',') // 标签
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.activityTripNew {
  width: 682px;
}
</style>
