const bigdataCenter = [
  { // 全文检索
    path: '/fullSearch',
    name: 'fullSearch',
    component: () => import('@/views/bigdataCenter/fullSearch/fullSearch')
  },
  { // 全文检索详情
    path: '/fullSearchDetails',
    name: 'fullSearchDetails',
    component: () => import('@/views/bigdataCenter/fullSearch/fullSearchDetails')
  },
  { // 数据可视化
    path: '/dataVisualization',
    name: 'dataVisualization',
    component: () => import('@/views/bigdataCenter/dataVisualization/dataVisualization')
  },
  { // 智能报表
    path: '/intelligentReport',
    name: 'intelligentReport',
    component: () => import('@/views/bigdataCenter/intelligentReport/intelligentReport')
  },
  { // 智能报表查看
    path: '/intelligentReportContent',
    name: 'intelligentReportContent',
    component: () => import('@/views/bigdataCenter/intelligentReport/intelligentReportContent')
  },
  { // 智能报表编辑
    path: '/intelligentReportContentEdit',
    name: 'intelligentReportContentEdit',
    component: () => import('@/views/bigdataCenter/intelligentReport/intelligentReportContentEdit')
  },
  { // 数据平台
    path: '/dataPlatform',
    name: 'dataPlatform',
    component: () => import('@/views/bigdataCenter/dataPlatform/dataPlatform')
  },
  { // 数据平台详情
    path: '/dataPlatformDetails',
    name: 'dataPlatformDetails',
    component: () => import('@/views/bigdataCenter/dataPlatform/dataPlatformDetails')
  },
  { // 数据平台更多
    path: '/dataPlatformList',
    name: 'dataPlatformList',
    component: () => import('@/views/bigdataCenter/dataPlatform/dataPlatformList')
  }
]
export default bigdataCenter
