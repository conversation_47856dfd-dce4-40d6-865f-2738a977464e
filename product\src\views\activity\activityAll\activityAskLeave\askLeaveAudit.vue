<template>
  <div class="askLeaveAudit">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="审核"
                    prop="status"
                    class="form-input">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">通过</el-radio>
          <el-radio :label="2">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="短信通知邀请人"
                    prop="isSendMsg"
                    class="form-input">
        <el-radio-group v-model="form.isSendMsg">
          <el-radio label="0">通知</el-radio>
          <el-radio label="1">不通知</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上传文件(文件)"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleFile"
                   :http-request="fileUpload"
                   :file-list="fileData"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="审核说明"
                    class="form-ue">
        <el-input type="textarea"
                  :autosize="{ minRows: 9, maxRows: 12}"
                  placeholder="请输入内容"
                  v-model="form.content">
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'askLeaveAudit',
  data () {
    return {
      form: {
        status: '',
        isSendMsg: '',
        content: 0
      },
      rules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'blur' }
        ],
        isSendMsg: [
          { required: true, message: '请选择是否短信通知邀请人', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入审核说明', trigger: 'blur' }
        ]
      },
      fileData: []
    }
  },
  props: ['id'],
  mounted () {
    if (this.id) {
      this.leaveInfo()
    }
  },
  methods: {
    handleFile (file, fileList) {
    },
    fileUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'leavePhoto')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.fileData.push(data[0])
      }).catch(() => {
        files.onError()
      })
    },
    beforeRemove (file, fileList) {
      var fileData = this.fileData
      this.fileData = fileData.filter(item => item.id !== file.id)
    },
    async leaveInfo () {
      const res = await this.$api.activity.leaveInfo(this.id)
      var { data } = res
      this.form.status = data.status
      this.form.isSendMsg = data.isSendMsg
      this.form.remarks = data.reasonChoose
      this.form.content = data.remarks
      if (data.attachmentList) {
        data.attachmentList.forEach((item, index) => {
          item.uid = item.id
          item.name = item.fileName
        })
        this.fileData = data.attachmentList
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var attachmentIds = []
          this.fileData.forEach(item => {
            attachmentIds.push(item.id)
          })
          this.form.attachmentIds = attachmentIds.join(',')
          this.$api.activity.leaveEditStatusSingle({
            id: this.id,
            status: this.form.status,
            remarks: this.form.content,
            isSendMsg: this.form.isSendMsg,
            attachmentIds: attachmentIds.join(',')
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.askLeaveAudit {
  width: 682px;
}
</style>
