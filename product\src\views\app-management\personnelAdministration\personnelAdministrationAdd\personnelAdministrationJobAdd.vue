<template>
  <div class="personnelAdministrationJobAdd">
    <personnelAdministrationJobNew :id="id"
                      v-if="show"
                      @callback="callback"></personnelAdministrationJobNew>
  </div>
</template>
<script>
const personnelAdministrationJobNew = () => import('../personnelAdministrationJobNew')
export default {
  name: 'personnelAdministrationJobAdd',
  data () {
    return {
      show: true,
      id: this.$route.query.id,
      toId: this.$route.query.toId
    }
  },
  components: {
    personnelAdministrationJobNew
  },
  inject: ['tabDel'],
  methods: {
    callback () {
      if (this.id) {
        this.tabDel(this.id, this.toId)
      } else {
        this.show = false
        setTimeout(() => {
          this.show = true
        }, 200)
      }
    }
  }
}
</script>
