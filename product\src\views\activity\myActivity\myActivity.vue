<template>
  <div class="myActivity">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable">
          <!-- <el-table-column label="序号"
                           width="80"
                           prop="num"></el-table-column> -->
          <el-table-column label="活动主题"
                           min-width="220"
                           prop="meetName">
            <template slot-scope="scope">
              <el-button @click="details(scope.row)"
                         type="text">{{scope.row.meetName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="签到人数"
                           width="90"
                           prop="signInNum"></el-table-column>
          <el-table-column label="签到人员"
                           prop="signInNames"
                           min-width="150"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="活动类型"
                           width="90"
                           prop="meetType"></el-table-column>
          <el-table-column label="组织部门"
                           width="90"
                           prop="organizer"></el-table-column>
          <el-table-column label="活动主体"
                           width="90"
                           prop="pubOrganizer"></el-table-column>
          <el-table-column label="活动开始时间"
                           width="160">
            <template slot-scope="scope">{{scope.row.meetStartTime|datefmt('YYYY-MM-DD')}}</template>
          </el-table-column>
          <el-table-column label="开始签到时间"
                           width="160">
            <template slot-scope="scope">{{scope.row.meetSignBeginTime|datefmt('YYYY-MM-DD')}}</template>
          </el-table-column>
          <el-table-column label="活动结束时间"
                           width="160">
            <template slot-scope="scope">{{scope.row.meetEndTime|datefmt('YYYY-MM-DD')}}</template>
          </el-table-column>
          <el-table-column label="活动状态"
                           min-width="120"
                           prop="state"></el-table-column>
          <el-table-column label="是否APP显示"
                           v-if="$isAppShow()"
                           width="120">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isAppShow"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="是否公开"
                           width="120"
                           prop="isPublish">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isPublish"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="是否公开报名"
                           width="120"
                           prop="isPublishbm">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isPublishbm"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="建立人"
                           width="120"
                           prop="createName">
          </el-table-column>

        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <xyl-popup-window v-model="detailsShow"
                      title="活动详情">
      <activityDetails :id="id"> </activityDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import activityDetails from '../activityAll/activityDetails'
export default {
  name: 'myActivity',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false,
      detailsShow: false
    }
  },
  components: {
    activityDetails
  },
  activated () {
    this.activityFindMyActivitys()
  },
  methods: {
    search () {
      this.page = 1
      this.activityFindMyActivitys()
    },
    reset () {
      this.keyword = ''
      this.activityFindMyActivitys()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    newCallback () {
      this.activityFindMyActivitys()
      this.show = false
      this.detailsShow = false
    },
    async activityFindMyActivitys () {
      const res = await this.$api.activity.activityFindMyActivitys({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    editor (row) {
      this.id = row.id
      this.show = true
    },
    howManyArticle (val) {
      this.activityFindMyActivitys()
    },
    whatPage (val) {
      this.activityFindMyActivitys()
    }
  }
}
</script>
<style lang="scss">
.myActivity {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
    .table-icon {
      .el-icon-check {
        font-size: 16px;
        color: #35be38;
      }

      .el-icon-close {
        font-size: 16px;
        color: #e24c4b;
      }
    }
  }
}
</style>
