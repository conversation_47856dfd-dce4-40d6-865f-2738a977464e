<template>
  <div class="upload">
    <el-upload class="upload-demo" ref="upload" action="/" accept=".xls.xlsx" :http-request="customUpload" :on-remove="handleRemove" :file-list="files" :limit="1" :auto-upload="false">
      <el-button size="small" type="primary">选取文件</el-button>
    </el-upload>
    <div class="form-footer-btn">
      <el-button type="primary" size="small" @click="onSubmit">确定</el-button>
    </div>
  </div>

</template>

<script>
export default {
  props: {
    id: String,
    files: []
  },
  methods: {
    onSubmit() {
      this.$refs.upload.submit()
    },
    // 移除文件
    handleRemove (file) {
      for (const i in this.files) {
        if (this.files[i].uid === file.uid) {
          this.files.splice(i, 1)
        }
      }
    },
    // 上传逻辑
    customUpload (file) {
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('voteId', this.id)
      this.$api.appManagement.voteQuestion.import(formData).then(res => {
        this.$message.success('上传excel成功')
        this.$emit('callback', 1)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.upload {
  width: 450px;
  padding: 20px;
}
</style>
