<template>
  <div class="join-man static">
    <div class="search-box">
      <el-input size="mini"
                v-model="keyword"
                placeholder="请输入关键字"></el-input>
      <el-button size="mini"
                 type="primary"
                 @click="search">查询</el-button>
      <el-button size="mini"
                 @click="reset">重置</el-button>
    </div>
    <div class="btn-box">
      <el-button size="mini"
                 type="primary"
                 @click="handleExport">导出</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  class="sp-table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="题目"
                           prop="name"
                           min-width="260"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="选项"
                           min-width="250">
            <template slot-scope="scope">
              <div v-if="scope.row.topic === 'multi'|| scope.row.topic === 'single'"
                   class="option-item">
                <el-tooltip v-for="(ops,idx) in scope.row.data"
                            :key="idx"
                            class="item"
                            effect="dark"
                            :content="ops.optionName"
                            placement="top">
                  <p class="sp">{{ops.optionName}}</p>
                </el-tooltip>
              </div>
              <div v-if="scope.row.topic === 'text'">
                <span>文本</span>
              </div>
              <div v-if="scope.row.topic === 'judge'">
                <p><i class="el-icon-check check-color"></i></p>
                <p><i class="el-icon-close close-color"></i></p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="投票数"
                           min-width="100">
            <template slot-scope="scope">
              <div v-if="scope.row.topic!=='text'">
                <p class="t-color sh"
                   v-for="(ops,idx) in scope.row.data"
                   :key="idx"
                   @click="openVoteNum(scope.row,ops.optionName)">{{ops.count}}</p>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="page-box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :excelId="excelId"
                 :params="params"
                 :type="110"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  props: {
    id: String
  },
  data () {
    return {
      isStatic: true,
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      selectionList: [],
      params: null,
      exportShow: false,
      excelId: null
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 列表
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        id: this.id
      }
      data.module = 'questionnaire'
      if (this.keyword !== '') {
        data.keyword = this.keyword
      }
      this.$api.appManagement.voteCount(data).then(res => {
        if (res.errcode === 200) {
          this.total = res.data.total
          this.tableData = res.data.list
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 导出
    handleExport () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.params = {
        paperId: this.$route.query.id
      }
      this.exportShow = true
    },
    // 打开投票数
    openVoteNum (val, option) {
      this.$emit('cancel', val, option)
    }
    // 单元格样式
    // cellStyle ({ row, column, rowIndex, columnIndex }) {
    //   if (columnIndex === 4) {
    //     console.log('执行了')
    //     return 'color:red;background:pink;height:100%;'
    //   } else {
    //     return ''
    //   }
    // }
  }
}
</script>

<style lang="scss">
@import "./dialog-list.scss";
.el-tooltip__popper {
  max-width: 600px;
}
</style>
