<template>
  <el-popover placement="right"
              width="180"
              trigger="click"
              transition="el-zoom-in-top"
              :visible-arrow="false"
              :open-delay="400"
              v-model="show"
              popper-class="xyl-menu-popover">
    <div slot="reference"
         class="xyl-menu-popover-div">
      <span>
        <slot></slot>
      </span>
      <i :class="['el-icon-arrow-right',show?'el-icon-arrow-right-a':'']"></i>
    </div>
    <div class="xyl-menu-list">
      <div v-for="(item) in data"
           :key="item[props.id]">
        <xyl-menu-children :data="item[props.children]"
                           v-if="isShowChildren(item)"
                           :parent="parent"
                           :props="props">{{item[props.label]}}</xyl-menu-children>
        <div :class="['xyl-menu-item',item[props.id] == model?'xyl-menu-item-a':'']"
             @click="menuClick(item)"
             v-else>{{item[props.label]}}</div>
      </div>
    </div>
  </el-popover>
</template>
<script>
export default {
  name: 'xylMenuChildren',
  data () {
    return {
      show: false
    }
  },
  inject: {
    menu: {
      default: ''
    }
  },
  props: {
    parent: [String, Number, Array, Object],
    data: {
      type: Array,
      default: () => []
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label',
          id: 'id',
          to: 'to',
          isShow: 'isShow',
          showValue: true
        }
      }
    }
  },
  computed: {
    model: {
      get () {
        return this.menu.value
      }
    }
  },
  created () {
    this.data.forEach(item => {
      if (item[this.props.id] === this.model) {
        this.menu.parent = [this.parent]
        setTimeout(() => {
          this.show = true
        }, 400)
      }
    })
  },
  methods: {
    isShowChildren (menu) {
      let isShow = false
      if (menu[this.props.children].length) {
        isShow = menu[this.props.children].some(item => item[this.props.isShow] === this.props.showValue)
      }
      return isShow
    },
    menuClick (data) {
      this.menu.$emit('input', data[this.props.id])
    }
  }
}
</script>
