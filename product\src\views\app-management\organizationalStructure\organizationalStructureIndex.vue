<template>
  <div class="domtree-index">
    <ul class="domtree-group">
      <li v-for="(item,index) in group" :key="index">
        <span @click="contentClick(item.id)">{{item.name}}</span>
      </li>
    </ul>
    <ul class="domtree-group-line"></ul>
    <ul class="domtree-group-vertical"></ul>
    <ul class="domtree-group-bar">
      <li></li>
      <li></li>
    </ul>
    <ul class="domtree-grouping">
      <li>
         <span  @click="contentClick(grouping.groupingIdOne)">{{grouping.groupingNameOne}}</span>
      </li>
      <li>
        <img src ="../../../assets/images/organizational.png" />
      </li>
      <!-- <li>↓</li>
      <li>
        <i>指</i>
        <i>导</i>
      </li>
      <li>↓</li> -->
      <li>
         <span  @click="contentClick(grouping.groupingIdTwo)">{{grouping.groupingNameTwo}}</span>
       </li>
    </ul>
    <ul class="domtree-grouping-vertical"></ul>
    <ul class="domtree-branch">
      <li>
          <span  @click="contentClick(branch.branchIdOne)">{{branch.branchNameOne}}</span>
        </li>
        <li>
          <span  @click="contentClick(branch.branchIdTwo)">{{branch.branchNameTwo}}</span>
        </li>
        <li>
          <span  @click="contentClick(branch.branchIdThree)">{{branch.branchNameThree}}</span>
        </li>
        <li>
          <span  @click="contentClick(branch.branchIdFour)">{{branch.branchNameFour}}</span>
        </li>
    </ul>
    <ul class="domtree-detailed">
        <li class="domtree-detailed-list-vertical-one"></li>
        <li class="domtree-detailed-list domtree-detailed-list-one">
            <ul>
                <li v-for="(item,index) in detailedOne" :key="index">
                  <span  @click="contentClick(item.id)">{{item.name}}</span>
                </li>
            </ul>
        </li>
        <li class="domtree-detailed-list-vertical-two"></li>
        <li class="domtree-detailed-list domtree-detailed-list-two">
            <ul>
                <li v-for="(item,index) in detailedTwo" :key="index">
                  <span  @click="contentClick(item.id)">{{item.name}}</span>
                </li>
            </ul>
        </li>
        <li class="domtree-detailed-list-vertical-three"></li>
        <li class="domtree-detailed-list domtree-detailed-list-three">
            <ul>
                <li v-for="(item,index) in detailedThree" :key="index">
                  <span  @click="contentClick(item.id)">{{item.name}}</span>
                </li>
            </ul>
        </li>
        <li class="domtree-detailed-list-vertical-four"></li>
        <li class="domtree-detailed-list domtree-detailed-list-four">
            <ul>
                <li v-for="(item,index) in detailedFour" :key="index">
                  <span  @click="contentClick(item.id)">{{item.name}}</span>
                </li>
            </ul>
        </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'organizationalStructureIndex',
  emits: ['updateSelectNode'],
  data() {
    return {
      group: [],
      grouping: [{
        groupingNameOne: '',
        groupingIdOne: ''
      }, {
        groupingNameTwo: '',
        groupingIdTwo: ''
      }],
      branch: [{
        branchNameOne: '',
        branchIdOne: ''
      }, {
        branchNameTwo: '',
        branchIdTwo: ''
      }, {
        branchNameThree: '',
        branchIdThree: ''
      }, {
        branchNameFour: '',
        branchIdFour: ''
      }],
      detailedOne: [],
      detailedTwo: [],
      detailedThree: [],
      detailedFour: [],
      isShow: false
    }
  },
  created() {
    this.informationColumnTree()
  },
  methods: {
    async informationColumnTree () {
      const res = await this.$api.appManagement.organizationTree({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword
      })
      var { data } = res
      if (data) {
        if (data[0].name) {
          const datas = {}
          datas.name = data[0].name
          datas.id = data[0].id
          this.group.push(datas)
        }

        if (data[0].children) {
          data.forEach(item => {
            this.grouping.groupingNameOne = item.children[0].name
            this.grouping.groupingIdOne = item.children[0].id
            this.grouping.groupingNameTwo = item.children[1].name
            this.grouping.groupingIdTwo = item.children[1].id
          })
        }

        if (data[0].children[1].children) {
          data.forEach(item => {
            this.branch.branchNameOne = item.children[0].children[0].name
            this.branch.branchIdOne = item.children[0].children[0].id
          })
        }

        if (data[0].children[1].children) {
          data.forEach(item => {
            this.branch.branchNameTwo = item.children[1].children[0].name
            this.branch.branchIdTwo = item.children[1].children[0].id
            this.branch.branchNameThree = item.children[1].children[1].name
            this.branch.branchIdThree = item.children[1].children[1].id
            this.branch.branchNameFour = item.children[1].children[2].name
            this.branch.branchIdFour = item.children[1].children[2].id
          })
        }

        if (data[0].children[0].children) {
          for (let i = 0; i < data.length; i++) {
            for (let j = 0; j < data[i].children.length; j++) {
              for (let k = 0; k < data[i].children[j].children.length; k++) {
                for (let h = 0; h < data[i].children[j].children[k].children.length; h++) {
                  if (this.branch.branchIdOne === data[i].children[j].children[k].children[h].parentId) {
                    this.detailedOne.push(data[i].children[j].children[k].children[h])
                  }
                  if (this.branch.branchIdTwo === data[i].children[j].children[k].children[h].parentId) {
                    this.detailedTwo.push(data[i].children[j].children[k].children[h])
                  }
                  if (this.branch.branchIdThree === data[i].children[j].children[k].children[h].parentId) {
                    this.detailedThree.push(data[i].children[j].children[k].children[h])
                  }
                  if (this.branch.branchIdFour === data[i].children[j].children[k].children[h].parentId) {
                    this.detailedFour.push(data[i].children[j].children[k].children[h])
                  }
                }
              }
            }
          }
        }
      }
    },
    contentClick(ids) {
      this.$emit('updateSelectNode', { id: ids })
    }
  }
}
</script>
<style lang="scss">
.domtree-index {
    width: 80%;
    margin: 15px auto;
    overflow: hidden;
    .domtree-group {
      width: 14%;
      float:left;
      // margin-right: 4%;
      li {
        position: relative;
        // margin-top: 320px;
        margin-top: 340px;
        span {
          font-size:20px;
          color: #fff;
          background:rgba(242, 121, 0, 1);
          display: block;
          padding: 10px;
          border-radius: 7px;
          border: 1px solid rgba(121, 121, 121, 1);
          text-align: center;
          cursor: pointer;
          margin-top: 30px;
        }
      }
    }
    .domtree-group-line {
      width: 2%;
      height: 2px;
      float: left;
      background-color: #000;
      // margin-top: 340px;
      margin-top: 360px;
    }
    .domtree-group-vertical,
    .domtree-grouping-vertical,
    .domtree-detailed-list-vertical-one,
    .domtree-detailed-list-vertical-two,
    .domtree-detailed-list-vertical-three,
    .domtree-detailed-list-vertical-four {
      width: 0.1%;
      height: 490px;
      float: left;
      background-color: #000;
      margin-top: 148px;
    }
    .domtree-grouping-vertical {
      width: 2px;
      // height: 500px;
      height: 545px;
      // margin: 375px 0 0 -24px;
      margin: 385px 0 0 -24px;
    }
    .domtree-detailed-list-vertical-one {
      width: 2px;
      height: 220px;
      margin: 40px 0 0 -24px;
    }
    .domtree-detailed-list-vertical-two {
      width: 2px;
      height: 110px;
      margin: 65px 0 0 -24px;
    }
    .domtree-detailed-list-vertical-three {
      width: 2px;
      height: 220px;
      margin: 65px 0 0 -24px;
    }
    .domtree-detailed-list-vertical-four {
      width: 2px;
      height: 220px;
      margin: 65px 0 0 -24px;
    }
    .domtree-group-bar {
      li{
        width: 2%;
        height: 2px;
        float: left;
        background-color: #000;
        margin: 146px 0 0 -2px;
      }
      li:last-child{
        // margin: 586px 0 0 -23px;
        margin: 636px 0 0 -23px;
      }
    }
    .domtree-grouping,
    .domtree-branch {
      width: 16%;
      float:left;
      margin: 95px 4% 0 0;
     }
    .domtree-grouping {
      li {
        position: relative;
        span {
          font-size: 20px;
          color: #fff;
          background:rgba(4, 106, 108, 1);
          display: block;
          padding: 10px;
          border-radius: 7px;
          border: 1px solid rgba(121, 121, 121, 1);
          text-align: center;
          cursor: pointer;
          margin-top: 30px;
        }
      }
      li:nth-child(1):after{
        content:'';
        width: 48px;
        height: 2px;
        background-color: #000;
        display: block;
        margin: -20px 0 0 190px;
      }
      li:nth-child(2){
        margin: 148px auto;
        text-align: center;
      }
      // li:nth-child(1):after {
      //   content:'';
      //   width: 48px;
      //   height: 2px;
      //   background-color: #000;
      //   display: block;
      //   margin: -20px 0 0 190px;
      // }
      // li:nth-child(2):before {
      //   width: 2px;
      //   height: 48px;
      //   margin: -100px 0 0 10px;
      // }
      // li:nth-child(3) {
      //   border: 1px solid #000;
      //   width: 18%;
      //   // margin: 136px auto;
      //   margin: 145px auto;
      //   i {
      //     font-style: normal;
      //     display: block;
      //     color: #FF0000;
      //     font-size: 22px;
      //     padding: 10px 5px;
      //   }
      // }
      // li:nth-child(2),
      // li:nth-child(4) {
      //   margin: 0 auto;
      //   text-align: center;
      //   font-size: 28px;
      // }
      // li:nth-child(2) {
      //   top: 60px;
      // }
      li:last-child {
        top: 15px;
        span {
          background: rgba(15, 91, 168, 1);
        }
      }
      li:last-child:after {
        content:'';
        width: 48px;
        height: 2px;
        background-color: #000;
        display: block;
        margin: -20px 0 0 190px;
      }
    }
    .domtree-branch {
      li {
        position: relative;
        span {
          font-size: 18px;
          color: #fff;
          background:rgba(4, 106, 108, 1);
          display: block;
          padding: 10px;
          border-radius: 7px;
          border: 1px solid rgba(121, 121, 121, 1);
          text-align: center;
          cursor: pointer;
          margin-top: 30px;
        }
      }
      li:nth-child(1):after,
      li:nth-child(2):after,
      li:nth-child(3):after,
      li:nth-child(4):after {
        content:'';
        width: 48px;
        height: 2px;
        background-color: #000;
        display: block;
        margin: -20px 0 0 190px;
      }
      li:nth-child(2):before,
      li:nth-child(4):before {
        content:'';
        width: 24px;
        height: 2px;
        background-color: #000;
        display: block;
        position: relative;
        top: 52px;
        left: -24px;
      }
      li:nth-child(4):before {
        top: 52px;
      }
      li:nth-child(2) {
        // margin-top: 175px;
        margin-top: 185px;
        span {
          background: rgba(113, 113, 255, 1);
        }
      }
      li:nth-child(3) {
        // margin-top: 190px;
         margin-top: 225px;
        span {
          background: rgba(238, 117, 126, 1);
        }
      }
      li:nth-child(4) {
        // margin-top: 235px;
        margin-top: 245px;
        span {
          background: rgba(62, 139, 202, 1);
        }
      }
    }
    .domtree-detailed {
      width: 15%;
      float:left;
      .domtree-detailed-list ul li {
        position: relative;
        span {
          font-size: 16px;
          color: #fff;
          background:rgba(4, 106, 108, 1);
          display: block;
          padding: 10px;
          border-radius: 7px;
          border: 1px solid rgba(121, 121, 121, 1);
          text-align: center;
          cursor: pointer;
          margin-top: 15px;
        }
      }
     .domtree-detailed-list-one {
        li:nth-child(1):before,
        li:nth-child(2):before,
        li:nth-child(4):before,
        li:nth-child(5):before  {
          content:'';
          width: 24px;
          height: 2px;
          background-color: #000;
          display: block;
          position: relative;
          top: 38px;
          left: -24px;
        }
        li:nth-child(5):before {
          top: 40px;
        }
      }
      .domtree-detailed-list-two {
        li:nth-child(1):before,
        li:nth-child(3):before {
          content:'';
          width: 24px;
          height: 2px;
          background-color: #000;
          display: block;
          position: relative;
          top: 38px;
          left: -24px;
        }
        li:nth-child(3):before {
          top: 40px;
        }
      }
      .domtree-detailed-list-three {
        li:nth-child(1):before,
        li:nth-child(2):before,
        li:nth-child(4):before,
        li:nth-child(5):before  {
          content:'';
          width: 24px;
          height: 2px;
          background-color: #000;
          display: block;
          position: relative;
          top: 38px;
          left: -24px;
        }
        li:nth-child(5):before {
          top: 40px;
        }
      }
      .domtree-detailed-list-four {
        li:nth-child(1):before,
        li:nth-child(2):before,
        li:nth-child(4):before,
        li:nth-child(5):before  {
          content:'';
          width: 24px;
          height: 2px;
          background-color: #000;
          display: block;
          position: relative;
          top: 38px;
          left: -24px;
        }
        li:nth-child(5):before {
          top: 40px;
        }
      }
      .domtree-detailed-list-two ul li {
        span {
          background: rgba(113, 113, 255, 1);
        }
      }
      .domtree-detailed-list-three ul li {
        span {
          background: rgba(238, 117, 126, 1);
        }
      }
      .domtree-detailed-list-four ul li {
        span {
          background: rgba(62, 139, 202, 1);
        }
      }
      .domtree-detailed-list-two,
      .domtree-detailed-list-three,
      .domtree-detailed-list-four {
        margin-top: 25px;
      }
    }
}
</style>
