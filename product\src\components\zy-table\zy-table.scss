.zy-table {
  height: 100%;
  width: 100%;
  background-color: #fff;
  position: relative;
  border-bottom: 1px solid #e6e6e6;

  .my-scroll-bar {
    height: 100%;
    width: 100%;

    .el-scrollbar__wrap {
      // overflow-x: hidden;

      /*IE下隐藏滚动条*/
      -ms-scroll-chaining: chained;
      -ms-overflow-style: none;
      -ms-content-zooming: zoom;
      -ms-scroll-rails: none;
      -ms-content-zoom-limit-min: 100%;
      -ms-content-zoom-limit-max: 500%;
      // -ms-scroll-snap-type: proximity;
      -ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
      -ms-overflow-style: none;
      overflow: auto;
      /*火狐下隐藏滚动条*/
      scrollbar-width: none;

      /*谷歌下隐藏滚动条*/
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .el-scrollbar__bar {
      z-index: 99;
    }

    .is-vertical {
      .el-scrollbar__thumb {
        background: rgba(0, 0, 0, 0.46);
      }
    }

    .is-horizontal {
      height: 28px;
      display: flex;
      align-items: center;
      // background-color: #eef1f4;
      background-color: #f9f9f9;
      bottom: 0;

      .el-scrollbar__thumb {
        height: 24px;
        background: url('../../assets/img/slide_bg.png') no-repeat center center;
        // background-color: #fbfbfb;
        background-color: #ececec;
      }
    }
  }

  .el-scrollbar__wrap .el-scrollbar__view {
    box-sizing: border-box;

    .el-table {
      padding-bottom: 28px;
      overflow: visible;
      top: 0;
      left: 0;
      background-color: #fff;

      &::before {
        background-color: transparent;
      }

      &::after {
        background-color: transparent;
      }
    }

    .el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: #f1f1f3;
    }

    .hover-row > td {
      background-color: #f1f1f3;
    }

    .el-table th {
      background-color: #ebebed;
      height: 52px;
      font-size: 14px;

      & > .cell {
        font-weight: 600;
      }
    }

    .el-table .cell {
      padding: 0 16px;
      position: relative;

      .caret-wrapper {
        position: absolute;
        transform: translateY(-5px);
      }
    }

    .el-table__body td {
      height: 52px;
      padding: 0;
      font-size: 14px;
    }

    .el-table--border th {
      border-color: #e6e6e6;
    }

    .el-table--border td {
      border-color: transparent;
    }

    .el-button {
      font-size: 14px;
    }

    .el-table__header-wrapper {
      overflow: visible;
      position: relative;
      top: 0;
      left: 0;
      z-index: 99;
    }

    .el-table__fixed {
      z-index: 99;

      .el-table__fixed-header-wrapper {
        z-index: 99;
        top: -1px;
      }
    }

    .el-table__fixed::before,
    .el-table__fixed-right::before {
      background-color: transparent;
    }

    .el-table__fixed-right {
      z-index: 99;
      box-shadow: 0 0 0 rgba(0, 0, 0, 0);

      .el-table__fixed-header-wrapper {
        z-index: 99;
        top: -1px;
      }
    }

    // .el-table__body-wrapper {
    //   width: 100%;
    // }

    .el-table--scrollable-x .el-table__body-wrapper {
      overflow: visible;
    }
  }
  .el-scrollbar__bar {
    z-index: 99;
    opacity: 1;
    .is-vertical {
      opacity: 1;
    }
  }
}
