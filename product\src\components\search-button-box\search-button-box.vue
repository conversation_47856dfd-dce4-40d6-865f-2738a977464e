<template>
  <div class="search-button-box">
    <div class="search-button">
      <slot name="button"></slot>
    </div>
    <div class="search-search-box">
      <slot name="search"></slot>
      <el-button @click="search"
                 v-if="searchButton"
                 type="primary">查询</el-button>
      <el-button @click="reset"
                 v-if="resetButton">重置</el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'searchButtonBox',
  props: {
    searchButton: {
      type: <PERSON>olean,
      default: true
    },
    resetButton: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    search () {
      this.$emit('search-click')
    },
    reset () {
      this.$emit('reset-click')
    }
  }
}
</script>
<style lang="scss">
.search-button-box {
  height: 64px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  .el-button {
    padding: 0 16px;
    line-height: 40px;
    height: 40px;
  }
  .search-search-box {
    display: flex;
    align-items: center;
    .el-input {
      width: 222px;
      margin-left: 24px;
    }
    .zy-select {
      margin-left: 24px;
      .el-input {
        width: 222px;
        margin-left: 0;
      }
    }
    .el-button {
      margin-left: 24px;
    }
    .el-select {
      margin-left: 24px;

      .el-input {
        margin-left: 0;
      }
    }
  }
}
</style>
