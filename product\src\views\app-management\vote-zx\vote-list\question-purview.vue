<template>
  <div class="question">
    <p class="title">{{voteInfo.name}} <span v-if="userName">({{userName}})</span> </p>
    <p class="describe">{{voteInfo.instructions}}</p>
    <div class="question-item" v-for="(item,index) in questionList" :key="index">
      <p class="question-name">{{index+1}}、{{item.name}}
        <span v-if="item.topic === 'multi'" class="more-choice">[多选]</span>
        <span v-if="item.topic === 'single'" class="more-choice">[单选]</span>
        <!-- <span v-if="item.topic === 'judge'" class="more-choice">[填空]</span> -->
      </p>
      <!-- 单选 -->
      <template v-if="item.topic === 'single'">
        <el-radio-group v-model="item.answer">
          <p v-for="(op,idx) in item.options" :key="idx" class="options-item">
            <el-radio :label="op">{{op}}</el-radio>
          </p>
        </el-radio-group>
      </template>
      <!-- 多选 -->
      <template v-if="item.topic === 'multi'">
        <!-- <p v-for="(ops,idx) in item.options" :key="idx" class="options-item">
            <el-checkbox :label="ops"></el-checkbox>
          </p> -->
        <el-checkbox-group v-model="item.answer">
          <p v-for="(ops,idx) in item.options" :key="idx" class="options-item">
            <el-checkbox :label="ops"></el-checkbox>
          </p>
        </el-checkbox-group>
      </template>
      <!-- <template v-if="item.topic === 'judge'">
        <div v-if="item.paperAnswer">
          <p v-if="item.paperAnswer.answer==='0'"><i class="el-icon-check check-color"></i></p>
          <p v-if="item.paperAnswer.answer==='1'"><i class="el-icon-close close-color"></i></p>
        </div>
      </template> -->
      <!-- 文本输入 -->
      <template v-if="item.topic === 'text'">
        <el-input v-if="item.paperAnswer" class="textarea" type="textarea" :rows="3" placeholder="请输入..." v-model="item.paperAnswer.answer" maxlength="150" show-word-limit>
        </el-input>
        <el-input v-else class="textarea" type="textarea" :rows="3" placeholder="请输入..." v-model="item.answer" maxlength="150" show-word-limit>
        </el-input>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      voteInfo: {},
      questionList: [],
      userName: ''
    }
  },
  mounted () {
    if (this.$route.query.user) {
      this.userName = this.$route.query.user.userName
    }
    this.getQuestion()
  },
  methods: {
    // 获取投票和题目
    getQuestion () {
      const voteId = this.$route.query.id
      this.$api.appManagement.voteInfo(voteId).then(res => {
        if (res.errcode === 200) {
          this.voteInfo = res.data
        }
      })
      const data = {
        pageNo: 1,
        pageSize: 100,
        paperId: voteId
      }
      data.module = 'questionnaire'
      if (this.$route.query.user) {
        data.userId = this.$route.query.user.userId
        this.$api.appManagement.voteUserAnswer(data).then(res => {
          if (res.errcode === 200) {
            this.questionList = res.data.list.map(item => {
              if (item.topic === 'multi' || item.topic === 'single') {
                item.options = item.options.split('|')
                if (item.paperAnswer) {
                  if (item.topic === 'single') {
                    item.answer = item.paperAnswer.answer.split('||').filter(item => item)[0]
                  } else {
                    item.answer = item.paperAnswer.answer.split('||').filter(item => item)
                  }
                } else {
                  if (item.topic === 'single') {
                    item.answer = ''
                  } else {
                    item.answer = []
                  }
                }
              }
              return item
            })
          }
        })
      } else {
        this.$api.appManagement.votequestionList(data).then(res => {
          if (res.errcode === 200) {
            this.questionList = res.data.map(item => {
              if (item.topic === 'multi' || item.topic === 'single') {
                item.options = item.options.split('|')
              }
              if (item.topic === 'multi') {
                item.answer = []
              }
              return item
            })
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
@import "./vote-list.scss";
</style>
