<template>
  <div class="libraryAll">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <zy-select width="222"
                   node-key="id"
                   :data="bookType"
                   twolabel="bookCount"
                   twolabelText="册"
                   v-model="bookTypeId"
                   @select="bookTypeChoose"
                   :props="{children: 'children',label: 'name'}"
                   placeholder="请选择书库分类"></zy-select>
      </template>
      <template slot="button">
        <el-button @click="newData"
                   type="primary">从总书库添加书籍</el-button>
      </template>
    </xyl-search-button>
    <el-scrollbar class="libraryAllBox">
      <div class="libraryAllListBox">
        <div class="libraryAllItem"
             v-for="(item) in tableData"
             :key="item.id"
             @click="details(item)">
          <div class="libraryAllItemImg">
            <img :src="item.coverImgUrl"
                 alt="">
          </div>
          <div class="libraryAllItemBox">
            <div class="libraryAllItemName">{{item.bookName}}</div>
            <div class="libraryAllItemIntroduction">{{item.bookDescription}}</div>
            <div class="libraryAllItemAuthor">
              <div class="libraryAllItemAuthorText">{{item.authorName}}</div>
              <div class="libraryAllItemButton">
                <el-button type="primary"
                           plain
                           @click.stop="editor(item)">编辑</el-button>
                <el-button type="primary"
                           plain
                           @click.stop="bookDel(item)">删除</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="show"
                      title="编辑书籍">
      <booksAdd :id="id"
                @callback="newCallback"> </booksAdd>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      title="书籍详情">
      <libraryDetails :id="id"
                      @callback="newCallback"> </libraryDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import booksAdd from './booksAdd'
import libraryDetails from './libraryDetails'
export default {
  name: 'libraryAll',
  data () {
    return {
      keyword: '',
      bookTypeId: '',
      bookTypeFirstId: '',
      bookTypeSecondId: '',
      bookType: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false,
      detailsShow: false
    }
  },
  inject: ['newTab'],
  components: {
    booksAdd,
    libraryDetails
  },
  watch: {
    bookTypeId (val) {
      if (val === '') {
        this.bookTypeFirstId = ''
        this.bookTypeSecondId = ''
      }
    }
  },
  activated () {
    this.getSyTypeTree()
    this.syBookList()
  },
  methods: {
    search () {
      this.page = 1
      this.syBookList()
    },
    reset () {
      this.keyword = ''
      this.bookTypeId = ''
      this.bookTypeFirstId = ''
      this.bookTypeSecondId = ''
      this.syBookList()
    },
    newData () {
      this.newTab({ name: '总书库', menuId: '16620092810', to: '/totalLibrary' })
    },
    async getSyTypeTree () {
      const res = await this.$api.academy.getSyTypeTree({ neecCountBook: 1 })
      var { data } = res
      this.bookType = data
    },
    bookTypeChoose (data) {
      if (data.level == '1') { // eslint-disable-line
        this.bookTypeFirstId = data.id
        this.bookTypeSecondId = ''
      } else {
        this.bookTypeFirstId = ''
        this.bookTypeSecondId = data.id
      }
    },
    async syBookList () {
      const res = await this.$api.academy.syBookList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        bookTypeFirstId: this.bookTypeFirstId,
        bookTypeSecondId: this.bookTypeSecondId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
    },
    howManyArticle () {
      this.syBookList()
    },
    whatPage () {
      this.syBookList()
    },
    editor (row) {
      this.id = row.id
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    newCallback () {
      this.search()
      this.show = false
    },
    bookDel (row) {
      this.$confirm('此操作将删除该书籍, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.syBookdels(row.id)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async syBookdels (id) {
      const res = await this.$api.academy.syBookdels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.search()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.libraryAll {
  width: 100%;
  height: 100%;
  .libraryAllBox {
    width: 100%;
    height: calc(100% - 116px);
    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .is-vertical {
      .el-scrollbar__thumb {
        background-color: rgba(144, 147, 153, 0.8);
      }
    }

    .libraryAllListBox {
      display: flex;
      flex-wrap: wrap;
      margin: 24px;
      margin-right: 24px;
      border-left: 1px solid #e5e5e5;
      .libraryAllItem {
        display: flex;
        justify-content: space-between;
        width: 378px;
        height: 172px;
        cursor: pointer;
        border: 1px solid #e5e5e5;
        border-left: 0;
        padding: 18px;
        .libraryAllItemImg {
          height: 138px;
          width: 102px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .libraryAllItemBox {
          width: 226px;
          height: 100%;
          position: relative;
          .libraryAllItemName {
            color: #333;
            line-height: 21px;
            font-size: 16px;
            margin-bottom: 7px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .libraryAllItemIntroduction {
            line-height: 24px;
            color: #666;
            letter-spacing: 0.93px;
            height: 72px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            font-size: 13px;
          }
          .libraryAllItemAuthor {
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .libraryAllItemAuthorText {
              width: calc(100% - 126px);
              font-size: 13px;
              color: #999;
              letter-spacing: 1px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
            .libraryAllItemButton {
              min-width: 118px;
              .el-button {
                font-size: 13px;
                padding: 0 12px;
                height: 28px;
                span {
                  display: inline-block;
                  line-height: 28px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
