<template>
  <el-popover placement="bottom-start"
              trigger="click"
              :popper-class="`HeaderMultiSelect ${className}`">
    <template slot="reference">
      <div class="HeaderMultiSelectName">
        {{name}}<div class="HeaderMultiSelectIcon"></div>
      </div>
    </template>
    <el-checkbox :indeterminate="isIndeterminate"
                 v-model="checkAll"
                 @change="handleCheckAllChange">全选</el-checkbox>
    <!-- <el-scrollbar class="HeaderMultiSelectCheckbox"> -->
    <el-checkbox-group v-model="checkedCities"
                       @change="handleCheckedCitiesChange">
      <el-checkbox v-for="item in data"
                   :label="item.id"
                   :key="item.id">{{item.value}}</el-checkbox>
    </el-checkbox-group>
    <!-- </el-scrollbar> -->
  </el-popover>
</template>
<script>
export default {
  name: 'HeaderMultiSelect',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    },
    name: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      checkAll: false,
      checkedCities: [],
      isIndeterminate: true
    }
  },
  watch: {
    value () {
      this.checkedCities = this.value
      this.checkAll = this.checkedCities.length === this.data.length
      this.isIndeterminate = this.checkedCities.length > 0 && this.checkedCities.length < this.data.length
    },
    data () {
      this.checkAll = this.checkedCities.length === this.data.length
      this.isIndeterminate = this.checkedCities.length > 0 && this.checkedCities.length < this.data.length
    }
  },
  created () {
    this.checkedCities = this.value
    this.checkAll = this.checkedCities.length === this.data.length
    this.isIndeterminate = this.checkedCities.length > 0 && this.checkedCities.length < this.data.length
  },
  methods: {
    handleCheckAllChange (val) {
      this.checkedCities = val ? this.data.map((v) => v.id) : []
      this.isIndeterminate = false
      this.$emit('input', this.checkedCities)
      this.$emit('checkbox')
    },
    handleCheckedCitiesChange (value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.data.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.data.length
      this.$emit('input', this.checkedCities)
      this.$emit('checkbox')
    }
  }
}
</script>
<style lang="scss">
.HeaderMultiSelectName {
  display: flex;
  align-items: center;
  .HeaderMultiSelectIcon {
    width: 18px;
    height: 18px;
    background: url("./header-multi-select.png") no-repeat;
    background-size: 100% 100%;
    margin-left: 2px;
  }
}
.HeaderMultiSelect {
  width: 360px;
  // padding: 0 !important;
  // height: 280px;
  .HeaderMultiSelectCheckbox {
    width: 100%;
    height: 220px;

    .el-scrollbar__wrap {
      overflow-x: hidden;

      .el-scrollbar__view {
        padding: 6px 12px;
      }
    }
  }
  .el-checkbox {
    width: 50%;
    margin: 6px 0;
  }
}
</style>
