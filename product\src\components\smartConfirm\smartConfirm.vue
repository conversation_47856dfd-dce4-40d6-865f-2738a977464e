<template>
  <transition name="confirmbox-fade">
    <div class="smartConfirmWrapper"
         v-show="visible"
         @click="otherClick">
      <div class="smartConfirmBox"
           ref="confirmBox">
        <div class="smartConfirmImg">
          <img src="../../assets/images/helper.gif"
               alt="/">
        </div>
        <div class="confirm_title">{{ title }}</div>
        <div class="content_text">{{ message }}</div>
        <div class="footer_button">
          <el-button @click="cancel">{{cancelText}}</el-button>
          <el-button type="primary"
                     @click="define">{{defineText}}</el-button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'smartConfirm',
  data () {
    return {
      title: '',
      message: '',
      defineText: '确定',
      cancelText: '取消',
      visible: false
    }
  },
  methods: {
    otherClick (e) {
      const confirmBox = this.$refs.confirmBox
      if (e.target.contains(confirmBox)) {
        this.cancel()
      }
    },
    close () {
      this.visible = false
      setTimeout(() => {
        this.$el.parentNode.removeChild(this.$el)
      }, 300)
    },
    define () {
      this.close()
      this.callback('confirm')
    },
    cancel () {
      this.close()
      this.callback('cancel')
    }
  }
}
</script>
<style lang="scss" scoped>
.smartConfirmWrapper {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  .smartConfirmBox {
    width: 390px;
    padding-bottom: 10px;
    vertical-align: middle;
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    text-align: left;
    backface-visibility: hidden;
    position: relative;
    .smartConfirmImg {
      position: absolute;
      right: 0;
      top: 0;
      transform: translate(50%, -50%);
      width: 66px;
      height: 66px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .confirm_title {
      display: flex;
      align-items: center;
      padding: 22px 22px 12px 22px;
      font-size: 18px;
      line-height: 28px;
    }
    .content_text {
      padding: 0 22px;
      color: #606266;
      font-size: 14px;
      line-height: 22px;
    }
    .footer_button {
      display: flex;
      justify-content: flex-end;
      padding-top: 22px;
      padding-right: 22px;
      .el-button {
        height: 36px;
        line-height: 34px;
        padding: 0 12px;
      }
      .el-button + .el-button {
        margin-left: 22px;
      }
    }
  }
}
.confirmbox-fade-enter-active {
  -webkit-animation: confirmbox-fade-in 0.3s;
  animation: confirmbox-fade-in 0.3s;
}
.confirmbox-fade-leave-active {
  -webkit-animation: confirmbox-fade-out 0.3s;
  animation: confirmbox-fade-out 0.3s;
}
@keyframes confirmbox-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
@keyframes confirmbox-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}
</style>
