<template>
  <div class="academyAnnouncementNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item class="form-title"
                    label="主题"
                    prop="noticeTitle">
        <el-input placeholder="请输入主题"
                  v-model="form.noticeTitle"
                  clearable
                  prop="meetName">
        </el-input>
      </el-form-item>
      <el-form-item class="form-input"
                    prop="sort"
                    label="序号">
        <el-input placeholder="请输入序号"
                  type="number"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="发布部门"
                    class="form-input">
        <zy-select width="296"
                   node-key="id"
                   v-model="form.dept"
                   :data="institutions"
                   placeholder="请选择发布部门"></zy-select>
      </el-form-item>
      <el-form-item label="发布日期"
                    class="form-input">
        <el-date-picker v-model="form.publishDate"
                        type="datetime"
                        value-format="timestamp"
                        placeholder="选择发布日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="关联书籍"
                    class="form-title">
        <el-button type="primary"
                   @click="BooksClick"
                   class="academyAnnouncementNewButton">选择书籍</el-button>
        <div class="academyAnnouncementNewItemBox">
          <div class="academyAnnouncementNewItem"
               v-for="(item, index) in book"
               :key="index">
            <div class="academyAnnouncementNewItemImg">
              <img :src="item.coverImgUrl"
                   alt="">
            </div>
            <div class="academyAnnouncementNewItemBoxs">
              <div class="academyAnnouncementNewItemName">{{item.bookName}}</div>
              <div class="academyAnnouncementNewItemIntroduction">{{item.bookDescription}}</div>
              <div class="academyAnnouncementNewItemAuthor">
                <div class="academyAnnouncementNewItemAuthorText">{{item.authorName}}</div>
                <div class="libraryAllItemButton">
                  <el-button type="text"
                             @click.stop="bookDel(item)">删除</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="内容"
                    prop="content"
                    class="form-ue">
        <UEditor v-model="form.content"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
    <xyl-popup-window v-model="show"
                      title="选择书籍">
      <RelatedBooks :data="book"
                    @callback="callback"></RelatedBooks>
    </xyl-popup-window>
  </div>
</template>
<script>
import RelatedBooks from '../RelatedBooks/RelatedBooks'
export default {
  name: 'academyAnnouncementNew',
  data () {
    return {
      form: {
        noticeTitle: '',
        sort: '',
        dept: '',
        publishDate: '',
        content: ''
      },
      rules: {
        noticeTitle: [
          { required: true, message: '请输入主题', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入序号', trigger: 'blur' }
        ],
        dept: [
          { required: true, message: '请选择发布部门', trigger: 'blur' }
        ],
        publishDate: [
          { required: true, message: '请选择发布日期', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      },
      institutions: [],
      show: false,
      book: []
    }
  },
  props: ['id'],
  components: {
    RelatedBooks
  },
  mounted () {
    this.treeList()
    if (this.id) {
      this.syNoticeInfo()
    }
  },
  methods: {
    /**
     *机构树
    */
    async treeList () {
      const res = await this.$api.systemSettings.treeList({})
      var { data } = res
      this.institutions = data
    },
    async syNoticeInfo () {
      const res = await this.$api.academy.syNoticeInfo({ id: this.id })
      var { data } = res
      this.form.noticeTitle = data.title
      this.form.sort = data.sort
      this.form.dept = data.officeId
      this.form.publishDate = data.publishDate
      this.form.content = data.content
      if (data.books.length) {
        this.book = data.books
      }
    },
    BooksClick () {
      this.show = true
    },
    callback (data, type) {
      if (data.length) {
        this.book = data
      }
      this.show = false
    },
    bookDel (item) {
      var books = this.book
      this.book = books.filter(book => book.id !== item.id)
    },
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var book = []
          this.book.forEach(item => {
            book.push(item.id)
          })
          this.generalAdd({
            // empty: '1', // 置空过滤标识
            id: this.id,
            title: this.form.noticeTitle,
            sort: this.form.sort,
            officeId: this.form.dept,
            publishDate: this.form.publishDate,
            content: this.form.content,
            bookIds: book.join(',')
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    async generalAdd (data) {
      var url = '/syNotice/add'
      if (this.id) {
        url = '/syNotice/edit'
      }
      const res = await this.$api.general.generalAdd(url, data)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        this.$emit('callback')
      }
    },
    /**
 * 取消按钮
*/
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.academyAnnouncementNew {
  width: 988px;
  .academyAnnouncementNewItemBox {
    display: flex;
    flex-wrap: wrap;
    padding-top: 12px;
    .academyAnnouncementNewItem {
      display: flex;
      justify-content: space-between;
      margin-right: 24px;
      margin-bottom: 24px;
      width: 362px;
      height: 128px;
      cursor: pointer;
      .academyAnnouncementNewItemImg {
        height: 128px;
        width: 95px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .academyAnnouncementNewItemBoxs {
        width: 252px;
        height: 100%;
        position: relative;
        .academyAnnouncementNewItemName {
          color: #333;
          line-height: 21px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-size: 16px;
          margin-bottom: 7px;
        }
        .academyAnnouncementNewItemIntroduction {
          line-height: 24px;
          color: #666;
          letter-spacing: 0.93px;
          height: 72px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          font-size: 13px;
        }
        .academyAnnouncementNewItemAuthor {
          position: absolute;
          left: 0;
          bottom: -2px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .academyAnnouncementNewItemAuthorText {
            font-size: 13px;
            color: #999;
            letter-spacing: 1px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .libraryAllItemButton {
            .el-button {
              font-size: 13px;
              padding: 0;
              span {
                display: inline-block;
                line-height: 16px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
