<template>
  <div class="vote-list">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入关键字"
                v-model="keyword"
                clearable
                @keyup.enter.native="search"></el-input>
      <el-select v-model="topic"
                 clearable
                 placeholder="题型">
        <el-option v-for="item in topicList"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="handleAdd">新增</el-button>
      <el-button type="primary"
                 @click="handleQuestionBankImport">题库导入</el-button>
      <el-button type="primary"
                 @click="isSort = true"
                 v-if="!isSort">排序</el-button>
      <el-button type="primary"
                 @click="handleSort"
                 v-if="isSort">确认排序</el-button>
      <el-button type="primary"
                 @click="handleDelete">删除</el-button>
      <el-button type="primary"
                 @click="handleExport">导出</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="排序"
                           width="120">
            <template slot-scope="scope">
              <span v-if="!isSort">{{scope.row.sort}}</span>
              <el-input v-if="isSort"
                        v-model="scope.row.sort"
                        type="number"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="题目"
                           min-width="260"
                           prop="name"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="openInfo(scope.row)">{{scope.row.name}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="题型"
                           width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.topic === 'single'">单选</span>
              <span v-if="scope.row.topic === 'multi'">多选</span>
              <span v-if="scope.row.topic === 'text'">简答</span>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="200">
            <template slot-scope="scope">
              <el-button @click="handleEdit(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="isAdd"
               :title="addTitle">
      <question-add @cancel="handleAddCancel"
                    :editData="editData"
                    :paperId="$route.query.id"></question-add>
    </zy-pop-up>
    <zy-pop-up v-model="isCheck"
               title="题库导入">
      <question-check @cancel="handleCheckCancel"
                      :paperId="$route.query.id"
                      :arr="checkArr"></question-check>
    </zy-pop-up>
    <zy-pop-up title="题目详情"
               v-model="isInfo">
      <div class="question-info">
        <p class="title">{{this.detail.name}}(
          <span v-if="this.detail.topic === 'single'">单选</span>
          <span v-if="this.detail.topic === 'multi'">多选</span>
          <span v-if="this.detail.topic === 'text'">简答</span>)
        </p>
        <div class="question-option"
             v-if="this.detail.options">
          <p v-for="(item,index) in this.detail.option"
             :key="index">{{index+1}}、{{item}}</p>
        </div>
        <div class="question-blank"
             v-if="!this.detail.options"></div>
      </div>
    </zy-pop-up>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :excelId="excelId"
                 :params="params"
                 :type="203"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
  </div>
</template>

<script>
import questionAdd from '../components/question-add'
import questionCheck from '../components/question-check'
export default {
  data () {
    return {
      keyword: '',
      isSort: false,
      isAdd: false,
      addTitle: '新增题目',
      isCheck: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      selectionList: [],
      editData: null,
      detail: {},
      isInfo: false,
      checkArr: [],
      params: null,
      exportShow: false,
      excelId: null,
      topic: '',
      topicList: [
        { value: 'single', label: '单选' },
        { value: 'multi', label: '多选' },
        { value: 'text', label: '简答' }
      ]
    }
  },
  components: { questionAdd, questionCheck },
  mounted () {
    this.getList()
  },
  methods: {
    // 列表
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        paperId: this.$route.query.id,
        module: 'questionnaire'
      }
      if (this.keyword !== '') {
        data.keyword = this.keyword
      }
      if (this.topic !== '') {
        data.topic = this.topic
      }
      this.$api.appManagement.votequestionList(data).then(res => {
        const { errcode, total, data } = res
        if (errcode === 200) {
          this.total = total
          this.tableData = data
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.type = ''
      this.topic = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 激活新增
    handleAdd () {
      this.isAdd = true
      this.addTitle = '新增'
      this.editData = null
    },
    // 编辑
    handleEdit (val) {
      this.$api.appManagement.questionInfo(val.questionId).then(res => {
        if (res.errcode === 200) {
          this.editData = res.data
          this.isAdd = true
          this.addTitle = '编辑'
        }
      })
    },
    // 激活题库导入
    handleQuestionBankImport () {
      this.isCheck = true
      this.checkArr = this.tableData
    },
    // 新增取消弹窗
    handleAddCancel (val) {
      this.editData = null
      this.isAdd = false
      if (val) {
        this.getList()
      }
    },
    // 选题取消弹窗
    handleCheckCancel (val) {
      this.isCheck = false
      if (val) {
        this.getList()
      }
    },
    // 排序
    handleSort () {
      const list = this.tableData.map(item => {
        const li = {
          sort: item.sort.toString(),
          id: item.id
        }
        return li
      })
      const s = new Set()
      list.forEach(i => s.add(i.sort))
      if (list.length !== s.size) {
        return this.$message.warning('序号有相同的项')
      }
      const data = {
        // paperId: this.$route.query.id,
        module: 'questionnaire',
        question: JSON.stringify(list)
      }
      this.$api.appManagement.voteQuestionSort(data).then(res => {
        if (res.errcode === 200) {
          this.isSort = false
          this.$message.success('修改排序成功')
          this.getList()
        }
      })
    },
    // 删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要删除的项')
      }
      this.$confirm('此操作将删除选中的题目, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectionList.map(item => item.id).join(',')
        this.$api.appManagement.voteQuestionDelete(ids, this.$route.query.id).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    // 导出
    handleExport () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.params = {
        paperId: this.$route.query.id,
        isPublic: 1
      }
      this.exportShow = true
    },
    // 打开详情
    openInfo (val) {
      this.$api.appManagement.questionInfo(val.questionId).then(res => {
        if (res.errcode === 200) {
          this.detail = res.data
          if (res.data.options) {
            this.detail.option = res.data.options.split('|')
          }
          this.isInfo = true
        }
      })
    }
  }
}
</script>

<style lang="scss">
@import "./vote-list.scss";
</style>
