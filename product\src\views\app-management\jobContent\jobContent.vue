<template>
  <div class="job-content">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2"
                       :buttonNumber="2">
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="leadingPartyGroup"
                   filterable
                   clearable
                   placeholder="请选择党组">
          <el-option v-for="item in leadingPartyGroupData"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <!-- <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:add'"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:dels'"
                   @click="deleteClick">删除</el-button> -->
        <el-button type="primary"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   @click="deleteClick">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="information-box">
      <div class="information-data-box">
        <div class="plenum-data">
          <zy-table>
            <el-table :data="tableData"
                      ref="table"
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               fixed="left"
                               width="60"></el-table-column>
              <el-table-column label="序号"
                               width="80"
                               prop="sort">
              </el-table-column>
              <el-table-column label="党组"
                              width="260">
                <template slot-scope="scope">
                  <el-button @click="details(scope.row)"
                             type="text">{{scope.row.leadingPartyGroupName}}</el-button>
                </template>
              </el-table-column>
              <el-table-column label="工作内容"
                               prop="jobContent">
              </el-table-column>
              <!-- <el-table-column label="操作"
                               fixed="right"
                               v-if="$hasPermission(['auth:zyinfodetail:edit'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button @click="modify(scope.row)"
                             v-permissions="'auth:zyinfodetail:edit'"
                             type="primary"
                             plain
                             size="mini">编辑</el-button>
                </template>
              </el-table-column> -->
              <el-table-column label="操作"
                               fixed="right"
                               width="260">
                <template slot-scope="scope">
                  <el-button @click="modify(scope.row)"
                             type="primary"
                             plain
                             size="mini">编辑</el-button>
                  <!-- <el-button @click="modifyTask(scope.row)"
                             type="primary"
                             plain
                             size="mini">增加工作任务</el-button> -->
                  <el-button @click="modifyTask(scope.row)"
                             type="primary"
                             plain
                             size="mini">工作任务</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="$pageSizes()"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>

    <zy-pop-up v-model="show"
               :title="id?'编辑':'新增'">
      <jobContentAdd :id="id"
                     @callback="addCallback"></jobContentAdd>
    </zy-pop-up>

    <!-- <zy-pop-up v-model="showTask"
               title="增加工作任务">
      <jobTaskAdd :id="id"
                  @callback="addCallbackTask"></jobTaskAdd>
    </zy-pop-up> -->

    <zy-pop-up v-model="detailsShow"
               title="详情">
      <jobContentDetails :id="id"></jobContentDetails>
    </zy-pop-up>

    <zy-pop-up v-model="showTask"
               title="工作任务">
      <taskContent :id="id" :leadingPartyGroup="leadingPartyGroup" :jobContent="jobContent"></taskContent>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import jobContentAdd from './components/jobContentAdd'
import jobContentDetails from './components/jobContentDetails'
import taskContent from './taskContent'
// import jobTaskAdd from './components/jobTaskAdd'
export default {
  name: 'jobContent',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      leadingPartyGroup: '',
      leadingPartyGroupData: [
        { id: '1', name: '政协党组' },
        { id: '2', name: '机关党组' },
        { id: '3', name: '分党组' },
        { id: '4', name: '党总支、各支部' }
      ],
      id: '',
      show: false,
      detailsShow: false,
      showTask: false,
      jobContent: ''
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    jobContentAdd,
    jobContentDetails,
    taskContent
  },
  mounted () {
    this.informationList()
  },
  methods: {
    search () {
      this.page = 1
      this.informationList('search')
    },
    reset () {
      this.keyword = ''
      this.leadingPartyGroup = ''
      this.informationList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    modifyTask (row) {
      this.id = row.id
      this.leadingPartyGroup = row.leadingPartyGroup
      this.jobContent = row.jobContent
      this.showTask = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    addCallback () {
      this.informationList()
      this.show = false
    },
    addCallbackTask () {
      this.informationList()
      this.showTask = false
    },
    choiceClick (item) {
      this.informationList()
    },
    async informationList (type) {
      var leadingPartyGroup = ''
      if (type === 'search') {
        leadingPartyGroup = this.leadingPartyGroup
      }
      const res = await this.$api.appManagement.jobcontentList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        leadingPartyGroup: leadingPartyGroup,
        orderBy: 'leading_Party_group,sort',
        type: 1
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.informationList()
    },
    whatPage (val) {
      this.informationList()
    },
    deleteClick (row) {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的资料, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationListDel(this.choose.join(',')).then(res => {
            this.informationList()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationListDel (id) {
      const res = await this.$api.appManagement.jobcontentDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.job-content {
  width: 100%;
  height: 100%;

  .information-box {
    height: calc(100% - 64px);
    width: 100%;
    display: flex;

    .information-data-box {
      width: 100%;
      height: 100%;

      .plenum-data {
        height: calc(100% - 52px);
        width: 100%;
      }
    }
  }
}
</style>
