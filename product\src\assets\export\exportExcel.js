export function parseTime (time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  // eslint-disable-next-line
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  // eslint-disable-next-line
  return time_str
}

export function json2excel ({ fileName, excelHead, list, merges, filename }) {
  import('@/assets/export/Export-Excel').then(excel => {
    const styleArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    const length = Object.keys(excelHead).length
    const tHeader = excelHead['head' + (length - 1)]
    const styleList = []
    for (let index = 0; index < length; index++) {
      for (let i = 0; i < styleArr.length; i++) {
        styleList.push(styleArr[i] + (index + 1))
      }
      for (let i = 0; i < styleArr.length; i++) {
        styleList.push(styleArr[0] + styleArr[i] + (index + 1))
      }
    }
    var multiHeader = [excelHead.head]
    const data = formatJson(fileName, list)
    excel.export_json_to_excel({
      data, // 数据
      merges, // 合并
      styleList, // 样式
      multiHeader, // 表头
      header: tHeader, // 表头
      filename: filename // 文件名
    })
  })
}
// 数据过滤，时间过滤
function formatJson (filterVal, jsonData) {
  return jsonData.map(v =>
    filterVal.map(j => {
      if (j === 'timestamp') {
        return parseTime(v[j])
      } else {
        return v[j]
      }
    })
  )
}
