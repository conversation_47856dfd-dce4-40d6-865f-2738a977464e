const currentMember = () => import('@/views/memberInformation/generalCurrentMember/currentMember')
const nationalCurrentMember = () => import('@/views/memberInformation/generalCurrentMember/nationalCurrentMember')
const beforeMember = () => import('@/views/memberInformation/generalBeforeMember/beforeMember')
const nationalBeforeMember = () => import('@/views/memberInformation/generalBeforeMember/nationalBeforeMember')
const memberStatistical = () => import('@/views/memberInformation/generalMemberStatistical/memberStatistical')
const nationalMemberStatistical = () => import('@/views/memberInformation/generalMemberStatistical/nationalMemberStatistical')
const memberNew = () => import('@/views/memberInformation/generalCurrentMember/memberNew/memberNew')
const memberDetails = () => import('@/views/memberInformation/generalCurrentMember/memberDetails/memberDetails')
const presentCurrentMember = () => import('@/views/memberInformation/generalCurrentMember/presentCurrentMember')
const memberAudit = () => import('@/views/memberInformation/memberAudit/memberAudit')
const memberStatisticalList = () => import('@/views/memberInformation/generalMemberStatistical/memberStatisticalList')
const nationalMemberStatisticalList = () => import('@/views/memberInformation/generalMemberStatistical/nationalMemberStatisticalList')
const memberInformation = [
  { // 本届委员
    path: '/currentMember',
    name: 'currentMember',
    component: currentMember
  },
  { // 本届全国委员
    path: '/nationalCurrentMember',
    name: 'nationalCurrentMember',
    component: nationalCurrentMember
  },
  { // 历届委员
    path: '/beforeMember',
    name: 'beforeMember',
    component: beforeMember
  },
  { // 历届全国委员
    path: '/nationalBeforeMember',
    name: 'nationalBeforeMember',
    component: nationalBeforeMember
  },
  { // 委员统计
    path: '/memberStatistical',
    name: 'memberStatistical',
    component: memberStatistical
  },
  { // 全国委员统计
    path: '/nationalMemberStatistical',
    name: 'nationalMemberStatistical',
    component: nationalMemberStatistical
  },
  { // 委员统计
    path: '/memberStatisticalList',
    name: 'memberStatisticalList',
    component: memberStatisticalList
  },
  { // 全国委员统计
    path: '/nationalMemberStatisticalList',
    name: 'nationalMemberStatisticalList',
    component: nationalMemberStatisticalList
  },
  { // 代表信息审核
    path: '/memberAudit',
    name: 'memberAudit',
    component: memberAudit
  },
  { // 代表信息审核
    path: '/memberNew',
    name: 'memberNew',
    component: memberNew
  },
  { // 代表信息审核
    path: '/memberDetails',
    name: 'memberDetails',
    component: memberDetails
  },
  { // 代表信息审核
    path: '/presentCurrentMember',
    name: 'presentCurrentMember',
    component: presentCurrentMember
  }
]
export default memberInformation
