// 导入封装的方法
import {
  post,
  exportFile,
  fileRequest,
  postform
} from '../http'

const Communication = {

  uploadFile (params) {
    return postform('/attachment/uploadFile', params, { timeout: 80000 })
  },
  browsesave (params) { // 浏览新增
    return post('/browse/save', params)
  },
  materiallist (params) { // 文件资料下载
    return post('/material/list', params)
  },
  materialadd (url, params) { // 文件资料薪
    return post(`/material/${url}`, params)
  },
  materialinfo (params) { // 文件资料薪
    return post(`/material/info/${params}`)
  },
  materialdel (params) { // 文件资料薪
    return post('/material/dels/', params)
  },
  findDrafts (params) { // 草稿箱
    return post('/notice/findDrafts', params)
  },
  findInternalInfos (params) { // 发件箱
    return post('/notice/findInternalInfos', params)
  },
  findAdminInternalInfos (params) { // 发件箱
    return post('/notice/findAdminInternalInfos', params)
  },
  findInboxs (params) { // 收件箱
    return post('/notice/findInboxs', params)
  },
  findRecycleBins (params) { // 回收站
    return post('/notice/findRecycleBins', params)
  },
  addInternalInfo (url, params) { // 新增内部消息
    return post(`/notice/${url}`, params)
  },
  updateDraftOrRecycleBin (params) { // 新增内部消息
    return post('/notice/updateDraftOrRecycleBin', params)
  },
  noticeinfo (params) { //  详情
    return post(`/notice/info/${params}`)
  },
  //  海南分组管理
  findContactGroups (params) { //  分组列表
    return post('contacts/findContactGroups', params)
  },
  getContactGroupDetail (params) { //  分组详情
    return post('contacts/getContactGroupDetail', params)
  },
  delContactGroup (params) { //  删除分组
    return post('/contacts/delContactGroup', params)
  },
  addContactGroups (url, params) { //  新增修改分组
    return post(url, params)
  },
  contactslist (params) { // 通讯录列表
    return post('/contacts/list', params)
  },
  contactsinfo (params) { // 获取通讯录详情
    return post('/contacts/info/' + params)
  },
  contactsdel (params) { // 删除
    return post('/contacts/dels', params)
  },
  contactsadd (url, params) {
    return post(url, params)
  },
  contactsimportemplate () {
    return exportFile('/contacts/importemplate')
  },
  contactsimpor (params, text) {
    return fileRequest('/contacts/import', params, text)
  }

}
export default Communication
