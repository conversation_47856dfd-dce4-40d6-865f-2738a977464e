<template>
  <div class="poll-count">
    <div class="button-box">
      <el-button type="primary"
                 @click="handleExport">导出</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  ref="table"
                  slot="zytable">
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="选项"
                           width="420"
                           prop="optionName"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="投票人数"
                           align="center"
                           width="120">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="openDetail(scope.row.optionId)">{{scope.row.num}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <zy-pop-up v-model="isMan"
               title="投票人数统计">
      <optionsMan :id="id"></optionsMan>
    </zy-pop-up>
  </div>
</template>

<script>
import optionsMan from './widget/options-man.vue'
export default {
  components: {
    optionsMan
  },
  data () {
    return {
      list: [],
      isMan: false,
      id: null
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.$api.appManagement.pollOptionsCount(this.$route.query.id).then(res => {
        this.list = res.data
      })
    },
    /* TDD */
    handleExport () {
      this.$message.warning('等待后续开发')
    },
    openDetail (id) {
      this.id = id
      this.isMan = true
    }
  }

}
</script>
<style lang="scss" scoped>
.poll-count {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 64px);
    width: 100%;
  }
}
</style>
