<template>
  <div class="informationList">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2"
                       :buttonNumber="2">
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="stateId"
                   filterable
                   clearable
                   placeholder="请选择审核状态">
          <el-option v-for="item in state"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="infoClass"
                   filterable
                   clearable
                   placeholder="请选择资讯类型">
          <el-option v-for="item in infoClassData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="infoType"
                   filterable
                   clearable
                   placeholder="请选择显示类型">
          <el-option v-for="item in infoTypeData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:add'"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:dels'"
                   @click="deleteClick">删除</el-button>
        <el-button v-permissions="'auth:zyinfodetail:batchUpdate'"
                   @click="passClick(1)">审核通过</el-button>
        <el-button v-permissions="'auth:zyinfodetail:batchUpdate'"
                   @click="passClick(2)">审核不通过</el-button>
      </template>
    </xyl-search-button>
    <div class="information-box">
      <div class="information-tree-box">
        <div class="information-tree-text">选择栏目</div>
        <div class="information-tree">
          <zy-tree :tree="tree"
                   v-model="treeId"
                   :props="{ children: 'children', label: 'name'}"
                   @on-tree-click="choiceClick"></zy-tree>
        </div>
      </div>
      <div class="information-data-box">
        <div class="informationList">
          <zy-table>
            <el-table :data="tableData"
                      ref="table"
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               fixed="left"
                               width="60"></el-table-column>
              <el-table-column label="序号"
                               width="80"
                               prop="sort">
              </el-table-column>
              <el-table-column label="标题"
                               min-width="260"
                               show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-button @click="details(scope.row)"
                             type="text">{{scope.row.title}}</el-button>
                </template>
              </el-table-column>
              <el-table-column label="所属栏目"
                               width="160"
                               prop="structureName">
              </el-table-column>
              <el-table-column label="资讯类型"
                               width="90"
                               prop="infoClass">
              </el-table-column>
              <el-table-column label="显示类型"
                               width="90"
                               prop="infoType">
              </el-table-column>
              <el-table-column label="来源"
                               width="160"
                               prop="source">
              </el-table-column>
              <!-- <el-table-column label="官网推送"
                               width="90"
                               prop="isOfficialWebsitePush">
              </el-table-column> -->
              <el-table-column label="是否置顶"
                               width="90">
                <template slot-scope="scope">
                  <div>{{scope.row.isTop==1?'置顶':'不置顶'}}</div>
                </template>
              </el-table-column>
              <el-table-column label="发布人"
                               width="160"
                               prop="createBy">
              </el-table-column>
              <el-table-column label="发布时间"
                               width="190"
                               prop="publishDate">
              </el-table-column>
              <el-table-column label="审核状态"
                               width="120"
                               prop="auditingFlag">
              </el-table-column>
              <el-table-column label="资讯关联"
                               v-if="$hasPermission(['auth:inforelation:list'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button @click="associated(scope.row)"
                             type="text">资讯关联</el-button>
                </template>
              </el-table-column>
              <el-table-column label="组图"
                               v-if="$hasPermission(['auth:zyinforeportpic:pic:list'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button :disabled="scope.row.canGroupPicOption!=1"
                             @click="mosaic(scope.row)"
                             type="text">组图</el-button>
                </template>
              </el-table-column>
              <el-table-column label="报道图片"
                               v-if="$hasPermission(['auth:zyinforeportpic:scrolling:report:list','auth:zyinforeportpic:scrolling:pic:list'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button :disabled="scope.row.canScrollReportPicOption!=1"
                             @click="rolling(scope.row)"
                             type="text">{{scope.row.infoClass}}</el-button>
                </template>
              </el-table-column>
              <el-table-column label="评论回复"
                               v-if="$hasPermission(['auth:comment:list'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button :disabled="scope.row.commentCount==0"
                             @click="commentCount(scope.row)"
                             type="text">{{scope.row.commentCount}}</el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作"
                               fixed="right"
                               v-if="$hasPermission(['auth:zyinfodetail:edit'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button @click="modify(scope.row)"
                             v-permissions="'auth:zyinfodetail:edit'"
                             type="primary"
                             plain
                             size="mini">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="$pageSizes()"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>

    <xyl-popup-window v-model="show"
                      :title="id?'编辑':'新增'">
      <information-add :id="id"
                       :module="module"
                       :parentId="treeId"
                       @callback="addCallback"></information-add>
    </xyl-popup-window>

    <xyl-popup-window v-model="detailsShow"
                      title="详情">
      <information-details :id="id"></information-details>
    </xyl-popup-window>

    <xyl-popup-window v-model="mosaicShow"
                      title="组图">
      <information-mosaic :id="id"></information-mosaic>
    </xyl-popup-window>
    <xyl-popup-window v-model="pictureShow"
                      title="滚动图片">
      <information-picture :id="id"></information-picture>
    </xyl-popup-window>
    <xyl-popup-window v-model="reportsShow"
                      title="滚动报道">
      <information-reports :id="id"></information-reports>
    </xyl-popup-window>
    <xyl-popup-window v-model="associatedShow"
                      title="关联资讯">
      <associated-information :id="id"></associated-information>
    </xyl-popup-window>

    <!-- <xyl-popup-window v-model="pushShow"
               title="推送">
      <PushWebsite :id="ids"
                   @callback="callback"></PushWebsite>
    </xyl-popup-window>
    <xyl-popup-window v-model="CancelPushShow"
               title="取消推送">
      <CancelPushWebsite @callback="callback"></CancelPushWebsite>
    </xyl-popup-window>

    <xyl-popup-window v-model="deleteShow"
               title="同步删除">
      <DeletePushWebsite :id="ids"
                         @callback="deleteCallback"></DeletePushWebsite>
    </xyl-popup-window> -->
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import informationAdd from './components/informationAdd'
import informationMosaic from './components/informationMosaic/informationMosaic'
import informationPicture from './components/informationPicture/informationPicture'
import informationReports from './components/informationReports/informationReports'
import associatedInformation from './components/associatedInformation/associatedInformation'
import informationDetails from './components/informationDetails'
// import PushWebsite from '../../components/PushWebsite'
// import CancelPushWebsite from '../../components/CancelPushWebsite'
// import DeletePushWebsite from '../../components/DeletePushWebsite'
export default {
  name: 'informationList',
  data () {
    return {
      module: 1,
      keyword: '',
      treeId: '1',
      tree: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      infoClass: '',
      infoType: '',
      stateId: '',
      state: [],
      infoClassData: [],
      infoTypeData: [],
      id: '',
      show: false,
      mosaicShow: false,
      pictureShow: false,
      reportsShow: false,
      associatedShow: false,
      detailsShow: false,
      ids: '',
      pushShow: false,
      CancelPushShow: false,
      deleteShow: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    informationAdd,
    informationMosaic,
    informationPicture,
    informationReports,
    associatedInformation,
    informationDetails
    // PushWebsite,
    // CancelPushWebsite,
    // DeletePushWebsite
  },
  mounted () {
    if (this.$route.query.module) {
      this.module = this.$route.query.module
    }
    this.dictionaryPubkvs()
    this.informationList()
    this.informationColumnTree()
    if (this.$route.params.operation === 'new') {
      this.show = true
    }
  },
  methods: {
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'zy_info_type,auditing_flag,info_show_type'
      })
      var { data } = res
      this.infoClassData = data.zy_info_type
      this.infoTypeData = data.info_show_type
      this.state = data.auditing_flag
    },
    search () {
      this.page = 1
      this.informationList()
    },
    reset () {
      this.keyword = ''
      this.infoClass = ''
      this.infoType = ''
      this.stateId = ''
      this.informationList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    pushClick () {
      if (this.choose.length) {
        this.$confirm('此操作将会把选择的资讯推送到官网, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.ids = this.choose.join(',')
          this.pushShow = true
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    CancelPushClick () {
      this.CancelPushShow = true
    },
    callback () {
      this.pushShow = false
      this.CancelPushShow = false
    },
    /**
     * 打开评论 5
     */
    commentCount (row) {
      this.newTab({ name: '评论回复', menuId: row.id, to: '/comments', params: { keyId: row.id, type: '5' } })
    },
    associated (row) {
      this.id = row.id
      this.associatedShow = true
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    addCallback () {
      this.informationList()
      this.show = false
    },
    rolling (row) {
      this.id = row.id
      if (row.infoClass === '滚动报道') {
        this.reportsShow = true
      } else if (row.infoClass === '滚动图片') {
        this.pictureShow = true
      }
    },
    passClick (auditingFlag) {
      if (this.choose.length) {
        this.$confirm(`此操作将选择的资讯的状态改为${auditingFlag === 1 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationBatchUpdate(this.choose.join(','), auditingFlag)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationBatchUpdate (id, auditingFlag) {
      const res = await this.$api.appManagement.informationBatchUpdate({ ids: id, auditingFlag: auditingFlag })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: this.module })
      var { data } = res
      var arr = [{ children: [], id: '1', name: '所有' }]
      this.tree = arr.concat(data)
    },
    choiceClick (item) {
      this.informationList()
    },
    async informationList () {
      const res = await this.$api.appManagement.informationList({
        module: this.module,
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        structureId: this.treeId,
        infoClass: this.infoClass,
        infoType: this.infoType,
        auditingFlag: this.stateId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    mosaic (row) {
      this.id = row.id
      this.mosaicShow = true
    },
    mosaicCallback () {
      this.informationList()
      this.mosaicShow = false
    },
    howManyArticle (val) {
      this.informationList()
    },
    whatPage (val) {
      this.informationList()
    },
    deleteClick (row) {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的资讯, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // this.officeList(this.choose.join(','))
          this.informationListDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationListDel (id) {
      const res = await this.$api.appManagement.informationListDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async officeList () {
      const res = await this.$api.appManagement.officeList({
        pageNo: 1,
        pageSize: this.$pageSize(),
        detailIds: this.id
      })
      var { data } = res
      if (data.length) {
        this.$confirm('当前检查到删除的数据有推送至官网的资讯, 是否同步删除?', '提示', {
          confirmButtonText: '同步删除',
          cancelButtonText: '不删除',
          type: 'warning'
        }).then(() => {
          this.ids = this.choose.join(',')
          this.deleteShow = true
        }).catch(() => {
          this.informationListDel(this.choose.join(','))
        })
      } else {
        this.informationListDel(this.choose.join(','))
      }
    },
    deleteCallback () {
      this.deleteShow = false
      this.informationListDel(this.choose.join(','))
    }
  }
}
</script>
<style lang="scss">
.informationList {
  width: 100%;
  height: 100%;

  .information-box {
    height: calc(100% - 64px);
    width: 100%;
    display: flex;

    .information-tree-box {
      width: 222px;
      height: 100%;

      .zy-tree {
        width: 222px;
        min-width: 222px;
      }

      .information-tree-text {
        height: 52px;
        line-height: 52px;
        padding-left: 24px;
        font-size: 16px;
        background-color: #e6e5e8;
      }

      .information-tree {
        height: calc(100% - 52px);
      }
    }

    .information-data-box {
      width: calc(100% - 222px);
      height: 100%;

      .informationList {
        height: calc(100% - 52px);
        width: 100%;
      }
    }
  }
}
</style>
