<template>
  <div class="customTopicAdd">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="标题"
                    prop="title"
                    class="form-title">
        <el-input placeholder="请输入标题"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="所属栏目"
                    class="form-input">
        <zy-select width="296"
                   node-key="id"
                   :props="{children: 'children',label: 'name'}"
                   v-model="form.structureId"
                   :data="structureIdData"
                   placeholder="请选择所属栏目"></zy-select>
      </el-form-item>
      <el-form-item label="发布时间"
                    prop="publishTime"
                    class="form-input">
        <el-date-picker v-model="form.publishTime"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input">
        <el-input-number style="width:100%;"
                         placeholder="请输入排序"
                         v-model="form.sort"
                         clearable>
        </el-input-number>
      </el-form-item>
      <el-form-item label="封面图"
                    class="form-img">
        <el-upload class="form-img-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="coverUpload"
                   :show-file-list="false">
          <img v-if="coverImg.filePath"
               :src="coverImg.filePath"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="主题图"
                    class="form-img">
        <el-upload class="form-img-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="themeUpload"
                   :show-file-list="false">
          <img v-if="themeImg.filePath"
               :src="themeImg.filePath"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="是否发布"
                    class="form-input">
        <el-radio-group v-model="form.isPublish">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否置顶"
                    class="form-input">
        <el-radio-group v-model="form.isTop">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-ue">
        <UEditor v-model="form.content"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'customTopicAdd',
  data () {
    return {
      form: {
        title: '',
        structureId: '',
        publishTime: '',
        isPublish: '1',
        sort: '',
        isTop: '0',
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        publishTime: [
          { required: true, message: '选择发布时间', trigger: 'blur' }
        ]
      },
      structureIdData: [],
      coverImg: [],
      themeImg: {}
    }
  },
  props: ['id', 'module', 'columnModule'],
  mounted () {
    this.informationColumnTree()
    if (this.id) {
      this.customTopicListInfo()
    } else {
      this.form.publishTime = this.$format(new Date(), 'YYYY-MM-DD HH:mm:ss')
    }
  },
  methods: {
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: this.columnModule })
      var { data } = res
      this.structureIdData = data
    },
    async customTopicListInfo () {
      const res = await this.$api.appManagement.customTopicListInfo(this.id)
      var { data } = res
      this.form.title = data.title
      this.form.publishTime = data.publishTime
      this.form.sort = data.sort
      this.form.isTop = data.isTop
      this.form.isPublish = data.isPublish
      this.form.content = data.content
      this.form.content = data.content
      this.form.structureId = data.structureId
      if (data.coverImg) {
        data.coverImg.filePath = data.coverImg.fullUrl
        this.coverImg = data.coverImg
      }
      if (data.themeImg) {
        data.themeImg.filePath = data.themeImg.fullUrl
        this.themeImg = data.themeImg
      }
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    themeUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.themeImg = data[0]
      }).catch(() => {
        files.onError()
      })
    },
    coverUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.coverImg = data[0]
      }).catch(() => {
        files.onError()
      })
    },
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // if (JSON.stringify(this.coverImg) === '{}') {
          //   this.$message({
          //     message: '请先上传封面图再新增！',
          //     type: 'warning'
          //   })
          //   return
          // }
          // if (JSON.stringify(this.themeImg) === '{}') {
          //   this.$message({
          //     message: '请先上传主题图再新增！',
          //     type: 'warning'
          //   })
          //   return
          // }
          var url = '/specialsubjectinfo/add'
          if (this.id) {
            url = '/specialsubjectinfo/edit'
          }
          this.$api.general.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            title: this.form.title,
            publishTime: this.form.publishTime,
            isPublish: this.form.isPublish,
            sort: this.form.sort,
            isTop: this.form.isTop,
            content: this.form.content,
            themeImg: this.themeImg.id,
            coverImg: this.coverImg.id,
            structureId: this.form.structureId,
            module: this.module
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.customTopicAdd {
  width: 988px;

  .form-img {
    width: 296px;

    .form-img-uploader {
      width: 296px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 296px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 296px;
        height: 128px;
        display: block;
      }
    }
  }

  .form-img + .form-img {
    margin-right: 296px;
  }
}
</style>
