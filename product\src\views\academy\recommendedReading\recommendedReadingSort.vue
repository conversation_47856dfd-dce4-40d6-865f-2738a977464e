<template>
  <div class="recommendedReadingSort">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="newData">刷新</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="排序"
                           width="100"
                           prop="sort"></el-table-column>
          <el-table-column label="推荐阅读内容"
                           min-width="220"
                           prop="recommentContent"></el-table-column>
          <el-table-column label="操作"
                           width="160"
                           fixed="right">
            <template slot-scope="scope">
              <el-button @click="editor(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑排序</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="show"
                      :title="id==''?'新增混合排序':'编辑混合排序'">
      <recommendedReadingSortAdd :id="id"
                                 :sort="sort"
                                 @callback="newCallback"> </recommendedReadingSortAdd>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import recommendedReadingSortAdd from './recommendedReadingSortAdd'
export default {
  name: 'recommendedReadingSort',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      sort: '',
      show: false
    }
  },
  mixins: [tableData],
  components: {
    recommendedReadingSortAdd
  },
  mounted () {
    this.recommendsortList()
  },
  methods: {
    search () {
      this.page = 1
      this.recommendsortList()
    },
    reset () {
      this.keyword = ''
      this.recommendsortList()
    },
    newData () {
      this.$confirm('此操作将刷新混合排序, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.recommendsortFlush()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消刷新'
        })
      })
    },
    async recommendsortFlush () {
      const res = await this.$api.academy.recommendsortFlush()
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.search()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    editor (row) {
      this.id = row.id
      this.sort = row.sort
      this.show = true
    },
    newCallback () {
      this.recommendsortList()
      this.show = false
    },
    async recommendsortList () {
      const res = await this.$api.academy.recommendsortList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.recommendsortList()
    },
    whatPage (val) {
      this.recommendsortList()
    }
  }
}
</script>
<style lang="scss">
.recommendedReadingSort {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
