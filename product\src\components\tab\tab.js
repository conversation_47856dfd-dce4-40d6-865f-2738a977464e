import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
const delay = (function () {
  let timer = 0
  return function (callback, ms) {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
export default {
  name: 'XylTab',
  props: {
    value: [String, Number, Array, Object],
    distance: {
      type: Number,
      default: 168
    }
  },
  render () {
    const prev = this.prevShow ? (<div class="xyl-tab-prev" on-click={this.prevClick}><i class="el-icon-d-arrow-left"></i></div>) : null
    const next = this.nextShow ? (<div class="xyl-tab-next" on-click={this.nextClick}><i class="el-icon-d-arrow-right"></i></div>) : null
    return (<div class="xyl-tab" ref='tab'>
      <div class="xyl-tab-wrap">
        <div class="xyl-tab-scroll" ref='scroll' style={this.scrollStyle}>{this.$slots.default}</div>
        {prev}{next}
      </div>
    </div>)
  },
  computed: {
    panesActive: {
      get () {
        let active = null
        this.panes.forEach(item => {
          if (this.value === item.value) {
            active = item
          }
        })
        return active
      }
    },
    scrollStyle: {
      get () {
        console.log(this.scrollLeft)
        const style = {}
        const transform = `translateX(${this.scrollLeft}px)`
        style.transform = transform
        style.msTransform = transform
        style.webkitTransform = transform
        return style
      }
    }
  },
  data () {
    return {
      panes: [],
      prevShow: false,
      nextShow: false,
      scrollLeft: 0
    }
  },
  mounted () {
    this.$nextTick(() => {
      const that = this
      erd.listenTo(that.$refs.tab, (element) => {
        that.$nextTick(() => {
          that.scrollLeft = 0
          that.tabUpdateRef(that.panesActive)
        })
      })
    })
    this.calcPaneInstances()
    this.obtainActive()
  },
  updated () {
    this.calcPaneInstances()
  },
  watch: {
    value (val) {
      this.obtainActive()
    }
  },
  methods: {
    calcPaneInstances (isForceUpdate = false) {
      if (this.$slots.default) {
        const paneSlots = this.$slots.default.filter(vnode => vnode.tag && vnode.componentOptions && vnode.componentOptions.Ctor.options.name === 'XylTabItem')
        const panes = paneSlots.map(({ componentInstance }) => componentInstance)
        const panesChanged = !(panes.length === this.panes.length && panes.every((pane, index) => pane === this.panes[index]))
        if (isForceUpdate || panesChanged) {
          this.panes = panes
        }
      } else if (this.panes.length !== 0) {
        this.panes = []
      }
    },
    prevClick () {
      const left = this.$refs.tab.offsetWidth - this.$refs.scroll.offsetWidth
      this.scrollLeft = this.scrollLeft + this.distance > 0 ? 0 : this.scrollLeft + this.distance
      delay(() => {
        this.prevShow = this.scrollLeft !== 0
        this.nextShow = this.scrollLeft !== left
      }, 520)
    },
    nextClick () {
      const left = this.$refs.tab.offsetWidth - this.$refs.scroll.offsetWidth
      this.scrollLeft = this.scrollLeft - this.distance < left ? left : this.scrollLeft - this.distance
      delay(() => {
        this.prevShow = this.scrollLeft !== 0
        this.nextShow = this.scrollLeft !== left
      }, 520)
    },
    obtainActive () {
      if (this.panesActive) {
        this.$nextTick(() => {
          this.tabUpdateRef(this.panesActive)
        })
        return
      }
      setTimeout(() => {
        this.obtainActive()
      }, 520)
    },
    tabUpdateRef (e) {
      if (!e) {
        return
      }
      const left = this.$refs.tab.offsetWidth - this.$refs.scroll.offsetWidth
      const width = this.$refs.tab.offsetWidth
      const offset = e.$el.offsetLeft
      if (this.$refs.tab.offsetWidth > this.$refs.scroll.offsetWidth) {
        this.scrollLeft = 0
        delay(() => {
          this.prevShow = false
          this.nextShow = false
        }, 520)
        return
      }
      if (width - (offset + e.$el.offsetWidth) <= this.scrollLeft || width - (offset + e.$el.offsetWidth) - 222 <= this.scrollLeft) {
        var is = width - (offset + e.$el.offsetWidth) - 222
        if (width - (offset + e.$el.offsetWidth) - 222 <= left) {
          is = width - (offset + e.$el.offsetWidth)
        }
        this.scrollLeft = width - (offset + e.$el.offsetWidth) - 222 <= left ? left : is
        delay(() => {
          this.prevShow = this.scrollLeft !== 0
          this.nextShow = this.scrollLeft !== left
        }, 520)
      }
      if (-offset > this.scrollLeft || -offset + 222 > this.scrollLeft) {
        var negative = -offset + 222
        if (-offset + 222 > 0) {
          negative = 0
        }
        this.scrollLeft = -offset > 0 ? 0 : negative
        delay(() => {
          this.prevShow = this.scrollLeft !== 0
          this.nextShow = this.scrollLeft !== left
        }, 520)
      }
    }
  }
}
