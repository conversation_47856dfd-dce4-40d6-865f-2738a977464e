const workstationNew = [
  { // 工作站统计
    path: '/workstationStatistics',
    name: 'workstationStatistics',
    component: () => import('@/views/workstationNew/workstationStatistics/workstationStatistics')
  },
  { // 工作站首页
    path: '/workstationIndex',
    name: 'workstationIndex',
    component: () => import('@/views/workstationNew/workstationIndex/workstationIndex')
  },
  { // 工作站管理
    path: '/workstationSupervise',
    name: 'workstationSupervise',
    component: () => import('@/views/workstationNew/workstationSupervise/workstationSupervise')
  },
  { // 工作站工作人员管理
    path: '/workstationPersonnel',
    name: 'workstationPersonnel',
    component: () => import('@/views/workstationNew/workstationPersonnel/workstationPersonnel')
  },
  { // 代表查询
    path: '/workstationStatisticsQuery',
    name: 'workstationStatisticsQuery',
    component: () => import('@/views/workstationNew/workstationStatistics/workstationStatisticsQuery/workstationStatisticsQuery')
  },
  { // 代表管理
    path: '/workstationRepresentative',
    name: 'workstationRepresentative',
    component: () => import('@/views/workstationNew/workstationRepresentative/workstationRepresentative')
  },
  { // 代表管理新增
    path: '/workstationRepresentativeAdd',
    name: 'workstationRepresentativeAdd',
    component: () => import('@/views/workstationNew/workstationRepresentative/workstationRepresentativeAdd')
  },
  { // 代表管理详情
    path: '/workstationRepresentativeDetails',
    name: 'workstationRepresentativeDetails',
    component: () => import('@/views/workstationNew/workstationRepresentative/workstationRepresentativeDetails')
  },
  { // 信息发布管理
    path: '/workstationRelease',
    name: 'workstationRelease',
    component: () => import('@/views/workstationNew/workstationRelease/workstationRelease')
  },
  { // 活动管理
    path: '/workstationActivities',
    name: 'workstationActivities',
    component: () => import('@/views/workstationNew/workstationActivities/workstationActivities')
  },
  { // 群众意见管理
    path: '/workstationCivilian',
    name: 'workstationCivilian',
    component: () => import('@/views/workstationNew/workstationCivilian/workstationCivilian')
  },
  { // 主题图管理
    path: '/workstationTheme',
    name: 'workstationTheme',
    component: () => import('@/views/workstationNew/workstationTheme/workstationTheme')
  },
  { // 信息报送
    path: '/workstationSubmitted',
    name: 'workstationSubmitted',
    component: () => import('@/views/workstationNew/workstationSubmitted/workstationSubmitted')
  },
  { // 站点统计
    path: '/worksiteStatistics',
    name: 'worksiteStatistics',
    component: () => import('@/views/workstationNew/worksiteStatistics/worksiteStatistics')
  }
]
export default workstationNew
