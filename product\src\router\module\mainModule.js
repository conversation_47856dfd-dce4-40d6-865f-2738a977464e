// 表头字段
const excelHeaderFields = () => import('@/views/mainModule/components/excelConfiguration/excelHeaderFields')
// 模块表头
const moduleHeader = () => import('@/views/mainModule/components/excelConfiguration/moduleHeader')
// 办理单位管理
const handleUnit = () => import('@/views/mainModule/components/handleUnit/handleUnit')
// 查看办理单位用户
const handleUnitUser = () => import('@/views/mainModule/components/handleUnit/handleUnitUser')
// 流程配置
const processConfiguration = () => import('@/views/mainModule/components/processConfiguration/processConfiguration')
const mainModule = [
  { // excel表头字段
    path: '/excelHeaderFields',
    name: 'excelHeaderFields',
    component: excelHeaderFields
  },
  { // excel按模块关联表头字段
    path: '/moduleHeader',
    name: 'moduleHeader',
    component: moduleHeader
  },
  { // 办理单位管理
    path: '/handleUnit',
    name: 'handleUnit',
    component: handleUnit
  },
  { // 查看办理单位用户
    path: '/handleUnitUser',
    name: 'handleUnitUser',
    component: handleUnitUser
  },
  { // 流程配置
    path: '/processConfiguration',
    name: 'processConfiguration',
    component: processConfiguration
  }
]
export default mainModule
