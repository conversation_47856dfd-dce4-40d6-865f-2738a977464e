<template>
  <div class="zy-upload">
    <el-upload action="#"
               :class="{ uploadSty:showBtn,disUploadSty:!showBtn}"
               :multiple="multiple"
               :limit="limit"
               list-type="picture-card"
               accept=".jpg,.jpeg,.png,.PNG,.JPG"
               :on-change="handleChange"
               :http-request="customUpload"
               :before-upload="beforeAvatarUpload"
               :file-list="filelist">
      <i slot="default"
         class="el-icon-plus"></i>
      <div slot="file"
           slot-scope="{file}">
        <img class="el-upload-list__item-thumbnail"
             :src="file.url"
             alt="">
        <span class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview"
                @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span class="el-upload-list__item-delete"
                @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </span>
      </div>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible"
               append-to-body>
      <img width="100%"
           :src="dialogImageUrl"
           alt="">
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'zyUpload',
  props: {
    value: [Array],
    limit: {
      type: Number,
      default: 1
    },
    multiple: {
      type: Boolean,
      default: false
    },
    module: {
      type: String,
      default: 'minisuggestion'
    }
  },
  model: {
    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg
    event: 'file'// 这个字段，是指父组件监听 parent-event 事件
  },
  watch: {
    value (val) {
      if (val) {
        this.filelist = val
        this.showBtn = this.filelist.length < this.limit
      } else {
        this.filelist = []
      }
    },
    filelist (val) {
      this.$emit('file', val)
    }
  },
  data () {
    return {
      dialogVisible: false,
      dialogImageUrl: '',
      filelist: this.value,
      showBtn: true
    }
  },
  methods: {
    // 图片预览
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 移除图片
    handleRemove (file) {
      // 存在浅拷贝 所以不需要再赋值
      const arr = this.filelist
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].uid === file.uid) {
          arr.splice(i, 1)
        }
      }
      this.showBtn = this.filelist.length < this.limit
    },
    // 校验文件类型和文件大小
    beforeAvatarUpload (file) {
      // const isJPG = file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 10
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      if (testmsg !== 'png' && testmsg !== 'jpg' && testmsg !== 'PNG' && testmsg !== 'jpeg' && testmsg !== 'JPG') {
        this.$message.error('图片文件格式暂时不支持!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10MB!')
        return false
      }
      return isLt2M && testmsg
    },
    // 上传逻辑
    async customUpload (file) {
      const siteId = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId
      const formData = new FormData()
      formData.append('attachment', file.file)
      formData.append('module', this.module)
      formData.append('siteId', siteId)
      this.$api.mainModule.uploadFile(formData).then(res => {
        const { errcode, data } = res
        if (errcode === 200) {
          const fileData = {
            name: data[0].fileName,
            size: data[0].fileSize,
            type: data[0].fileType,
            url: data[0].filePath,
            id: data[0].id,
            uid: data[0].uid
          }
          this.filelist.push(fileData)
        }
      })
    },
    handleChange (file, filelist) {
      if (file.status !== 'ready') {
        this.showBtn = filelist.length < this.limit
      }
    }
  }
}
</script>

<style lang="scss">
@import "./zy-upload.scss";
</style>
