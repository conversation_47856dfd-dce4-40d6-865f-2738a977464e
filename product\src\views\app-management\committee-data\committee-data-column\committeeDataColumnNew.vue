<template>
  <div class="committeeDataColumnNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="栏目名称"
                    class="form-input"
                    prop="name">
        <el-input placeholder="请输入栏目名称"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上级栏目">
        <zy-select width="296"
                   node-key="id"
                   :props="{label:'name',children:'children'}"
                   v-model="form.superior"
                   :data="menu"
                   placeholder="请选择上级栏目"></zy-select>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input"
                    prop="sort">
        <el-input-number v-model="form.sort"
                         placeholder="请输入排序"
                         :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="是否置顶"
                    class="form-input">
        <el-radio-group v-model="form.isTop">
          <el-radio label="1">置顶</el-radio>
          <el-radio label="0">不置顶</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否APP显示"
                    v-if="$isAppShow()"
                    class="form-input">
        <el-radio-group v-model="form.isPushApp">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'committeeDataColumnNew',
  data () {
    return {
      menu: [],
      form: {
        name: '',
        superior: '',
        sort: '',
        isTop: '1',
        isPushApp: '1'
      },
      rules: {
        name: [
          { required: true, message: '请输入栏目名称', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id', 'module'],
  mounted () {
    this.informationColumnTree()
    if (this.id) {
      this.informationColumnInfo()
    }
  },
  methods: {
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: this.module })
      var { data } = res
      if (this.id) {
        this.menu = this.filterData(data)
      } else {
        this.menu = data
      }
    },
    filterData (menuList) {
      return menuList.filter(item => {
        return item.id !== this.id
      }).map(item => {
        item = Object.assign({}, item)
        if (item.children) {
          item.children = this.filterData(item.children)
        }
        return item
      })
    },
    async informationColumnInfo () {
      const res = await this.$api.appManagement.informationColumnInfo(this.id)
      var { data: { parentId, sort, name, isTop, isPushApp } } = res
      this.form.superior = parentId
      this.form.name = name
      this.form.sort = sort
      this.form.isTop = isTop
      this.form.isPushApp = isPushApp
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.form.superior === '') {
            this.form.superior = 1
          }
          var url = '/zyinfostructure/add'
          if (this.id) {
            url = '/zyinfostructure/edit'
          }
          this.$api.systemSettings.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            parentId: this.form.superior,
            name: this.form.name,
            sort: this.form.sort,
            isTop: this.form.isTop,
            isPushApp: this.form.isPushApp,
            module: this.module
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.committeeDataColumnNew {
  width: 682px;
}
</style>
