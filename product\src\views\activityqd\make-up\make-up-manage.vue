<template>
  <div class="activity-make-up-manage">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input v-model="searchParams.keyword"
                  placeholder="请输入关键字"
                  clearable
                  @keyup.enter.native="search"></el-input>
        <zy-cascader width="222"
                     node-key="id"
                     clearable
                     v-model="searchParams.meetType"
                     :data="typeList"
                     placeholder="请选择类型">
        </zy-cascader>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="handleAdd">新增</el-button>
        <el-button type="primary"
                   @click="handleBatchDelete">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  stripe
                  border
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                           type="index"
                           width="80"></el-table-column>
          <el-table-column label="活动主题"
                           prop="meetName"
                           show-overflow-tooltip
                           min-width="120"></el-table-column>
          <el-table-column label="组织部门"
                           prop="organizer"
                           width="120"></el-table-column>
          <el-table-column label="活动类型"
                           prop="meetType"
                           width="120"></el-table-column>
          <el-table-column label="活动时间"
                           prop="starTime"
                           min-width="360">
            <template slot-scope="scope">{{scope.row.meetStartTime}}至{{scope.row.meetEndTime}}</template>
          </el-table-column>
          <el-table-column label="创建人"
                           prop="createName"
                           min-width="120"></el-table-column>
          <el-table-column label="操作"
                           min-width="120"
                           fixed="right">
            <template slot-scope="scope">
              <el-button @click="handleEdit(scope.row.id)"
                         type="text">编辑</el-button>
              <el-button @click="handleDelete(scope.row.id)"
                         type="text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total"></el-pagination>
    </div>
  </div>
</template>

<script>
import table from '@/mixins/table.js'
import { filterParams } from '@/common/handleParams'
export default {
  mixins: [table],
  data () {
    return {
      searchParams: {
        keyword: '',
        meetType: null
      },
      typeList: []
    }
  },
  created () {
    this.getTypeList()
    this.getList()
  },
  inject: ['newTab'],
  methods: {
    // 获取所属分类
    getTypeList () {
      this.$api.activity.treelist({ treeType: 3 }).then(res => {
        this.typeList = res.data
      })
    },
    getList () {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize
      }
      params = Object.assign(params, filterParams(this.searchParams))
      this.$api.activityqd.makeUpList(params).then(res => {
        var { data, total } = res
        this.list = data
        this.total = total
      })
    },
    search () {
      this.getList()
    },
    reset () {
      this.searchParams = {
        keyword: '',
        meetType: ''
      }
      this.getList()
    },
    handleDelete (ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activityqd.dels(ids).then((res) => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    handleBatchDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.handleDelete(this.selectionList.map((v) => v.id).join(','))
    },
    handleAdd () {
      const mid = new Date().getTime().toString()
      this.newTab({
        name: '新建活动补录',
        menuId: mid,
        to: '/activity-make-up',
        params: { mid }
      })
    },
    handleEdit (id) {
      this.newTab({
        name: '编辑活动补录',
        menuId: id,
        to: '/activity-make-up',
        params: { id: id, mid: id }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.activity-make-up-manage {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
