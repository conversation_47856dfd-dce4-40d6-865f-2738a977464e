<template>
  <div class="booksAdd">
    <div class="booksAddPortrait">
      <div class="booksAddCover"><span class="red">*</span>书籍封面</div>
      <el-upload class="booksUploader"
                 action="/"
                 :before-upload="handleImg"
                 :http-request="imgUpload"
                 :show-file-list="false">
        <img v-if="file.filePath"
             :src="file.filePath"
             class="booksAddImg">
        <i v-else
           class="el-icon-plus booksUploaderIcon"></i>
      </el-upload>
    </div>
    <div class="booksAddForm">
      <el-form :model="form"
               :rules="rules"
               inline
               ref="form"
               label-position="top"
               class="newForm">
        <el-form-item class="form-input"
                      label="书籍名称"
                      prop="bookName">
          <el-input placeholder="请输入书籍名称"
                    v-model="form.bookName"
                    clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="作者"
                      class="form-input"
                      prop="authorName">
          <el-input placeholder="请输入作者"
                    v-model="form.authorName"
                    clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="一级类别"
                      prop="bookTypeFirstId"
                      class="form-input">
          <el-select v-model="form.bookTypeFirstId"
                     filterable
                     clearable
                     placeholder="请选择一级类别">
            <el-option v-for="item in bookType"
                       :key="item.id"
                       :label="item.name"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="二级类别"
                      class="form-input">
          <el-select v-model="form.bookTypeSecondId"
                     filterable
                     clearable
                     placeholder="请选择二级类别">
            <el-option v-for="item in typeSmall"
                       :key="item.id"
                       :label="item.name"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资源上传"
                      class="form-upload">
          <span slot="label"><span class="red">*</span>资源上传</span>
          <el-upload class="form-upload-demo"
                     drag
                     action="/"
                     :before-remove="beforeRemove"
                     :before-upload="handleFile"
                     :http-request="fileUpload"
                     :file-list="fileData"
                     multiple>
            <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
            <div class="el-upload__tip">仅支持pdf、txt格式</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="书籍简介"
                      prop="bookDescription"
                      class="form-ue">
          <el-input type="textarea"
                    :autosize="{ minRows: 4, maxRows: 9}"
                    placeholder="请输入书籍简介"
                    v-model="form.bookDescription">
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="booksAddFormButton">
      <el-button type="primary"
                 @click="submitForm('form')">确定</el-button>
      <el-button @click="resetForm('form')">取消</el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'booksAdd',
  data () {
    return {
      form: {
        bookName: '',
        authorName: '',
        bookDescription: '',
        bookTypeFirstId: '',
        bookTypeSecondId: ''
      },
      rules: {
        bookName: [
          { required: true, message: '请输入书籍名称', trigger: 'blur' }
        ],
        authorName: [
          { required: true, message: '请输入作者', trigger: 'blur' }
        ],
        bookDescription: [
          { required: true, message: '请输入书籍简介', trigger: 'blur' }
        ],
        bookTypeFirstId: [
          { required: true, message: '请选择一级类别', trigger: 'blur' }
        ]
        // bookTypeSecondId: [
        //   { required: true, message: '请选择二级类别', trigger: 'blur' }
        // ]
      },
      bookType: [],
      typeSmall: [],
      file: {},
      fileData: []
    }
  },
  props: ['id'],
  mounted () {
    this.getSyTypeTree()
  },
  watch: {
    'form.bookTypeFirstId' (val) {
      if (val) {
        this.bookType.forEach(item => {
          if (item.id === val) {
            this.form.bookTypeSecondId = ''
            this.typeSmall = item.children
          }
        })
      } else {
        this.form.bookTypeSecondId = ''
        this.typeSmall = []
      }
    }
  },
  methods: {
    async getSyTypeTree () {
      const res = await this.$api.academy.getSyTypeTree({})
      var { data } = res
      this.bookType = data
      if (this.id) {
        this.syBookInfo()
      }
    },
    handleFile (file, fileList) {
      if (this.fileData.length >= 1) {
        this.$message({
          message: '只能上传一个文件资源!',
          type: 'warning'
        })
        return false
      }
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'txt'
      const extension4 = testmsg === 'pdf'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是pdf、txt格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    fileUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'bookfile')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.fileData = data
      }).catch(() => {
        files.onError()
      })
    },
    beforeRemove (file, fileList) {
      var fileData = this.fileData
      this.fileData = fileData.filter(item => item.id !== file.id)
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'bookpic')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.file = data[0]
      }).catch(() => {
        files.onError()
      })
    },
    async syBookInfo () {
      const res = await this.$api.academy.syBookInfo({ id: this.id })
      var { data } = res
      this.form.bookName = data.bookName
      this.form.authorName = data.authorName
      this.form.bookDescription = data.bookDescription
      this.form.bookTypeFirstId = data.bookTypeFirstId
      setTimeout(() => {
        this.typeSmall.forEach(item => {
          if (item.id === data.bookTypeSecondId) {
            this.form.bookTypeSecondId = data.bookTypeSecondId
          }
        })
      }, 520)
      if (data.coverImg) {
        this.file = { id: data.coverImg, filePath: data.coverImgUrl }
      }
      if (data.bookContent) {
        this.fileData = [{ id: data.bookContent, uid: data.bookContent, name: data.bookContentName, url: data.bookContentUrl }]
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (!this.file.id) {
            this.$message({
              message: '请上传书籍封面图！',
              type: 'warning'
            })
            return
          }
          var bookContent = []
          this.fileData.forEach(item => {
            bookContent.push(item.id)
          })
          var url = '/syBook/add'
          if (this.id) {
            url = '/syBook/edit'
          }
          this.$api.systemSettings.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            bookName: this.form.bookName,
            authorName: this.form.authorName,
            bookDescription: this.form.bookDescription,
            bookTypeFirstId: this.form.bookTypeFirstId,
            bookTypeSecondId: this.form.bookTypeSecondId,
            coverImg: this.file.id ? this.file.id : '',
            bookContent: bookContent.join(',')
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.booksAdd {
  width: 900px;
  padding: 24px;
  padding-right: 0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .booksAddCover {
    padding: 12px 0;
    padding-top: 6px;
    line-height: 24px;
    font-size: 14px;
  }
  .red {
    color: #f56c6c;
    margin-right: 4px;
  }

  .booksAddPortrait {
    width: 162px;
    height: 216px;
    background-size: 100% 100%;
    padding-top: 24px;

    .booksUploader {
      height: 100%;
      width: 100%;
      .el-upload {
        border: 1px dashed #d9d9d9;
        &:hover {
          border-color: $zy-color;
        }
      }
      .booksUploaderIcon {
        font-size: 28px;
        color: #8c939d;
        width: 162px;
        height: 216px;
        line-height: 216px;
        text-align: center;
      }

      .booksAddImg {
        width: 144px;
        height: 175px;
        display: block;
      }
    }
  }
  .booksAddForm {
    width: 682px;
  }
  .booksAddFormButton {
    width: 100%;
    height: 52px;
    display: flex;
    justify-content: center;
    padding: 6px 0;

    .el-button {
      height: 36px;
      line-height: 36px;
      padding: 0 16px;
    }

    .el-button + .el-button {
      margin-left: 24px;
    }
  }
}
</style>
