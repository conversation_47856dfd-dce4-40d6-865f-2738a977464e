<template>
  <div class="committee-data">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2"
                       :buttonNumber="2">
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="stateId"
                   filterable
                   clearable
                   placeholder="请选择审核状态">
          <el-option v-for="item in state"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:add'"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:dels'"
                   @click="deleteClick">删除</el-button>
        <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:batchUpdate'"
                   @click="passClick(1)">审核通过</el-button>
        <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:batchUpdate'"
                   @click="passClick(2)">审核不通过</el-button>
      </template>
    </xyl-search-button>
    <div class="information-box">
      <div class="information-tree-box">
        <div class="information-tree-text">选择栏目</div>
        <div class="information-tree">
          <zy-tree :tree="tree"
                   v-model="treeId"
                   :props="{ children: 'children', label: 'name'}"
                   @on-tree-click="choiceClick"></zy-tree>
        </div>
      </div>
      <div class="information-data-box">
        <div class="plenum-data">
          <zy-table>
            <el-table :data="tableData"
                      ref="table"
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               fixed="left"
                               width="60"></el-table-column>
              <el-table-column label="序号"
                               width="80"
                               prop="sort">
              </el-table-column>
              <el-table-column label="标题"
                               min-width="260"
                               show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-button @click="details(scope.row)"
                             type="text">{{scope.row.title}}</el-button>
                </template>
              </el-table-column>
              <el-table-column label="所属栏目"
                               width="160"
                               prop="structureName">
              </el-table-column>
              <el-table-column label="来源"
                               width="160"
                               prop="source">
              </el-table-column>
              <el-table-column label="发布人"
                               width="160"
                               prop="createBy">
              </el-table-column>
              <el-table-column label="发布时间"
                               width="190"
                               prop="publishDate">
              </el-table-column>
              <el-table-column label="审核状态"
                               width="120"
                               prop="auditingFlag">
              </el-table-column>
              <el-table-column label="操作"
                               fixed="right"
                               v-if="$hasPermission(['auth:zyinfodetail:edit'])"
                               width="120">
                <template slot-scope="scope">
                  <el-button @click="modify(scope.row)"
                             v-permissions="'auth:zyinfodetail:edit'"
                             type="primary"
                             plain
                             size="mini">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="$pageSizes()"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>

    <zy-pop-up v-model="show"
               :title="id?'编辑':'新增'">
      <committeeDataAdd :id="id"
                        :module="module"
                        :parentId="treeId"
                        @callback="addCallback"></committeeDataAdd>
    </zy-pop-up>

    <zy-pop-up v-model="detailsShow"
               title="详情">
      <committeeDataDetails :id="id"></committeeDataDetails>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import committeeDataAdd from './components/committeeDataAdd'
import committeeDataDetails from './components/committeeDataDetails'
export default {
  name: 'committeeData',
  data () {
    return {
      module: 6,
      keyword: '',
      treeId: '1',
      tree: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      stateId: '',
      state: [
        { id: '1', name: '审核通过' },
        { id: '0', name: '待审核' },
        { id: '2', name: '审核不通过' }
      ],
      id: '',
      show: false,
      detailsShow: false
    }
  },
  inject: ['newTab'],
  mixins: [tableData],
  components: {
    committeeDataAdd,
    committeeDataDetails
  },
  mounted () {
    if (this.$route.query.module) {
      this.module = this.$route.query.module
    }
    this.informationList()
    this.informationColumnTree()
  },
  methods: {
    search () {
      this.page = 1
      this.informationList()
    },
    reset () {
      this.keyword = ''
      this.stateId = ''
      this.informationList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    modify (row) {
      this.id = row.id
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    addCallback () {
      this.informationList()
      this.informationColumnTree()
      this.show = false
    },
    passClick (auditingFlag) {
      if (this.choose.length) {
        this.$confirm(`此操作将选择的资料的状态改为${auditingFlag === 1 ? '审核通过' : '审核不通过'}, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationBatchUpdate(this.choose.join(','), auditingFlag)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationBatchUpdate (id, auditingFlag) {
      const res = await this.$api.appManagement.informationBatchUpdate({ ids: id, auditingFlag: auditingFlag })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: this.module })
      var { data } = res
      var arr = [{ children: [], id: '1', name: '所有' }]
      this.tree = arr.concat(data)
    },
    choiceClick (item) {
      this.informationList()
    },
    async informationList () {
      const res = await this.$api.appManagement.informationList({
        module: this.module,
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        structureId: this.treeId,
        auditingFlag: this.stateId
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.informationList()
    },
    whatPage (val) {
      this.informationList()
    },
    deleteClick (row) {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的资料, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationListDel(this.choose.join(',')).then(res => {
            this.informationColumnTree()
            this.informationList()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async informationListDel (id) {
      const res = await this.$api.appManagement.informationListDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.informationList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.committee-data {
  width: 100%;
  height: 100%;

  .information-box {
    height: calc(100% - 64px);
    width: 100%;
    display: flex;

    .information-tree-box {
      width: 222px;
      height: 100%;

      .zy-tree {
        width: 222px;
        min-width: 222px;
      }

      .information-tree-text {
        height: 52px;
        line-height: 52px;
        padding-left: 24px;
        font-size: 16px;
        background-color: #e6e5e8;
      }

      .information-tree {
        height: calc(100% - 52px);
      }
    }

    .information-data-box {
      width: calc(100% - 222px);
      height: 100%;

      .plenum-data {
        height: calc(100% - 52px);
        width: 100%;
      }
    }
  }
}
</style>
