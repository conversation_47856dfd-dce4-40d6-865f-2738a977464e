const currentBehalf = () => import('@/views/behalfInformation/generalCurrentBehalf/currentBehalf')
const nationalCurrentBehalf = () => import('@/views/behalfInformation/generalCurrentBehalf/nationalCurrentBehalf')
const presentCurrentBehalf = () => import('@/views/behalfInformation/generalCurrentBehalf/presentCurrentBehalf')
const beforeBehalf = () => import('@/views/behalfInformation/generalBeforeBehalf/beforeBehalf')
const nationalBeforeBehalf = () => import('@/views/behalfInformation/generalBeforeBehalf/nationalBeforeBehalf')
const behalfStatistical = () => import('@/views/behalfInformation/generalBehalfStatistical/behalfStatistical')
const nationalBehalfStatistical = () => import('@/views/behalfInformation/generalBehalfStatistical/nationalBehalfStatistical')
const time = () => import('@/views/behalfInformation/generalTime/time')
const nationalTime = () => import('@/views/behalfInformation/generalTime/nationalTime')
const behalfNew = () => import('@/views/behalfInformation/generalCurrentBehalf/behalfNew/behalfNew')
const behalfDetails = () => import('@/views/behalfInformation/generalCurrentBehalf/behalfDetails/behalfDetails')
const batchExportPhoto = () => import('@/views/behalfInformation/ExportPhoto/batchExportPhoto')
const memberExportPhoto = () => import('@/views/behalfInformation/ExportPhoto/memberExportPhoto')
const behalfAudit = () => import('@/views/behalfInformation/behalfAudit/behalfAudit')
const batchBirthdayReminder = () => import('@/views/behalfInformation/BirthdayReminder/batchBirthdayReminder')
const memberBirthdayReminder = () => import('@/views/behalfInformation/BirthdayReminder/memberBirthdayReminder')
const batchTransition = () => import('@/views/behalfInformation/Transition/batchTransition')
const memberTransition = () => import('@/views/behalfInformation/Transition/memberTransition')
const behalfStatisticalList = () => import('@/views/behalfInformation/generalBehalfStatistical/behalfStatisticalList')
const nationalBehalfStatisticalList = () => import('@/views/behalfInformation/generalBehalfStatistical/nationalBehalfStatisticalList')

const behalfInformation = [
  { // 代表审核
    path: '/behalfAudit',
    name: 'behalfAudit',
    component: behalfAudit
  },
  { // 本届代表
    path: '/currentBehalf',
    name: 'currentBehalf',
    component: currentBehalf
  },
  { // 本届全国代表
    path: '/nationalCurrentBehalf',
    name: 'nationalCurrentBehalf',
    component: nationalCurrentBehalf
  },
  { // 本届出缺代表
    path: '/presentCurrentBehalf',
    name: 'presentCurrentBehalf',
    component: presentCurrentBehalf
  },
  { // 历届代表
    path: '/beforeBehalf',
    name: 'beforeBehalf',
    component: beforeBehalf
  },
  { // 历届全国代表
    path: '/nationalBeforeBehalf',
    name: 'nationalBeforeBehalf',
    component: nationalBeforeBehalf
  },
  { // 代表统计
    path: '/behalfStatistical',
    name: 'behalfStatistical',
    component: behalfStatistical
  },
  { // 全国代表统计
    path: '/nationalBehalfStatistical',
    name: 'nationalBehalfStatistical',
    component: nationalBehalfStatistical
  },
  { // 代表统计
    path: '/behalfStatisticalList',
    name: 'behalfStatisticalList',
    component: behalfStatisticalList
  },
  { // 全国代表统计
    path: '/nationalBehalfStatisticalList',
    name: 'nationalBehalfStatisticalList',
    component: nationalBehalfStatisticalList
  },
  { // 届次管理
    path: '/time',
    name: 'time',
    component: time
  },
  { // 全国届次管理
    path: '/nationalTime',
    name: 'nationalTime',
    component: nationalTime
  },
  { // 添加代表信息
    path: '/behalfNew',
    name: 'behalfNew',
    component: behalfNew
  },
  { // 代表信息详情
    path: '/behalfDetails',
    name: 'behalfDetails',
    component: behalfDetails
  },
  { // 批量导入代表照片
    path: '/batchExportPhoto',
    name: 'batchExportPhoto',
    component: batchExportPhoto
  },
  { // 批量导入委员照片
    path: '/memberExportPhoto',
    name: 'memberExportPhoto',
    component: memberExportPhoto
  },
  { // 批量代表换届
    path: '/batchTransition',
    name: 'batchTransition',
    component: batchTransition
  },
  { // 批量委员换届
    path: '/memberTransition',
    name: 'memberTransition',
    component: memberTransition
  },
  { // 代表生日提醒
    path: '/batchBirthdayReminder',
    name: 'batchBirthdayReminder',
    component: batchBirthdayReminder
  },
  { // 委员生日提醒
    path: '/memberBirthdayReminder',
    name: 'memberBirthdayReminder',
    component: memberBirthdayReminder
  }
]
export default behalfInformation
