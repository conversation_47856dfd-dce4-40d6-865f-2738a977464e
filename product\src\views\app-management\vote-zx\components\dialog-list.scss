.join-man{
  width: 800px;
  .search-box{
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 10px;
  }
  .btn-box{
    height: 40px;
    line-height: 40x;
    padding-left: 15px;
  }
  .tableData{
    height: 480px !important;
    .t-color {
      color: #199bc5;
      cursor: pointer;
    }
    .sp{
      overflow : hidden;
      text-overflow: ellipsis;
      // display: -webkit-box;
      white-space: nowrap;
      // -webkit-line-clamp: 3;
      // -webkit-box-orient: vertical;
    }
    // .sh{
    //   line-height: 33%;
    // }
  }
  .page-box{
    height: 40px;
    display: flex;
    align-items: center;
  }
  .check-btn{
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .option-item{
    padding: 10px 0;
  }
  .check-color {
    color: #35be38;
  }
  .close-color {
    color: red;
  }
  // .el-table--border td {
  //   border-color: # !important;
  // }
}