<template>
  <div class="activityAll">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :buttonNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <zy-select width="222"
                   node-key="id"
                   v-model="meetType"
                   :data="meetTypeData"
                   placeholder="请选择活动类型"></zy-select>
        <zy-select width="222"
                   node-key="id"
                   v-model="meetState"
                   :data="meetStateData"
                   placeholder="请选择活动状态"></zy-select>
        <el-select v-model="pubOrganizer"
                   clearable
                   placeholder="请选择活动主体">
          <el-option v-for="item in pubOrganizerData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.value">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <!-- <el-button type="primary"
                   @click="newData">新增</el-button> -->
        <el-button type="primary"
                   v-permissions="'auth:activityAll:del'"
                   @click="deleteClick">删除</el-button>
        <el-button type="primary"
                   @click="exportMethods">导出excel</el-button>
        <el-button type="primary"
                   @click="smsSend('1')">报名短信通知</el-button>
        <el-button type="primary"
                   @click="smsSend('2')">签到短信通知</el-button>
        <el-button type="primary"
                   v-if="$isAppShow()"
                   @click="stateClick(1)">App显示</el-button>
        <el-button type="primary"
                   v-if="$isAppShow()"
                   @click="stateClick(0)">App不显示</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <!-- <el-table-column label="序号"
                           width="80"
                           prop="num"></el-table-column> -->
          <el-table-column label="活动主题"
                           min-width="220"
                           prop="meetName">
            <template slot-scope="scope">
              <el-button @click="details(scope.row)"
                         type="text">{{scope.row.meetName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="签到人数"
                           width="90"
                           prop="signInNum"></el-table-column>
          <el-table-column label="签到人员"
                           prop="signInNames"
                           min-width="150"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="活动类型"
                           width="90"
                           prop="meetType"></el-table-column>
          <el-table-column label="组织部门"
                           width="90"
                           prop="organizer"></el-table-column>
          <el-table-column label="活动主体"
                           width="90"
                           prop="pubOrganizer"></el-table-column>
          <el-table-column label="活动开始时间"
                           width="190">
            <template slot-scope="scope">{{scope.row.meetStartTime}}</template>
          </el-table-column>
          <el-table-column label="开始签到时间"
                           width="190">
            <template slot-scope="scope">{{scope.row.meetSignBeginTime}}</template>
          </el-table-column>
          <el-table-column label="活动结束时间"
                           width="190">
            <template slot-scope="scope">{{scope.row.meetEndTime}}</template>
          </el-table-column>
          <el-table-column label="活动状态"
                           min-width="120"
                           prop="state"></el-table-column>
          <el-table-column label="是否APP显示"
                           v-if="$isAppShow()"
                           width="120">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isAppShow"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="是否公开"
                           width="120"
                           prop="isPublish">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isPublish"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="是否公开报名"
                           width="120"
                           prop="isPublishbm">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isPublishbm"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="建立人"
                           width="120"
                           prop="createName">
          </el-table-column>
          <el-table-column label="评论回复"
                           min-width="190"
                           prop="commentCount">
            <template slot-scope="scope">
              <el-button @click="commentCount(scope.row)"
                         type="text">{{scope.row.commentCount}}</el-button>
            </template>
          </el-table-column>

          <el-table-column label="操作"
                           fixed="right"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="editor(scope.row)"
                         v-permissions="'auth:activityAll:edit'"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="detailsShow"
                      title="活动详情">
      <activityDetails :id="id"
                       type="1"></activityDetails>
    </xyl-popup-window>
    <xyl-popup-window v-model="exportShow"
                      title="导出">
      <zy-export :params="params"
                 :excelId="excelId"
                 :type="502"
                 @callback="callback"></zy-export>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import activityDetails from './activityDetails'
export default {
  name: 'activityAll',
  data () {
    return {
      keyword: '',
      meetType: '',
      meetTypeData: [],
      meetState: '',
      meetStateData: [
        { label: '未开始', id: 'notStart' },
        { label: '进行中', id: 'ongoing' },
        { label: '报名中', id: 'signUp' },
        { label: '已结束', id: 'end' }
      ],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false,
      detailsShow: false,
      exportShow: false,
      excelId: '',
      params: {},
      top: 0,
      pubOrganizer: '',
      pubOrganizerData: [
        { id: '001', value: '专委会活动' },
        { id: '002', value: '界别活动' },
        { id: '003', value: '委员工作站活动' }]
    }
  },
  mixins: [tableData],
  inject: ['newTab'],
  components: {
    activityDetails
  },
  mounted () {
    this.meetState = this.$route.params.meetState
    // this.treelist()
    // this.activityList()
  },
  activated () {
    this.meetState = this.$route.params.meetState
    this.treelist()
    this.activityList()
  },
  methods: {
    async treelist () {
      const res = await this.$api.activity.treelist({ treeType: 3 })
      var { data } = res
      this.meetTypeData = data
    },
    search () {
      this.page = 1
      this.activityList()
    },
    reset () {
      this.keyword = ''
      this.meetType = ''
      this.meetState = ''
      this.pubOrganizer = ''
      this.activityList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    exportMethods () {
      this.excelId = this.choose.join(',')
      this.params = {
        meetType: this.meetType,
        meetState: this.meetState,
        keyword: this.keyword,
        isCollection: 0
      }
      this.exportShow = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    commentCount (val) {
      this.newTab({ name: '评论回复', menuId: val.id, to: '/comments', params: { keyId: val.id, type: '49' } })
    },
    editor (row) {
      // this.id = row.id
      // this.show = true
      this.newTab({ name: '编辑活动', menuId: row.id, to: '/activityNew', params: { id: row.id } })
    },
    callback () {
      this.exportShow = false
    },
    newCallback () {
      this.activityList()
      this.show = false
      this.detailsShow = false
    },
    async activityList () {
      const res = await this.$api.activity.activityList({
        pageNo: this.page,
        pageSize: this.pageSize,
        meetType: this.meetType,
        meetState: this.meetState,
        keyword: this.keyword,
        pubOrganizer: this.pubOrganizer,
        isCollection: 0
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.activityList()
    },
    whatPage (val) {
      this.activityList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除该活动, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.activityDels(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async activityDels (id) {
      const res = await this.$api.activity.activityDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.activityList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    smsSend (type) {
      if (this.choose.length) {
        this.$confirm('此操作将发送短信, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.activitySmsSend(this.choose.join(','), type)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async activitySmsSend (id, type) {
      const res = await this.$api.activity.activitySmsSend({
        actvityId: id,
        type: type
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.activityList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    stateClick (type) {
      if (this.choose.length) {
        this.updateIsAppShow(this.choose.join(','), type)
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async updateIsAppShow (id, type) {
      const res = await this.$api.activity.updateIsAppShow({
        ids: id,
        isAppShow: type
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.activityList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    handleScroll (event, top) {
      this.top = top
    }
  }
}
</script>
<style lang="scss">
.activityAll {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
