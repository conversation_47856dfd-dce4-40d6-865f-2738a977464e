<template>
  <div class="BirthdayReminder">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="month"
                   placeholder="请选择月份">
          <el-option v-for="item in monthData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button type="primary">导出excel</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="姓名"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="details(scope.row)"
                         type="text">{{scope.row.userName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column :label="memberType=='1'?'委员证号':'代表证号'"
                           width="160"
                           prop="userNumber"></el-table-column>
          <el-table-column label="性别"
                           width="90"
                           prop="sex"></el-table-column>
          <el-table-column :label="'代表团'"
                           v-if="memberType=='3'"
                           width="160"
                           prop="representerTeam"></el-table-column>
          <el-table-column label="所属结构"
                           v-if="memberType=='3'"
                           width="160"
                           prop="representerElement"></el-table-column>
          <el-table-column label="界别"
                           v-if="memberType=='1'"
                           width="160"
                           prop="deleId"></el-table-column>
          <el-table-column label="所属专委会"
                           v-if="memberType=='1'"
                           width="160"
                           prop="committee"></el-table-column>
          <el-table-column label="出生年月"
                           width="160">
            <template slot-scope="scope">{{scope.row.birthday|datefmt('YYYY-MM')}}</template>
          </el-table-column>
          <el-table-column label="民族"
                           min-width="120"
                           prop="nation"></el-table-column>
          <el-table-column label="手机号码"
                           min-width="190"
                           prop="mobile"></el-table-column>
          <el-table-column label="状态"
                           min-width="120"
                           prop="isUsing">
            <template slot-scope="scope">
              {{scope.row.isUsing==1?'正常':'被撤销资格'}}
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
export default {
  name: 'BirthdayReminder',
  data () {
    return {
      keyword: '',
      month: '',
      monthData: [
        { id: 1, value: '一月' },
        { id: 2, value: '二月' },
        { id: 3, value: '三月' },
        { id: 4, value: '四月' },
        { id: 5, value: '五月' },
        { id: 6, value: '六月' },
        { id: 7, value: '七月' },
        { id: 8, value: '八月' },
        { id: 9, value: '九月' },
        { id: 10, value: '十月' },
        { id: 11, value: '十一月' },
        { id: 12, value: '十二月' }
      ],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize()
    }
  },
  mixins: [tableData],
  inject: ['newTab'],
  props: ['memberType'],
  mounted () {
    this.month = new Date().getMonth() + 1
    this.memberList()
  },
  activated () {
    this.memberList()
  },
  methods: {
    search () {
      this.page = 1
      this.memberList()
    },
    reset () {
      this.keyword = ''
      this.month = new Date().getMonth() + 1
      this.memberList()
    },
    async memberList () {
      const res = await this.$api.memberInformation.memberList({
        pageNo: this.page,
        pageSize: this.pageSize,
        memberType: this.memberType,
        keyword: this.keyword,
        birthdayMonth: this.month
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    details (row) {
      if (this.memberType === 1) {
        this.newTab({ name: '委员信息详情', menuId: '16620092811', to: '/memberDetails', params: { id: row.id, memberType: this.memberType } })
      } else {
        this.newTab({ name: '代表信息详情', menuId: '16620092811', to: '/behalfDetails', params: { id: row.id, memberType: this.memberType } })
      }
    },
    howManyArticle (val) {
      this.memberList()
    },
    whatPage (val) {
      this.memberList()
    }
  }
}
</script>
<style lang="scss">
.BirthdayReminder {
  height: 100%;
  width: 100%;

  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
