// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const officeAutomation = {
  // 流程配置 节点列表
  configProcessNodeList (data) {
    return get('/oa/flow/node/list', data)
  },
  // 流程配置 新增节点
  configProcessNodeAdd (data) {
    return post('/oa/flow/node/add', data)
  },
  // 流程配置 编辑节点
  configProcessNodeEdit (data) {
    return post('/oa/flow/node/edit', data)
  },
  // 流程配置 删除节点
  configProcessNodeDel (id) {
    return get(`/oa/flow/node/del/${id}`)
  },
  // 流程配置 新增节点操作
  configProcessNodeOperateList (data) {
    return get('/oa/flow/node/operation/list', data)
  },
  // 流程配置 新增节点操作
  configProcessNodeOperateAdd (data) {
    return post('/oa/flow/node/operation/add', data)
  },
  // 流程配置 新增节点操作
  configProcessNodeOperateEdit (data) {
    return post('/oa/flow/node/operation/edit', data)
  },
  // 流程配置 新增节点操作
  configProcessNodeOperateDel (id) {
    return get(`/oa/flow/node/operation/del/${id}`)
  },
  // 车辆管理 新增
  manageCarAdd (data) {
    return post('/oa/car/add', data)
  },
  // 车辆管理 编辑
  manageCarEdit (data) {
    return post('/oa/car/edit', data)
  },
  // 车辆管理 编辑
  manageCarList (data) {
    return get('/oa/car/list', data)
  },
  // 车辆管理 删除
  manageCarDel (id) {
    return get(`/oa/car/del/${id}`)
  },
  // 车辆管理 删除
  manageCarInfo (id) {
    return get(`/oa/car/info/${id}`)
  },
  // 驾驶员管理 新增
  manageDriverAdd (data) {
    return post('/oa/car/driver/add', data)
  },
  // 驾驶员管理 编辑
  manageDriverEdit (data) {
    return post('/oa/car/driver/edit', data)
  },
  // 驾驶员管理 列表
  manageDriverList (data) {
    return get('/oa/car/driver/list', data)
  },
  // 驾驶员管理 详情
  manageDriverInfo (id) {
    return get(`/oa/car/driver/info/${id}`)
  },
  // 驾驶员管理 详情
  manageDriverDel (id) {
    return get(`/oa/car/driver/del/${id}`)
  },
  // 用户签名管理 新增
  userSignatureAdd (data) {
    return post('/oa/signature/info/add', data)
  },
  // 用户签名管理 编辑
  userSignatureEdit (data) {
    return post('/oa/signature/info/edit', data)
  },
  // 用户签名管理 列表
  userSignatureList (data) {
    return get('/oa/signature/info/list', data)
  },
  // 用户签名管理 列表
  userSignatureListByUser (data) {
    return get('/oa/signature/info/listByUser', data)
  },
  // 用户签名管理 删除
  userSignatureDel (id) {
    return get(`/oa/signature/info/del/${id}`)
  },
  // 用户签名管理 删除
  userSignatureDels (ids) {
    return post('/oa/signature/info/dels', { ids: ids })
  },
  // 用户签名管理 删除
  userSignatureInfo (id) {
    return post(`/oa/signature/info/info/${id}`)
  },
  // 用户签名凭证
  userSignatureToken (params) {
    return post('/oa/signature/info/getSignatureInfo', params)
  },
  // 大事记 新增
  memorabiliaAdd (data) {
    return post('/oa/memorabilia/add', data)
  },
  // 大事记 编辑
  memorabiliaEdit (data) {
    return post('/oa/memorabilia/edit', data)
  },
  // 大事记 列表
  memorabiliaList (data) {
    return get('/oa/memorabilia/list', data)
  },
  // 大事记 详情
  memorabiliaInfo (id) {
    return get(`/oa/memorabilia/info/${id}`)
  },
  // 大事记 删除单项
  memorabiliaDel (id) {
    return get(`/oa/memorabilia/del/${id}`)
  },
  // 大事记 批量删除
  memorabiliaDels (ids) {
    return post('/oa/memorabilia/dels', { ids: ids })
  },
  // 获取栏目树
  learnDayColumnTree () {
    return post('/oa/study/getTree')
  },
  // 获取栏目列表
  learnDayColumnList (data) {
    return get('/oa/study/list', data)
  },
  // 每日一学  新增栏目
  learnDayColumnAdd (data) {
    return post('/oa/study/add', data)
  },
  // 每日一学  编辑栏目
  learnDayColumnEdit (data) {
    return post('/oa/study/edit', data)
  },
  // 每日一学 栏目详情
  learnDayColumnInfo (id) {
    return get(`/oa/study/info/${id}`)
  },
  // 每日一学 栏目删除
  learnDayColumndel (id) {
    return get(`/oa/study/del/${id}`)
  },
  // 每日一学 栏目批量删除
  learnDayColumndels (ids) {
    return post('/oa/study/dels', { ids: ids })
  },
  // 获取每日一学列表
  learnDayList (data) {
    return get('/oa/study/info/list', data)
  },
  // 每日一学 新增
  learnDayAdd (data) {
    return post('/oa/study/info/add', data)
  },
  // 每日一学 编辑
  learnDayEdit (data) {
    return post('/oa/study/info/edit', data)
  },
  // 每日一学 删除
  learnDayDel (id) {
    return get(`/oa/study/info/del/${id}`)
  },
  // 每日一学 批量删除
  learnDayDels (ids) {
    return get('/oa/study/info/dels', { ids: ids })
  },
  // 每日一学 详情
  learnDayInfo (id) {
    return get(`/oa/study/info/info/${id}`)
  },
  // 每日一学 不通过
  learnDayAuditNoPass (ids) {
    return post('/oa/study/info/auditNoPass', { ids: ids })
  },
  // 每日一学 通过
  learnDayAuditPass (ids) {
    return post('/oa/study/info/auditPass', { ids: ids })
  },
  // 文件资料 栏目新建
  documentColumnAdd (data) {
    return post('/oa/info/structure/add', data)
  },
  // 文件资料 栏目编辑
  documentColumnEdit (data) {
    return post('/oa/info/structure/edit', data)
  },
  // 文件资料 栏目删除
  documentColumnDel (id) {
    return get(`/oa/info/structure/del/${id}`)
  },
  // 文件资料 栏目批量删除
  documentColumnDels (ids) {
    return get('/oa/info/structure/dels', { ids: ids })
  },
  // 文件资料 栏目列表
  documentColumnList (data) {
    return get('/oa/info/structure/list', data)
  },
  // 文件资料 栏目详情
  documentColumnInfo (id) {
    return get(`/oa/info/structure/info/${id}`)
  },
  // 文件资料 栏目树
  documentColumnTree () {
    return post('/oa/info/structure/getTree')
  },
  // 文件资料 新增
  documentAdd (data) {
    return post('/oa/info/detail/add', data)
  },
  // 文件资料 编辑
  documentEdit (data) {
    return post('/oa/info/detail/edit', data)
  },
  // 文件资料 删除
  documentDel (id) {
    return get(`/oa/info/detail/del/${id}`)
  },
  // 文件资料 批量删除
  documentDels (ids) {
    return get('/oa/info/detail/dels', { ids: ids })
  },
  // 文件资料 列表
  documentList (data) {
    return get('/oa/info/detail/list', data)
  },
  // 文件资料 详情
  documentInfo (id) {
    return get(`/oa/info/detail/info/${id}`)
  },
  // 文件资料 审核不通过
  documentAuditNoPass (ids) {
    return post('/oa/info/detail/auditNoPass', { ids: ids })
  },
  // 文件资料 审核通过
  documentAuditPass (ids) {
    return post('/oa/info/detail/auditPass', { ids: ids })
  },
  // 值班 新增
  attendanceAdd (data) {
    return post('/oa/on/duty/add', data)
  },
  // 值班 编辑
  attendanceEdit (data) {
    return post('/oa/on/duty/edit', data)
  },
  // 值班 列表
  attendanceList (data) {
    return get('/oa/on/duty/list', data)
  },
  // 值班 删除
  attendanceDel (id) {
    return get(`/oa/on/duty/del/${id}`)
  },
  // 值班 批量删除
  attendanceDels (ids) {
    return get('/oa/on/duty/dels', { ids: ids })
  },
  // 值班 详情
  attendanceInfo (id) {
    return get(`/oa/on/duty/info/${id}`)
  },
  // 值班日志 新增
  attendanceLogAdd (data) {
    return post('/oa/duty/logs/add', data)
  },
  // 值班日志 编辑
  attendanceLogEdit (data) {
    return post('/oa/duty/logs/edit', data)
  },
  // 值班日志 删除
  attendanceLogDel (id) {
    return get(`/oa/duty/logs/del/${id}`)
  },
  // 值班日志 批量删除
  attendanceLogDels (ids) {
    return get('/oa/duty/logs/dels', { ids: ids })
  },
  // 值班日志 列表
  attendanceLogList (data) {
    return get('/oa/duty/logs/list', data)
  },
  // 值班日志 详情
  attendanceLogInfo (id) {
    return get(`/oa/duty/logs/info/${id}`)
  },
  // 工作日志 新增
  workLogAdd (data) {
    return post('/oa/work/report/add', data)
  },
  // 工作日志 编辑
  workLogEdit (data) {
    return post('/oa/work/report/edit', data)
  },
  // 工作日志 删除
  workLogDel (id) {
    return get(`/oa/work/report/del/${id}`)
  },
  // 工作日志 批量删除
  workLogDels (ids) {
    return get('/oa/work/report/dels', { ids: ids })
  },
  // 工作日志 列表
  workLogList (data) {
    return get('/oa/work/report/list', data)
  },
  // 工作日志 详情
  workLogInfo (id) {
    return get(`/oa/work/report/info/${id}`)
  },
  // 每月工作 列表
  monthWorkList (data) {
    return get('/oa/month/list', data)
  },
  // 每月工作 新增
  monthWorkAdd (data) {
    return post('/oa/month/add', data)
  },
  // 每月工作 删除
  monthWorkDel (id) {
    return get(`/oa/month/del/${id}`)
  },
  // 每月工作 工作计划新增
  monthWorkPlanAdd (data) {
    return post('/oa/month/work/addPlan', data)
  },
  // 每月工作 工作计划列表
  monthWorkPlanList (data) {
    return get('/oa/month/work/listByPlan', data)
  },
  // 每月工作 工作总结新增
  monthWorkSummaryAdd (data) {
    return post('/oa/month/work/addSum', data)
  },
  // 每月工作 工作总结列表
  monthWorkSummaryList (data) {
    return get('/oa/month/work/listBySum', data)
  },
  // 每月工作 工作计划/总结 编辑
  monthWorkEdit (data) {
    return post('/oa/month/work/edit', data)
  },
  // 每月工作 工作计划/总结 删除
  monthWorkItemDel (id) {
    return get(`/oa/month/work/del/${id}`)
  },
  // 每月工作 工作计划/总结 批量删除
  monthWorkItemDels (ids) {
    return get('/oa/month/work/dels', { ids: ids })
  },

  /**
   * 用车
  */
  // 可调配车辆列表
  canUseCarList (data) {
    return get('/oa/car/enableList', data)
  },
  // 可调配驾驶员列表
  canUseDriverList (data) {
    return get('/oa/car/driver/enableList', data)
  },
  // 用车申请新增
  useCarApplyAdd (data) {
    return post('/oa/car/application/add', data, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  // 用车申请编辑
  useCarApplyEdit (data) {
    return post('/oa/car/application/edit', data)
  },
  // 用车申请列表 我的
  myUserApplyList (data) {
    return get('/oa/car/application/myList', data)
  },
  // 用车申请列表 我的草稿
  UserApplyMyDraftList (data) {
    return get('/oa/car/application/myDraftList', data)
  },
  // 用车申请列表 管理员
  useCarApplyManageList (params) {
    return get('/oa/car/application/allList', params)
  },
  // 申请单状态列表
  applyStatusList () {
    return get('/oa/car/application/statusList')
  },
  // 申请人 删除用车
  ownerDeleteUseCar (id) {
    return get('/oa/car/application/ownerDelete', { id: id })
  },
  // 管理人 删除用车
  adminDeleteUseCar (id) {
    return get('/oa/car/application/powerDelete', { id: id })
  },
  // 用车申请详情
  useCarApplyInfo (id) {
    return get(`/oa/car/application/info/${id}`)
  },
  // 新增随行乘客
  passengerAdd (params) {
    return post('/oa/car/passenger/add', params)
  },
  // 修改增随行乘客
  passengerEdit (params) {
    return post('/oa/car/passenger/edit', params)
  },
  // 删除随行乘客
  passengerDel (id) {
    return get(`/oa/car/passenger/del/${id}`)
  },
  // 选择审批人详情
  chooseAuditDetail (id) {
    return get('/oa/car/application/chooseAuditDetail', { id: id })
  },
  // 选择审批人
  chooseAuditUser (params) {
    console.log(params)
    return get('/oa/car/application/chooseAuditUser', params)
  },
  // 流程记录列表
  progressLogList (params) {
    return get('/oa/flow/record/list', params)
  },
  // 我审查的用车申请列表
  myVerifyApplyCarList (params) {
    return get('/oa/car/application/myAuditList', params)
  },
  // 获取审查状态字典值
  getVerifyStatus (module) {
    return get('/oa/flow/node/operation/selectList', { moduleType: module })
  },
  // 审核人操作审核详情
  verifyUserAuditInfo (id) {
    return get('/oa/car/application/auditDetail', { id: id })
  },
  // 审批人审批
  verifyUserAudit (params) {
    return post('/oa/car/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  // 我调配的用车申请列表
  myDeployApplyCarList (params) {
    return get('/oa/car/application/myArrangeList', params)
  },
  // 新增车辆调配
  deployCarAdd (params) {
    return post('/oa/car/arrangement/add', params)
  },
  // 修改车辆调配
  deployCarEdit (params) {
    return post('/oa/car/arrangement/edit', params)
  },
  // 删除车辆调配
  deployCarDel (id) {
    return get(`/oa/car/arrangement/del/${id}`)
  },
  // 办结审批
  manageFinish (id) {
    return post('/oa/car/application/managerFinish', { id: id })
  },
  // 出车列表
  outCarList (data) {
    return post('/oa/car/application/getOutList', data)
  },
  // 修改出车信息
  outCarInfoEdit (data) {
    return post('/oa/car/arrangement/getOutEdit', data)
  },
  /**
   * 文明创建
   */
  // 考核对象主体新增
  examinebodyAdd (params) {
    return post('/oa/examinebody/add', params)
  },
  // 考核对象主体编辑
  examinebodyEdit (params) {
    return post('/oa/examinebody/edit', params)
  },
  // 考核对象主体列表
  examinebodyList (params) {
    return get('/oa/examinebody/list', params)
  },
  // 考核对象主体详情
  examinebodyInfo (id) {
    return post(`/oa/examinebody/info/${id}`)
  },
  // 考核对象主体删除
  examinebodyDel (id) {
    return post(`/oa/examinebody/del/${id}`)
  },
  // 考核对象主体批量删除
  examinebodyDels (ids) {
    return post('/oa/examinebody/dels', { ids: ids })
  },
  // 考核对象主体结果
  examinebodyResult (year) {
    return post('/oa/examinebody/ExamineResult', { year: year })
  },
  // 考核指标类型树
  getExamineTypeTree (params) {
    return post('/oa/examinetype/getExamineTypeTree', params)
  },
  // 考核指标类型列表
  getExamineTypeList (params) {
    return post('/oa/examinetype/list', params)
  },
  // 考核指标类型新增
  ExamineTypeAdd (params) {
    return post('/oa/examinetype/add', params)
  },
  // 考核指标类型编辑
  ExamineTypeEdit (params) {
    return post('/oa/examinetype/edit', params)
  },
  // 考核指标类型详情
  ExamineTypeInfo (id) {
    return post(`/oa/examinetype/info/${id}`)
  },
  // 考核指标类型删除
  ExamineTypeDel (id) {
    return post(`/oa/examinetype/del/${id}`)
  },
  // 考核指标类型批量删除
  ExamineTypeDels (ids) {
    return post('/oa/examinetype/dels', { ids: ids })
  },
  // 考核目标任务新增
  ExamineTaskAdd (params) {
    return post('/oa/examinetarget/add', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  // 考核目标任务编辑
  ExamineTaskEdit (params) {
    return post('/oa/examinetarget/edit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  // 考核目标任务列表
  ExamineTaskList (params) {
    return post('/oa/examinetarget/list', params)
  },
  // 考核目标导出
  ExamineResultExport (year) {
    return exportFile('/oa/examinebody/exportExcel', { year: year })
  },
  // 考核目标任务详情
  ExamineTaskInfo (id) {
    return post(`/oa/examinetarget/info/${id}`)
  },
  // 考核目标任务新增
  ExamineTaskDel (id) {
    return post(`/oa/examinetarget/del/${id}`)
  },
  // 考核目标任务新增
  ExamineTaskDels (ids) {
    return post('/oa/examinetarget/dels', { ids: ids })
  },
  // 获取考核主体年份列表
  ExamineYearList () {
    return get('/oa/examinebody/getYearList')
  },
  // 报送信息新增
  submitInfoAdd (params) {
    return post('/oa/examineinfo/add', params)
  },
  // 报送信息编辑
  submitInfoEdit (params) {
    return post('/oa/examineinfo/edit', params)
  },
  // 报送信息详情
  submitInfoInfo (id) {
    return get(`/oa/examineinfo/info/${id}`)
  },
  // 报送信息列表
  submitInfoList (params) {
    return get('/oa/examineinfo/list', params)
  },
  // 报送信息删除
  submitInfoDel (id) {
    return get(`/oa/examineinfo/del/${id}`)
  },
  // 报送信息批量删除
  submitInfoDels (ids) {
    return post('/oa/examineinfo/dels', { ids: ids })
  },
  /**
   * 物品申领
   */
  // 物品分类树
  goodsTypeTree (params) {
    return post('/oa/item/type/list', params)
  },
  // 新增物品分类
  goodsTypeAdd (params) {
    return post('/oa/item/type/add', params)
  },
  // 编辑物品分类
  goodsTypeEdit (params) {
    return post('/oa/item/type/edit', params)
  },
  // 物品分类详情
  goodsTypeInfo (id) {
    return get(`/oa/item/type/info/${id}`)
  },
  // 物品分类删除
  goodsTypeDelete (id) {
    return get(`/oa/item/type/del/${id}`)
  },
  // 物品分类树
  goodsTypeChooseTree (params) {
    return get('/oa/item/type/chooseList', params)
  },
  // 物品新增
  goodsAdd (params) {
    return post('/oa/item/info/add', params)
  },
  // 物品编辑
  goodsEdit (params) {
    return post('/oa/item/info/edit', params)
  },
  // 物品列表
  goodsList (params) {
    return get('/oa/item/info/list', params)
  },
  // 物品详情
  goodsInfo (id) {
    return get(`/oa/item/info/info/${id}`)
  },
  // 物品删除
  goodsDelete (id) {
    return get(`/oa/item/info/del/${id}`)
  },
  // 我的物品申领列表
  myApplyGoodsList (params) {
    return get('/oa/item/application/myList', params)
  },
  // 所有物品申领列表
  applyGoodsList (params) {
    return get('/oa/item/application/allList', params)
  },
  /** 我的物品申领草稿箱列表 */
  applyGoodsDraftList (params) {
    return get('/oa/item/application/myDraftList', params)
  },
  // 物品申领审批单状态列表
  applyGoodsStatusList () {
    return get('/oa/item/application/statusList')
  },
  /** 新增物品申领 */
  applyGoodsSave (params) {
    return post('/oa/item/application/add', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 编辑物品申领 */
  applyGoodsEdit (params) {
    return post('/oa/item/application/edit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 删除物品申领 */
  applyGoodsDel (id) {
    return get('/oa/item/application/ownerDelete', { id: id })
  },
  /** 删除物品申领 */
  applyGoodsAdminDel (id) {
    return get('/oa/item/application/powerDelete', { id: id })
  },
  /** 物品申领详情 */
  applyGoodsInfo (id) {
    return get(`/oa/item/application/info/${id}`)
  },
  /** 我审批的物品申领单列表 */
  myApprovalApplyGoodsList (params) {
    return get('/oa/item/application/myAuditList', params)
  },
  /** 审批物品申领详情 */
  approvalApplyGoodsInfo (id) {
    return get('/oa/item/application/auditDetail', { id: id })
  },
  /** 审批物品申领详情 */
  auditApplyGoodsInfo (id) {
    return get('/oa/item/application/chooseAuditDetail', { id: id })
  },
  /** 审批物品审批 */
  approvalApplyGoods (params) {
    return post('/oa/item/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 办结物品审批 */
  applyGoodsFinish (id) {
    return get('/oa/item/application/finish', { id: id })
  },
  /** 物品申领选择审批人 */
  applyGoodsChooseUser (params) {
    return get('/oa/item/application/chooseAuditUser', params)
  },
  // 发文审批
  /** 新增发文审批 */
  postWordAdd (params) {
    return post('/oa/publish/application/add', params)
  },
  /** 编辑发文审批 */
  postWordEdit (params) {
    return post('/oa/publish/application/edit', params)
  },
  /** 发文审批单状态列表 */
  postWordStatusList () {
    return get('/oa/publish/application/statusList')
  },
  /** 我发起的发文审批列表 */
  myPostWordList (params) {
    return get('/oa/publish/application/myList', params)
  },
  /** 我发起的发文审批草稿箱列表 */
  myPostWordDraftList (params) {
    return get('/oa/publish/application/myDraftList', params)
  },
  /** 我审核的发文审批列表 */
  myApprovalPostWordList (params) {
    return get('/oa/publish/application/myAuditList', params)
  },
  /** 发文审批所有列表 */
  postWordAllList (params) {
    return get('/oa/publish/application/allList', params)
  },
  /** 删除我发布的发文审批申请 */
  myPostWordDelete (id) {
    return get('/oa/publish/application/ownerDelete', { id: id })
  },
  /** 管理删除 发文审批 */
  postWordDelete (id) {
    return get('/oa/publish/application/powerDelete', { id: id })
  },
  /** 发文审批详情 */
  postWordInfo (id) {
    return get(`/oa/publish/application/info/${id}`)
  },
  /** 发文审批单审批详情 */
  approvalPostWordInfo (id) {
    return get('/oa/publish/application/auditDetail', { id: id })
  },
  /** 发文审批审核 */
  approvalPostWord (params) {
    return post('/oa/publish/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 发文审批选择审批人 */
  postWordChooseUser (params) {
    return post('/oa/publish/application/chooseAuditUser', params)
  },
  /** 发文审批办结 */
  postWordAddFinish (id) {
    return post('/oa/publish/application/finish', { id: id })
  },
  // 信息发布审批
  /** 信息发布平台列表 */
  infoPublishPlatformList (params) {
    return get('/oa/info/platform/list', params)
  },
  /** 新增信息发布平台 */
  infoPublishPlatformAdd (params) {
    return post('/oa/info/platform/add', params)
  },
  /** 编辑信息发布平台 */
  infoPublishPlatformEdit (params) {
    return post('/oa/info/platform/edit', params)
  },
  /** 删除信息发布平台 */
  infoPublishPlatformDel (id) {
    return get(`/oa/info/platform/del/${id}`)
  },
  /** 批量删除信息发布平台 */
  infoPublishPlatformDels (ids) {
    return post('/oa/info/platform/dels', { ids: ids })
  },
  /** 信息发布平台栏目列表 */
  platformColumnList (params) {
    return get('/oa/info/column/list', params)
  },
  /** 信息发布平台栏目新增 */
  platformColumnAdd (params) {
    return get('/oa/info/column/add', params)
  },
  /** 信息发布平台栏目编辑 */
  platformColumnEdit (params) {
    return get('/oa/info/column/edit', params)
  },
  /** 信息发布平台栏目删除 */
  platformColumnDel (id) {
    return get(`/oa/info/column/del/${id}`)
  },
  /** 信息发布平台栏目批量删除 */
  platformColumnDels (ids) {
    return get('/oa/info/column/dels', { ids: ids })
  },
  /** 信息发布审批 新增 */
  infoPublishAdd (params) {
    return post('/oa/info/audit/add', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 信息发布审批 编辑 */
  infoPublishEdit (params) {
    return post('/oa/info/audit/edit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 信息发布审批 所有列表 */
  infoPublishAllList (params) {
    return get('/oa/info/audit/allList', params)
  },
  /** 信息发布审批 我的申请列表 */
  infoPublishMyList (params) {
    return get('/oa/info/audit/myList', params)
  },
  /** 信息发布审批 我审查的列表 */
  infoPublishMyAuditList (params) {
    return get('/oa/info/audit/myAuditList', params)
  },
  /** 信息发布审批 审核状态列表 */
  infoPublishStatusList () {
    return get('/oa/info/audit/statusList')
  },
  /** 信息发布审批 删除 */
  infoPublishDel (id) {
    return get('/oa/info/audit/ownerDelete', { id: id })
  },
  /** 信息发布审批 管理员删除 */
  infoPublishAdminDel (id) {
    return get('/oa/info/audit/powerDelete', { id: id })
  },
  /** 信息发布审批 详情 */
  infoPublishInfo (id) {
    return get(`/oa/info/audit/info/${id}`)
  },
  /** 信息发布审批 选择审批人详情 */
  infoPublishChooseUserInfo (id) {
    return get('/oa/info/audit/chooseAuditDetail', { id: id })
  },
  /** 信息发布审批 审批详情 */
  infoPublishAuditInfo (id) {
    return get('/oa/info/audit/auditDetail', { id: id })
  },
  /** 信息发布审批 选择审批人 */
  infoPublishChooseUser (params) {
    return post('/oa/info/audit/chooseAuditUser', params)
  },
  /** 信息发布审批 审批 */
  infoPublishAudit (params) {
    return post('/oa/info/audit/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 信息发布审批 办结 */
  infoPublishFinish (id) {
    return post('/oa/info/audit/finish', { id: id })
  },
  /** 信息发布审批 我的草稿箱列表 */
  infoPublishMyDraftList (params) {
    return get('/oa/info/audit/myDraftList', params)
  },
  // 来文来电
  /** 来文来电所有申请列表 */
  communicateAllApplyList (params) {
    return get('/oa/communicate/application/allList', params)
  },
  /** 来文来电我的申请列表 */
  communicateMyApplyList (params) {
    return get('/oa/communicate/application/myList', params)
  },
  /** 来文来电草稿箱申请列表 */
  communicateMyDraftApplyList (params) {
    return get('/oa/communicate/application/myDraftList', params)
  },
  /** 我审查的来文来电审批列表 */
  communicateApplyMyAuditList (params) {
    return get('/oa/communicate/application/myAuditList', params)
  },
  /** 来文(来电)申请 管理员删除 */
  communicateApplyAdminDel (id) {
    return get('/oa/communicate/application/powerDelete', { id: id })
  },
  // 来文来电状态列表
  communicateStatusList () {
    return get('/oa/communicate/application/statusList')
  },
  /** 来文(来电)申请 删除 */
  communicateApplyDel (id) {
    return get('/oa/communicate/application/ownerDelete', { id: id })
  },
  /** 来文(来电)申请 新增 */
  communicateApplyAdd (params) {
    return post('/oa/communicate/application/add', params)
  },
  /** 来文(来电)申请 编辑 */
  communicateApplyEdit (params) {
    return post('/oa/communicate/application/edit', params)
  },
  /** 来文(来电)申请 详情 */
  communicateApplyInfo (id) {
    return get(`/oa/communicate/application/info/${id}`)
  },
  /** 来文(来电)申请 审批详情 */
  communicateApplyAuditInfo (id) {
    return get('/oa/communicate/application/auditDetail', { id: id })
  },
  /** 来文(来电)申请 审批 */
  communicateApplyAudit (params) {
    return post('/oa/communicate/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 来文(来电)申请 选择审批人 */
  communicateChooseAuditer (params) {
    return post('/oa/communicate/application/chooseAuditUser', params)
  },
  /** 来文(来电)申请 选择审批人详情 */
  communicateChooseAuditerInfo (id) {
    return get('/oa/communicate/application/chooseAuditDetail', { id: id })
  },
  /** 来文(来电)申请 办结 */
  communicateFinish (id) {
    return get('/oa/communicate/application/finish', { id: id })
  },
  /** 内部请示审批 新增 */
  internalRequestAdd (params) {
    return post('/oa/internal/application/add', params)
  },
  /** 内部请示审批 编辑 */
  internalRequestEdit (params) {
    return post('/oa/internal/application/edit', params)
  },
  /** 内部请示审批 详情 */
  internalRequestInfo (id) {
    return get(`/oa/internal/application/info/${id}`)
  },
  /** 内部请示审批 审批详情 */
  internalRequestAuditInfo (id) {
    return get('/oa/internal/application/auditDetail', { id: id })
  },
  /** 内部请示审批 详情 */
  internalRequestChooseUserInfo (id) {
    return get('/oa/internal/application/chooseAuditDetail', { id: id })
  },
  /** 内部请示审批 所有列表 */
  internalRequestAllList (params) {
    return get('/oa/internal/application/allList', params)
  },
  /** 内部请示审批 我的申请列表 */
  internalRequestApplyList (params) {
    return get('/oa/internal/application/myList', params)
  },
  /** 内部请示审批 草稿箱列表 */
  internalRequestDraftList (params) {
    return get('/oa/internal/application/myDraftList', params)
  },
  /** 内部请示审批 我的审核列表 */
  internalRequestApprovalList (params) {
    return get('/oa/internal/application/myAuditList', params)
  },
  /** 内部请示审批 审核状态列表 */
  internalRequestStatusList (params) {
    return get('/oa/internal/application/statusList', params)
  },
  /** 内部请示审批 管理员删除 */
  internalRequestAdminDel (id) {
    return get('/oa/internal/application/powerDelete', { id: id })
  },
  /** 内部请示审批 删除 */
  internalRequestDel (id) {
    return get('/oa/internal/application/ownerDelete', { id: id })
  },
  /** 内部请示审批 选择审批人 */
  internalRequestChooseUser (params) {
    return post('/oa/internal/application/chooseAuditUser', params)
  },
  /** 内部请示审批 审批 */
  internalRequestAudit (params) {
    return post('/oa/internal/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 内部请示审批 办结 */
  internalRequestFinish (id) {
    return post('/oa/internal/application/finish', { id: id })
  },
  /** 外出审批 新增 */
  goOutAdd (params) {
    return post('/oa/leave/application/add', params)
  },
  /** 外出审批 编辑 */
  goOutEdit (params) {
    return post('/oa/leave/application/edit', params)
  },
  /** 外出审批 详情 */
  goOutInfo (id) {
    return get(`/oa/leave/application/info/${id}`)
  },
  /** 外出审批 审批详情 */
  goOutAuditInfo (id) {
    return get('/oa/leave/application/auditDetail', { id: id })
  },
  /** 外出审批 详情 */
  goOutChooseUserInfo (id) {
    return get('/oa/leave/application/chooseAuditDetail', { id: id })
  },
  /** 外出审批 所有列表 */
  goOutAllList (params) {
    return get('/oa/leave/application/allList', params)
  },
  /** 外出审批 我的申请列表 */
  goOutApplyList (params) {
    return get('/oa/leave/application/myList', params)
  },
  /** 外出审批 草稿箱列表 */
  goOutDraftList (params) {
    return get('/oa/leave/application/myDraftList', params)
  },
  /** 外出审批 我的审核列表 */
  goOutApprovalList (params) {
    return get('/oa/leave/application/myAuditList', params)
  },
  /** 外出审批 审核状态列表 */
  goOutStatusList (params) {
    return get('/oa/leave/application/statusList', params)
  },
  /** 外出审批 管理员删除 */
  goOutAdminDel (id) {
    return get('/oa/leave/application/powerDelete', { id: id })
  },
  /** 外出审批 删除 */
  goOutDel (id) {
    return get('/oa/leave/application/ownerDelete', { id: id })
  },
  /** 外出审批 选择审批人 */
  goOutChooseUser (params) {
    return post('/oa/leave/application/chooseAuditUser', params)
  },
  /** 外出审批 审批 */
  goOutAudit (params) {
    return post('/oa/leave/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 外出审批 办结 */
  goOutFinish (id) {
    return post('/oa/leave/application/finish', { id: id })
  },
  /** 接待审批 新增 */
  receptionAdd (params) {
    return post('/oa/reception/application/add', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 接待审批 编辑 */
  receptionEdit (params) {
    return post('/oa/reception/application/edit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 接待审批 详情 */
  receptionInfo (id) {
    return get(`/oa/reception/application/info/${id}`)
  },
  /** 接待审批 审批详情 */
  receptionAuditInfo (id) {
    return get('/oa/reception/application/auditDetail', { id: id })
  },
  /** 接待审批 详情 */
  receptionChooseUserInfo (id) {
    return get('/oa/reception/application/chooseAuditDetail', { id: id })
  },
  /** 接待审批 所有列表 */
  receptionAllList (params) {
    return get('/oa/reception/application/allList', params)
  },
  /** 接待审批 我的申请列表 */
  receptionApplyList (params) {
    return get('/oa/reception/application/myList', params)
  },
  /** 接待审批 草稿箱列表 */
  receptionDraftList (params) {
    return get('/oa/reception/application/myDraftList', params)
  },
  /** 接待审批 我的审核列表 */
  receptionApprovalList (params) {
    return get('/oa/reception/application/myAuditList', params)
  },
  /** 接待审批 审核状态列表 */
  receptionStatusList (params) {
    return get('/oa/reception/application/statusList', params)
  },
  /** 接待审批 管理员删除 */
  receptionAdminDel (id) {
    return get('/oa/reception/application/powerDelete', { id: id })
  },
  /** 接待审批 删除 */
  receptionDel (id) {
    return get('/oa/reception/application/ownerDelete', { id: id })
  },
  /** 接待审批 选择审批人 */
  receptionChooseUser (params) {
    return post('/oa/reception/application/chooseAuditUser', params)
  },
  /** 接待审批 审批 */
  receptionAudit (params) {
    return post('/oa/reception/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 接待审批 办结 */
  receptionFinish (id) {
    return post('/oa/reception/application/finish', { id: id })
  },
  /** 维修审批 新增 */
  repairAdd (params) {
    return post('/oa/repair/application/add', params)
  },
  /** 维修审批 编辑 */
  repairEdit (params) {
    return post('/oa/repair/application/edit', params)
  },
  /** 维修审批 详情 */
  repairInfo (id) {
    return get(`/oa/repair/application/info/${id}`)
  },
  /** 维修审批 审批详情 */
  repairAuditInfo (id) {
    return get('/oa/repair/application/auditDetail', { id: id })
  },
  /** 维修审批 详情 */
  repairChooseUserInfo (id) {
    return get('/oa/repair/application/chooseAuditDetail', { id: id })
  },
  /** 维修审批 所有列表 */
  repairAllList (params) {
    return get('/oa/repair/application/allList', params)
  },
  /** 维修审批 我的申请列表 */
  repairApplyList (params) {
    return get('/oa/repair/application/myList', params)
  },
  /** 维修审批 草稿箱列表 */
  repairDraftList (params) {
    return get('/oa/repair/application/myDraftList', params)
  },
  /** 维修审批 我的审核列表 */
  repairApprovalList (params) {
    return get('/oa/repair/application/myAuditList', params)
  },
  /** 维修审批 审核状态列表 */
  repairStatusList (params) {
    return get('/oa/repair/application/statusList', params)
  },
  /** 维修审批 管理员删除 */
  repairAdminDel (id) {
    return get('/oa/repair/application/powerDelete', { id: id })
  },
  /** 维修审批 删除 */
  repairDel (id) {
    return get('/oa/repair/application/ownerDelete', { id: id })
  },
  /** 维修审批 选择审批人 */
  repairChooseUser (params) {
    return post('/oa/repair/application/chooseAuditUser', params)
  },
  /** 维修审批 审批 */
  repairAudit (params) {
    return post('/oa/repair/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 维修审批 办结 */
  repairFinish (id) {
    return post('/oa/repair/application/finish', { id: id })
  },
  // 新增日期提醒
  scheduleAdd (params) {
    return post('/oa/schedule/remind/add', params)
  },
  // 编辑日程提醒
  scheduleEdit (params) {
    return post('/oa/schedule/remind/edit', params)
  },
  // 日程提醒列表
  scheduleList (params) {
    return get('/oa/schedule/remind/list', params)
  },
  // 日程详情
  scheduleInfo (id) {
    return get(`/oa/schedule/remind/info/${id}`)
  },
  // 日程删除
  scheduleDel (id) {
    return get(`/oa/schedule/remind/del/${id}`)
  },
  // 首页模块配置新增
  homeModuleConfigAdd (params) {
    return post('/oa/audit/module/config/add', params)
  },
  // 首页模块配置编辑
  homeModuleConfigEdit (params) {
    return post('/oa/audit/module/config/edit', params)
  },
  // 首页模块配置列表
  homeModuleConfigList (params) {
    return get('/oa/audit/module/config/list', params)
  },
  // 首页模块配置详情
  homeModuleConfigInfo (id) {
    return get(`/oa/audit/module/config/info/${id}`)
  },
  // 首页模块配置删除
  homeModuleConfigDel (id) {
    return get(`/oa/audit/module/config/del/${id}`)
  },
  /** 报账审批 新增 */
  reimbursementAdd (params) {
    return post('/oa/reimbursement/application/add', params)
  },
  /** 报账审批 编辑 */
  reimbursementEdit (params) {
    return post('/oa/reimbursement/application/edit', params)
  },
  /** 报账审批 详情 */
  reimbursementInfo (id) {
    return get(`/oa/reimbursement/application/info/${id}`)
  },
  /** 报账审批 审批详情 */
  reimbursementAuditInfo (id) {
    return get('/oa/reimbursement/application/auditDetail', { id: id })
  },
  /** 报账审批 详情 */
  reimbursementChooseUserInfo (id) {
    return get('/oa/reimbursement/application/chooseAuditDetail', { id: id })
  },
  /** 报账审批 所有列表 */
  reimbursementAllList (params) {
    return get('/oa/reimbursement/application/allList', params)
  },
  /** 报账审批 我的申请列表 */
  reimbursementApplyList (params) {
    return get('/oa/reimbursement/application/myList', params)
  },
  /** 报账审批 草稿箱列表 */
  reimbursementDraftList (params) {
    return get('/oa/reimbursement/application/myDraftList', params)
  },
  /** 报账审批 我的审核列表 */
  reimbursementApprovalList (params) {
    return get('/oa/reimbursement/application/myAuditList', params)
  },
  /** 报账审批 审核状态列表 */
  reimbursementStatusList (params) {
    return get('/oa/reimbursement/application/statusList', params)
  },
  /** 报账审批 管理员删除 */
  reimbursementAdminDel (id) {
    return get('/oa/reimbursement/application/powerDelete', { id: id })
  },
  /** 报账审批 删除 */
  reimbursementDel (id) {
    return get('/oa/reimbursement/application/ownerDelete', { id: id })
  },
  /** 报账审批 选择审批人 */
  reimbursementChooseUser (params) {
    return post('/oa/reimbursement/application/chooseAuditUser', params)
  },
  /** 报账审批 审批 */
  reimbursementAudit (params) {
    return post('/oa/reimbursement/application/audit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  /** 报账审批 办结 */
  reimbursementFinish (id) {
    return post('/oa/reimbursement/application/finish', { id: id })
  },
  /** 视频会议列表 */
  videoMeetingList (params) {
    return post('/zyMeeting/list', params)
  },
  /** 视频会议新增 */
  videoMeetingAdd (params) {
    return post('/zyMeeting/add', params)
  },
  /** 视频会议编辑 */
  videoMeetingEdit (params) {
    return post('/zyMeeting/edit', params)
  },
  /** 视频会议删除 */
  videoMeetingDel (id) {
    return post('/zyMeeting/dels', { ids: id })
  },
  /** 视频会议详情 */
  videoMeetingInfo (id) {
    return post(`/zyMeeting/info/${id}`)
  },
  /** 华为云账号列表 */
  zyCloudAccountList (params) {
    return post('/zyCloudAccount/list', params)
  },
  /** 华为云账号新增 */
  zyCloudAccountAdd (params) {
    return post('/zyCloudAccount/save', params)
  },
  /** 华为云账号编辑 */
  zyCloudAccountEdit (params) {
    return post('/zyCloudAccount/edit', params)
  },
  /** 华为云账号删除 */
  zyCloudAccountDel (id) {
    return post('/zyCloudAccount/delete', { id: id })
  },
  /** 华为云账号详情 */
  zyCloudAccountInfo (id) {
    return post('/zyCloudAccount/form', { id: id })
  }

}
export default officeAutomation
