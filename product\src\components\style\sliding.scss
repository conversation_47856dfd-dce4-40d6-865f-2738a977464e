.xyl-sliding {
  width: 100%;
  margin: auto;

  .xyl-sliding-wrap {
    width: 100%;
    overflow: hidden;

    &:after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background-color: #e4e7ed;
      z-index: 2;
    }

    .xyl-sliding-prev,
    .xyl-sliding-next {
      position: absolute;
      top: 2px;
      height: calc(100% - 4px);
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      z-index: 9;
    }

    .xyl-sliding-prev {
      left: 0;
    }

    .xyl-sliding-next {
      right: 0;
    }

    .xyl-sliding-scroll {
      white-space: nowrap;
      position: relative;
      transition: transform .3s;
      float: left;
      display: block;
      z-index: 3;

      .xyl-sliding-active-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 2px;
        background-color: $zy-color;
        transition: transform .3s cubic-bezier(.645, .045, .355, 1);
        list-style: none;
      }

      .xyl-sliding-item {
        display: inline-block;
        height: 40px;
        line-height: 40px;
        cursor: pointer;
        margin-right: 38px;
      }

      .is-active {
        color: $zy-color;
      }
    }
  }
}