<template>
  <div class="customTopicDetails">
    <div class="custom-project-details-title">{{details.title}}</div>
    <div class="custom-project-details-xx">
      <div class="custom-project-details-source">发布人：{{name}}</div>
      <div class="custom-project-details-tiem">发布时间：{{details.publishTime}}</div>
    </div>
    <div class="cover">
      <div class="cover-text">封面图</div>
      <div class="cover-img"
           v-if="details.coverImg">
        <img :src="details.coverImg.fullUrl"
             alt="">
      </div>
    </div>
    <div class="theme">
      <div class="theme-text">主题图</div>
      <div class="theme-img"
           v-if="details.themeImg">
        <img :src="details.themeImg.fullUrl"
             alt="">
      </div>
    </div>
    <div class="custom-project-details-content"
         v-html="details.content"></div>
  </div>
</template>
<script>
export default {
  name: 'customTopicDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id', 'name'],
  mounted () {
    if (this.id) {
      this.customTopicListInfo()
    }
  },
  methods: {
    async customTopicListInfo () {
      const res = await this.$api.appManagement.customTopicListInfo(this.id)
      var { data } = res
      this.details = data
      // if (data.externalLinks) {
      //   window.open(data.externalLinks, '_blank')
      // }
    }
  }
}
</script>
<style lang="scss">
.customTopicDetails {
  width: 1000px;
  height: 100%;

  div {
    line-height: 22px;
  }

  .custom-project-details-title {
    width: 684px;
    margin: auto;
    line-height: 28px;
    font-size: 20px;
    padding: 20px 0;
    text-align: center;
  }

  .custom-project-details-xx {
    height: 46px;
    background: #f5f7f8;
    box-shadow: 0px -1px 0px 0px rgba(230, 230, 230, 1);
    display: flex;
    align-items: center;
    justify-content: center;

    .custom-project-details-source {
      font-size: 14px;
      line-height: 20px;
    }

    .custom-project-details-tiem {
      margin-left: 72px;
      font-size: 14px;
      line-height: 20px;
    }
  }

  .custom-project-details-content {
    width: 984px;
    padding: 22px 88px;
    margin: auto;
  }

  .cover {
    width: 984px;
    padding: 22px 88px;
    margin: auto;
    display: flex;
    margin-bottom: 24px;

    .cover-text {
      padding-right: 30px;
      font-size: 14px;
    }

    .cover-img {
      width: 500px;
      height: 300px;

      img {
        // width: 100%;
        height: 100%;
      }
    }
  }

  .theme {
    width: 984px;
    padding: 22px 88px;
    margin: auto;
    display: flex;

    .theme-text {
      padding-right: 30px;
      font-size: 14px;
    }

    .theme-img {
      width: 500px;
      height: 300px;

      img {
        // width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
