const linkTogether = [
  {
    path: '/pairManage',
    name: 'pairManage',
    component: () => import(/* 常委会结对人大代表 */ '@/views/linkTogether/pairManage/pairManage')
  },
  {
    path: '/representativeLetterAdmin',
    name: 'representativeLetterAdmin',
    component: () => import(/* 常委会查看代表来信 */ '@/views/linkTogether/representativeLetterAdmin/representativeLetterAdmin')
  },
  {
    path: '/mailBox-list',
    name: 'mailBoxList',
    component: () => import(/* 回复列表 */ '@/views/linkTogether/representativeLetterAdmin/mail-reply-list')
  },
  {
    path: '/link-representative-make-up',
    name: 'link-representative-make-up',
    component: () => import(/* 联系代表情况补录 */ '@/views/linkTogether/link-representative-make-up/link-representative-make-up')
  },
  {
    path: '/situationCount',
    name: 'situationCount',
    component: () => import(/* 联系代表情况统计 */ '@/views/linkTogether/situation-count/situation-count')
  },
  {
    path: '/CommitteeLetterAdmin',
    name: 'CommitteeLetterAdmin',
    component: () => import(/* 常委会查看代表和选民来信 */ '@/views/linkTogether/committee-letter-admin/committee-letter-admin')
  },
  {
    path: '/voterLetter',
    name: 'voterLetter',
    component: () => import(/* 选民来信 */ '@/views/linkTogether/representative-link-voter/voter-letter/voter-letter')
  },
  {
    path: '/representativeLectern',
    name: 'representativeLectern',
    component: () => import(/* 代表宣讲台 */ '@/views/linkTogether/representative-link-voter/representative-lectern/representative-lectern')
  },
  {
    path: '/voterInformationManage',
    name: 'voterInformationManage',
    component: () => import(/* 选民信息管理 */ '@/views/linkTogether/representative-link-voter/voter-information-manage/voter-information-manage')
  },
  {
    path: '/voterTalk',
    name: 'voterTalk',
    component: () => import(/* 选民捎话台 */ '@/views/linkTogether/representative-link-voter/voter-talk/voter-talk')
  }

]
export default linkTogether
