const communicationlist = [
  // {
  //   path: '/dataDownload', // 文件资料
  //   name: 'dataDownload'
  // },
  // {
  //   path: '/notification-haiNan', // 通知公告
  //   name: 'notification'
  // },
  // {
  //   path: '/PoliciesRegulations', // 政策法规
  //   name: 'PoliciesRegulations'
  // },
  {
    path: '/messageManagement', // 发件箱
    name: 'messageManagement',
    parent: 'InsideInformation'
  },
  {
    path: '/Outbox', // 发件箱
    name: 'Outbox',
    parent: 'InsideInformation'
  },
  {
    path: '/Inbox', // 收件箱
    name: 'Inbox',
    parent: 'InsideInformation'
  },
  {
    path: '/informationDrafts', // 收件箱
    name: 'informationDrafts',
    parent: 'InsideInformation'
  },
  {
    path: '/recyclebin', // 收件箱
    name: 'recyclebin',
    parent: 'InsideInformation'
  }
  // {
  //   path: '/addressBook', // 通讯录
  //   name: 'address-book',
  //   parent: 'mailList'
  // },
  // {
  //   path: '/addressBookGroup', // 通讯录
  //   name: 'address-book-group',
  //   parent: 'mailList'
  // }
]
const Communication = communicationlist.map(route => {
  if (!route.parent) {
    route = {
      ...route,
      component: () => import(/* webpackChunkName: "[request]" */'@/views/Communication/' + route.name + '/' + route.name + '.vue')
    }
  } else {
    route = {
      ...route,
      component: () => import(/* webpackChunkName: "[request]" */'@/views/Communication/' + route.parent + '/' + route.name + '/' + route.name + '.vue')
    }
  }

  return route
})
export default Communication
