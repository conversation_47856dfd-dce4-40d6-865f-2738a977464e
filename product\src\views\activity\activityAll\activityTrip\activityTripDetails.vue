<template>
  <div class="activityTripDetails details">
    <div class="details-title">日程行程详情</div>
    <div class="details-item-box">
      <div class="details-item">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.title}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">提交人</div>
        <div class="details-item-value">{{details.userName}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">提交时间</div>
        <div class="details-item-value">{{details.dateTime}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">附件</div>
        <div class="details-item-value">
          <div class="details-item-file"
               v-for="(item, index) in details.attachmentList"
               :key="index"
               @click="fileClick(item)">{{item.fileName}}</div>
        </div>
      </div>
      <div class="details-content"
           v-html="details.content"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'activityTripDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.activityscheduleInfo()
  },
  methods: {
    async activityscheduleInfo () {
      const res = await this.$api.activity.activityscheduleInfo(this.id)
      var { data } = res
      this.details = data
    },
    fileClick (data) {
      this.$api.proposal.downloadFile({ id: data.id }, data.fileName)
    }
  }
}
</script>
<style lang="scss">
.activityTripDetails {
  padding: 24px;
  .details-content {
    width: 100%;
    padding: 40px;
    line-height: 22px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
