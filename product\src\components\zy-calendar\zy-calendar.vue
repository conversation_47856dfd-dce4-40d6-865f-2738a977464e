<template>
  <div class="zy-calendar">
    <div class="zy-calendar-selected"
         v-if="showHeader">
      <transition name="fadeY">
        <div class="years"
             v-if="yearsshow">{{years}}</div>
      </transition>
      <transition name="fadeY">
        <div class="time"
             v-if="flag">{{month+'&nbsp;&nbsp;'+Whatday}}</div>
      </transition>
    </div>
    <section class="zy_container">
      <div class="zy_content_all">
        <div class="zy_top_changge">
          <li @click="PreMonth(myDate,false)">
            <div class="zy_jiantou1"></div>
          </li>
          <li class="zy_content_li">{{dateTop}}</li>
          <li @click="NextMonth(myDate,false)">
            <div class="zy_jiantou2"></div>
          </li>
        </div>
        <div class="zy_content">
          <div class="zy_content_item"
               v-for="(tag,index) in textTop"
               :key="index">
            <div class="zy_top_tag">{{tag}}</div>
          </div>
        </div>
        <div class="zy_content">
          <div class="zy_content_item"
               v-for="(item,index) in list"
               :key="index"
               @click="clickDay(item,index)">
            <div class="zy_item_date"
                 :class="[{ zy_isMark: item.isMark},{zy_other_dayhide:item.otherMonth!=='nowMonth'},{zy_want_dayhide:item.dayHide},{zy_isToday:item.isToday},{zy_chose_day:item.chooseDay},setClass(item)]">{{item.id}}</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import timeUtil from './timeUtil'
export default {
  name: 'zyCalendar',
  data () {
    return {
      years: '',
      month: '',
      Whatday: '',
      yearsshow: true,
      flag: true,
      myDate: [],
      list: [],
      historyChose: [],
      dateTop: ''
    }
  },
  props: {
    markDate: {
      type: Array,
      default: () => []
    },
    markDateMore: {
      type: Array,
      default: () => []
    },
    textTop: {
      type: Array,
      default: () => ['一', '二', '三', '四', '五', '六', '日']
    },
    sundayStart: {
      type: Boolean,
      default: () => false
    },
    agoDayHide: {
      type: String,
      default: '0'
    },
    futureDayHide: {
      type: String,
      default: '2554387200'
    },
    showHeader: {
      type: Boolean,
      default: false
    }
  },
  created () {
    this.intStart()
    this.myDate = new Date()
    this.selected(this.getNowFormatDate(this.myDate))
  },
  methods: {
    getWeekDate (data) {
      var now = new Date(data)
      var day = now.getDay()
      var weeks = new Array('星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六')// eslint-disable-line
      var week = weeks[day]
      return week
    },
    getNowFormatDate (data) {
      var date = data
      var seperator1 = '-'
      var year = date.getFullYear()
      var month = date.getMonth() + 1
      var strDate = date.getDate()
      if (month >= 1 && month <= 9) {
        month = '0' + month
      }
      if (strDate >= 0 && strDate <= 9) {
        strDate = '0' + strDate
      }
      var currentdate = year + seperator1 + month + seperator1 + strDate
      return currentdate
    },
    selected (data) {
      this.Whatday = this.getWeekDate(data)
      if (this.years !== data.slice(0, 4)) {
        this.years = data.slice(0, 4)
        this.yearsshow = false
        setTimeout(() => {
          this.yearsshow = true
        }, 200)
      }
      if (this.month !== data.slice(5).replace('/', '-')) {
        this.month = data.slice(5).replace('/', '-')
        this.flag = false
        setTimeout(() => {
          this.flag = true
        }, 200)
      }
    },
    intStart () {
      timeUtil.sundayStart = this.sundayStart
    },
    setClass (data) {
      const obj = {}
      obj[data.markClassName] = data.markClassName
      return obj
    },
    clickDay: function (item, index) {
      if (item.otherMonth === 'nowMonth' && !item.dayHide) {
        this.getList(this.myDate, item.date)
      }
      if (item.otherMonth !== 'nowMonth') {
        item.otherMonth === 'preMonth'
          ? this.PreMonth(item.date)
          : this.NextMonth(item.date)
      }
    },
    ChoseMonth: function (date, isChosedDay = true) {
      date = timeUtil.dateFormat(date)
      this.myDate = new Date(date)
      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))
      if (isChosedDay) {
        this.getList(this.myDate, date, isChosedDay)
      } else {
        this.getList(this.myDate)
      }
    },
    PreMonth: function (date, isChosedDay = true) {
      date = timeUtil.dateFormat(date)
      this.myDate = timeUtil.getOtherMonth(this.myDate, 'preMonth')
      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))
      if (isChosedDay) {
        this.getList(this.myDate, date, isChosedDay)
      } else {
        this.getList(this.myDate)
      }
    },
    NextMonth: function (date, isChosedDay = true) {
      date = timeUtil.dateFormat(date)
      this.myDate = timeUtil.getOtherMonth(this.myDate, 'nextMonth')
      this.$emit('changeMonth', timeUtil.dateFormat(this.myDate))
      if (isChosedDay) {
        this.getList(this.myDate, date, isChosedDay)
      } else {
        this.getList(this.myDate)
      }
    },
    forMatArgs: function () {
      let markDate = this.markDate
      let markDateMore = this.markDateMore
      markDate = markDate.map(k => {
        return timeUtil.dateFormat(k)
      })
      markDateMore = markDateMore.map(k => {
        k.date = timeUtil.dateFormat(k.date)
        return k
      })
      return [markDate, markDateMore]
    },
    getList: function (date, chooseDay, isChosedDay = true) {
      const [markDate, markDateMore] = this.forMatArgs()
      this.dateTop = `${date.getFullYear()}年${date.getMonth() + 1}月`
      const arr = timeUtil.getMonthList(this.myDate)
      for (let i = 0; i < arr.length; i++) {
        let markClassName = ''
        const k = arr[i]
        k.chooseDay = false
        const nowTime = k.date
        const t = new Date(nowTime).getTime() / 1000
        for (const c of markDateMore) {
          if (c.date === nowTime) {
            markClassName = c.className || ''
          }
        }
        k.markClassName = markClassName
        k.isMark = markDate.indexOf(nowTime) > -1
        k.dayHide = t < this.agoDayHide || t > this.futureDayHide
        if (k.isToday) {
          this.$emit('isToday', nowTime)
        }
        const flag = !k.dayHide && k.otherMonth === 'nowMonth'
        if (chooseDay && chooseDay === nowTime && flag) {
          this.$emit('choseDay', nowTime)
          this.selected(nowTime)
          this.historyChose.push(nowTime)
          k.chooseDay = true
        } else if (
          this.historyChose[this.historyChose.length - 1] === nowTime &&
          !chooseDay &&
          flag
        ) {
          k.chooseDay = true
        }
      }
      this.list = arr
    }
  },
  mounted () {
    this.getList(this.myDate)
  },
  watch: {
    markDate: {
      handler (val, oldVal) {
        this.getList(this.myDate)
      },
      deep: true
    },
    markDateMore: {
      handler (val, oldVal) {
        this.getList(this.myDate)
      },
      deep: true
    },
    agoDayHide: {
      handler (val, oldVal) {
        this.getList(this.myDate)
      },
      deep: true
    },
    futureDayHide: {
      handler (val, oldVal) {
        this.getList(this.myDate)
      },
      deep: true
    },
    sundayStart: {
      handler (val, oldVal) {
        this.intStart()
        this.getList(this.myDate)
      },
      deep: true
    }
  }
}
</script>
<style lang="scss">
@import "./zy-calendar.scss";
</style>
