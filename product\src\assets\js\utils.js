import AES from 'crypto-js/aes'
import pad from 'crypto-js/pad-pkcs7'
import mode from 'crypto-js/mode-ecb'
import enc from 'crypto-js/enc-utf8'
import md5 from 'crypto-js/md5'
export default {
  // 加密
  encrypt (word, keyStr) {
    var key = enc.parse(keyStr)
    var srcs = enc.parse(word)
    var encrypted = AES.encrypt(srcs, key, { mode: mode, padding: pad })
    return encrypted.toString()
  },
  // 解密
  decrypt (word, keyStr) {
    var key = enc.parse(keyStr)
    var decrypt = AES.decrypt(word, key, { mode: mode, padding: pad })
    return enc.stringify(decrypt).toString()
  },
  // 加密
  Md5Js (text) {
    return md5(text).toString()
  },
  tmp (type) {
    var tmp = Date.parse(new Date()).toString()
    if (type) {
      tmp = tmp.substr(0, 10)
    }
    return tmp
  },
  tmpNte () {
    let date = new Date()
    console.log(date)
    const Y = date.getFullYear()
    const M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1)
    const D = date.getDate() < 10 ? ('0' + date.getDate()) : date.getDate()
    const hours = date.getHours() < 10 ? ('0' + date.getHours()) : date.getHours()
    const minutes = date.getMinutes() < 10 ? ('0' + date.getMinutes()) : date.getMinutes()
    const seconds = date.getSeconds() < 10 ? ('0' + date.getSeconds()) : date.getSeconds()
    date = Y + '' + M + '' + D + '' + hours + '' + minutes + '' + seconds
    return date
  }
}
