<template>
  <div class="vote-item-add">
    <el-form :model="form" ref="addform" :rules="rules" class="qd-form" label-position="right" label-width="120px" inline>
      <el-form-item label="序号" prop="sort" class="form-item-wd100">
        <el-input-number v-model="form.sort" :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="名称" prop="name" class="form-item-wd100">
        <el-input v-model="form.name" placeholder="请输入名称"></el-input>
      </el-form-item>
      <el-form-item label="封面图片" class="form-item-wd100">
        <qd-upload-img v-model="form.files" module="bgImageforVoteItem"></qd-upload-img>
      </el-form-item>
      <el-form-item label="上传附件" class="form-item-wd100">
        <upload v-model="form.files1"></upload>
      </el-form-item>
      <el-form-item label="内容详情" class="form-item-wd100">
        <wang-editor v-model='form.instructions'></wang-editor>
      </el-form-item>
    </el-form>
    <div class="form-footer-btn">
      <el-button type="primary" size="small" @click="onSubmit('addform')">确定</el-button>
    </div>
  </div>

</template>

<script>
import upload from './upload'
export default {
  components: { upload },
  props: {
    editData: Object
  },
  data() {
    return {
      form: {
        sort: '',
        name: '',
        files: [],
        files1: [],
        instructions: ''
      },
      rules: {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入序号', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    if (this.editData) {
      this.form = this.editData
    }
  },
  methods: {
    onSubmit(formRef) {
      this.$refs[formRef].validate(valid => {
        if (valid) {
          this.$emit('callback', this.form)
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.vote-item-add {
  width: 900px;
  padding: 15px;
}
</style>
