<template>
  <div class="materialScienceMy">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="submitUnit"
                   filterable
                   clearable
                   placeholder="请选择提交单位">
          <el-option v-for="item in submitUnitData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="activityItem"
                   filterable
                   clearable
                   placeholder="请选择活动事项">
          <el-option v-for="item in activityItemData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="wordClick">导出word</el-button>
        <el-button type="primary"
                   @click="exportClick">导出Excel</el-button>
        <el-button type="primary"
                   @click="deleteClick"  v-permissions="'auth:materialScienceAll:del'">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                          type="index"
                           width="60"></el-table-column>
          <el-table-column label="活动事项"
                           min-width="120"
                           prop="activityItemName"></el-table-column>
          <el-table-column label="提交单位"
                           min-width="120"
                           prop="submitUnitName"></el-table-column>
          <el-table-column label="材料名称"
                           min-width="220"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.materialName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="形成时间"
                           min-width="120"
                           prop="formationDate"></el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="100">
            <template slot-scope="scope">
              <el-button @click="editor(scope.row)"
                         type="primary"
                         plain
                         v-permissions="'auth:materialScienceAll:edit'"
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :type="1212"
                 :excelId="choose.join(',')"
                 @callback="exportCallback"></zy-export>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import exportWord from '@/mixins/exportWord'
export default {
  name: 'materialScienceMy',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      exportShow: false,
      submitUnit: '',
      activityItem: '',
      submitUnitData: [],
      activityItemData: [],
      type: this.$route.query.type,
      detail: {}
    }
  },
  mixins: [tableData, exportWord],
  components: {
  },
  inject: ['newTab', 'refresh'],
  mounted () {
    this.PublicOpinionList()
    this.dictionaryPubkvs()
  },
  activated () {
    this.PublicOpinionList()
  },
  methods: {
    reset () {
      this.keyword = ''
      this.submitUnit = ''
      this.activityItem = ''
      this.PublicOpinionList()
    },
    /**
     * 字典
     */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'submit_unit_type,activity_item_type'
      })
      var { data } = res
      this.submitUnitData = data.submit_unit_type
      this.activityItemData = data.activity_item_type
    },
    details (row) {
      this.refresh()
      var name = ''
      if (this.type === '1') {
        name = '我的'
      } else {
        name = '所有'
      }
      this.newTab({ name: name + '材料详情', menuId: row.id, to: '/materialScienceDetails', params: { id: row.id, type: this.type, name: name } })
    },
    editor (row) {
      this.refresh()
      var name = ''
      if (this.type === '1') {
        name = '我的'
      } else {
        name = '所有'
      }
      this.newTab({ name: name + '材料编辑', menuId: row.id, to: '/materialScienceAdd', params: { id: row.id, type: this.type } })
    },
    async PublicOpinionList () {
      var datas = {
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        submitUnit: this.submitUnit,
        activityItem: this.activityItem
      }
      var res = ''
      if (this.type === '1') {
        res = await this.$api.appManagement.materialsubmitMyList(datas)
      } else {
        res = await this.$api.appManagement.materialsubmitList(datas)
      }
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.socialinfodels(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async socialinfodels (id) {
      const res = await this.$api.appManagement.materialsubmitDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.PublicOpinionList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    // 导出
    exportClick () {
      if (this.choose.length === 0) {
        return this.$message.warning('请至少选中一条数据进行导出！')
      }
      this.exportShow = true
    },
    exportCallback () {
      this.choose = []
      this.selectObj = []
      this.PublicOpinionList()
      this.exportShow = false
    },
    howManyArticle (val) {
      this.PublicOpinionList()
    },
    whatPage (val) {
      this.PublicOpinionList()
    },
    search () {
      this.page = 1
      this.PublicOpinionList()
    },
    async socialinfolook () {
      var res = await this.$api.appManagement.materialsubmitInfo(this.choose[0])
      var { data } = res
      this.detail = data
    },
    wordClick () {
      if (this.choose.length === 1) {
        this.socialinfolook()
        var name = ''
        if (this.type === '1') {
          name = '我的'
        } else {
          name = '所有'
        }
        console.log(name)
        this.$confirm('此操作将当前选中的数据导出word, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let contentArr = []
          if (this.detail.remarks) {
            contentArr = this.detail.remarks.split('\n')
          }
          var obj = {
            submitUnitName: this.detail.submitUnitName,
            materialName: this.detail.materialName,
            activityItemName: this.detail.activityItemName,
            formationDate: this.$format(this.detail.formationDate, 'YYYY年MM月DD日'),
            titile: name,
            contentArr: contentArr
          }
          this.exportWord('word/materialScience.docx', name + '材料详情导出', obj)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消导出'
          })
        })
      } else {
        this.$message({
          type: 'info',
          message: '请选择一条数据导出word'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.materialScienceMy {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
