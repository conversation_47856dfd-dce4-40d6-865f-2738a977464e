import Vue from 'vue'
import valid from './valid/valid.vue'
import zyTree from './zy-tree/zy-tree.vue'
import zyExport from './zy-export/zy-export.vue'
import zyExportStudy from './zy-exportStudy/zy-exportStudy.vue'
import zyTreeComponents from './zy-tree-components/zy-tree-components.vue'
import UEditor from './UEditor/UEditor.vue'
import zyTable from './zy-table/zy-table.vue'
import zyPopUp from './zy-pop-up/zy-pop-up.vue'
import zySelect from './zy-select/zy-select.vue'
import zyCascader from './zy-cascader/zy-cascader.vue'
import screeningBox from './screening-box/screening-box.vue'
import candidatesUser from './candidates-user/candidates-user.vue'
import zySelectCheckbox from './zy-select-checkbox/zy-select-checkbox.vue'
import zyCascaderCheckbox from './zy-cascader-checkbox/zy-cascader-checkbox.vue'
import zyCalendar from './zy-calendar/zy-calendar.vue'
import zyUpload from './zy-upload/zy-upload'
import signature from './signature/signature'
import zySlidingBox from './zy-sliding-box/zy-sliding-box.vue'
import searchButtonBox from './search-button-box/search-button-box.vue'
import CandidatesBox from './candidates-box/candidates-box.vue'
import xylMenu from './xyl-menu/xyl-menu.vue'
import searchButton from './search-button/search-button'
import XylTemplateImport from './template-import/template-import'
import urbanLinkage from './urban-linkage/urban-linkage'
import XylPopupWindow from './popup-window/popup-window'
import XylSliding from './sliding/sliding'
import XylSlidingItem from './sliding/sliding-item'
import XylLinkage from './linkage/linkage.vue'
import wangEditor from './wang-editor/wang-editor.vue'
import zyUploadFile from './zy-upload-file/zy-upload-file.vue'
import HeaderMultiSelect from './header-multi-select/header-multi-select.vue'
const zyComponents = [
  XylSliding,
  XylSlidingItem,
  XylLinkage,
  XylPopupWindow,
  zySlidingBox,
  xylMenu,
  XylTemplateImport,
  searchButtonBox,
  valid,
  zyCalendar,
  zyExport,
  zyExportStudy,
  zyTreeComponents,
  zyCascader,
  zySelect,
  zySelectCheckbox,
  zyCascaderCheckbox,
  UEditor,
  screeningBox,
  zyTable,
  zyPopUp,
  zyTree,
  zyUpload,
  candidatesUser,
  signature,
  CandidatesBox,
  searchButton,
  urbanLinkage,
  wangEditor,
  zyUploadFile,
  HeaderMultiSelect
]
zyComponents.forEach(item => {
  Vue.component(item.name, item)
})
export default zyComponents
