<template>
  <div class="materialRead">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-date-picker v-model="time"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd HH:mm:ss">
      </el-date-picker>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="handleExport">导出</el-button>
    </div>
    <div class="all">整体情况</div>
    <div class="total">
      共发布调查问卷{{total}}场；{{personTime}}人次参与问卷调查；参与率{{rate}}%
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="50"></el-table-column>
          <el-table-column label="问卷名称"
                           width="350"
                           :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="handleInfo(scope.row)">{{scope.row.name}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="时间"
                           prop="publishDate"
                           min-width="200"></el-table-column>
          <el-table-column label="参与人次"
                           prop="count"
                           min-width="100"></el-table-column>
          <!-- <el-table-column label="合格率" prop="rate" min-width="100"></el-table-column> -->
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  inject: ['newTab'],
  data () {
    return {
      time: [],
      list: [],
      page: 1,
      pageSize: this.$pageSize(),
      total: 0,
      selectionList: [],
      personTime: 0,
      rate: 0
    }
  },
  mounted () {
    this.getList()
    this.getTotal()
  },
  methods: {
    // 获取总的数据
    getTotal () {
      this.$api.BehalfTraining.examinationAll({ module: 'questionnaire' }).then(res => {
        // console.log(res)
        this.rate = res.data.average
        this.personTime = res.data.personTime
      })
    },
    // 获取数据
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        module: 'questionnaire',
        startTime: this.time ? this.time[0] : '',
        endTime: this.time ? this.time[1] : ''
      }
      this.$api.BehalfTraining.examinationList(data).then(res => {
        if (res.errcode === 200) {
          this.list = res.data
          this.total = res.total
        }
      })
    },
    // 搜索
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.time = []
      this.getList()
    },
    // 导出
    handleExport () { },
    // 切换分页
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 切换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 去详情页
    handleInfo (val) {
      this.newTab({ name: '试题详情', menuId: val.id, to: '/zx-questionPurview', params: { id: val.id } })
    }
  }
}
</script>

<style lang="scss" scoped>
.materialRead {
  width: 100%;
  height: 100%;
  .all {
    height: 44px;
    padding-left: 22px;
    line-height: 44px;
    background-color: #ebeff4;
  }
  .total {
    height: 46px;
    padding-left: 22px;
    line-height: 46px;
  }
  .tableData {
    height: calc(100% - 282px);
  }
  .time {
    margin-right: 20px;
  }
}
</style>
