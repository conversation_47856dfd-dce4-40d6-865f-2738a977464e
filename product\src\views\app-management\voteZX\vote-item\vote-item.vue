<template>
  <div class="vote-item">
    <search-box @search-click="search"
                @reset-click="reset"
                title="投票项筛选">
      <zy-widget label="关键字">
        <el-input v-model="searchParams.keyword"
                  placeholder="请输入关键字"
                  clearable
                  @keyup.enter.native="search"></el-input>
      </zy-widget>
    </search-box>
    <div class="qd-list-wrap">
      <div class="qd-btn-box">
        <el-button type="primary"
                   size="small"
                   @click="handleAdd">新增</el-button>
        <el-button type="success"
                   plain
                   size="small"
                   @click="isImport = true">通过模版添加</el-button>
        <el-button type="success"
                   plain
                   size="small"
                   @click="handleDownload">下载模版</el-button>
        <el-button type="danger"
                   size="small"
                   plain
                   @click="handleBatchDelete">删除</el-button>
      </div>
      <div class="qd-table-box">
        <zy-table>
          <el-table :data="list"
                    ref="table"
                    slot="zytable"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection"
                             fixed="left"
                             width="60"></el-table-column>
            <el-table-column label="排序"
                             prop="sort"
                             width="80"></el-table-column>
            <el-table-column label="封面图"
                             min-width="120"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                <el-image style="width: 60px; height: 60px"
                          :src="scope.row.imgPath"
                          :preview-src-list="srcList">
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="标题"
                             prop="name"
                             width="360"
                             show-overflow-tooltip></el-table-column>
            <el-table-column label="操作"
                             prop="format"
                             width="100">
              <template slot-scope="scope">
                <el-button @click="handleEdit(scope.row)"
                           type="text">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
        </zy-table>
      </div>
      <div class="qd-page-box">
        <el-pagination @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page.sync="page"
                       :page-sizes="$pageSizes()"
                       :page-size.sync="pageSize"
                       background
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="total"></el-pagination>
      </div>
    </div>
    <zy-pop-up v-model="isAdd"
               :title="id?'新建投票项':'编辑投票项'">
      <add-vote-item :editData="editData"
                     @callback="handleCallback"></add-vote-item>
    </zy-pop-up>
    <zy-pop-up v-model="isImport"
               title="上传模版">
      <importExcel :id="id"
                   @callback="handleImportCallback"></importExcel>
    </zy-pop-up>
  </div>
</template>

<script>
import _ from 'lodash'
import AddVoteItem from '../add/widget/widget/add-vote-item.vue'
import table from '@/mixins/table.js'
import { filterParams, checkParams } from '@/common/handleParams'
import importExcel from './widget/import-excel'
export default {
  components: { AddVoteItem, importExcel },
  mixins: [table],
  data () {
    return {
      searchParams: { keyword: '' },
      isAdd: false,
      id: null,
      srcList: [],
      editData: null,
      isImport: false
    }
  },
  created () {
    this.id = this.$route.query.id
    this.getList()
  },
  methods: {
    getList () {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        voteId: this.id
      }
      params = Object.assign(params, filterParams(this.searchParams))
      this.$api.appManagement.voteQuestion.list(params).then(res => {
        const { data, total } = res
        this.list = data.map(v => {
          const files = v.attachmentList.filter(v => v.moduleType === 'bgImageforVoteItem')
          v.imgPath = files.length > 0 ? files[0].filePath : ''
          return v
        })
        this.total = total
        this.srcList = (this.list || []).map(v => v.imgPath)
      })
    },
    search () {
      if (!checkParams(this.searchParams)) {
        return this.$message.warning('请输入关键字')
      }
      this.page = 1
      this.getList()
    },
    reset () {
      this.searchParams = {
        keyword: ''
      }
      this.getList()
    },
    handleBatchDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.handleDelete(this.selectionList.map((v) => v.id).join(','))
    },
    handleDelete (ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.voteQuestion.dels(ids).then(res => {
          this.$message.success('删除成功')
          this.getList()
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    handleAdd () {
      this.isAdd = true
      this.editData = null
    },
    handleEdit (v) {
      this.editData = {
        id: v.id,
        sort: v.sort,
        name: v.name,
        instructions: v.instructions,
        files: v.attachmentList.filter(v => v.moduleType === 'bgImageforVoteItem'),
        files1: v.attachmentList.filter(v => v.moduleType === 'voteItemFile').map(item => {
          return {
            name: item.fileName,
            size: item.fileSize,
            type: item.fileType,
            url: item.filePath,
            id: item.id,
            uid: item.uid
          }
        })
      }
      this.isAdd = true
    },
    handleCallback (val) {
      const data = _.cloneDeep(val)
      data.voteId = this.id
      let attachmentIds = ''
      if (val.files.length > 0) {
        attachmentIds = val.files.map(v => v.id).join(',')
      }
      if (val.files1.length > 0) {
        attachmentIds = val.files1.map(v => v.id).join(',')
      }
      if (val.files.length > 0 && val.files1.length > 0) {
        attachmentIds = `${val.files.map(v => v.id).join(',')},${val.files1.map(v => v.id).join(',')}`
      }
      data.attachmentIds = attachmentIds
      delete data.files
      delete data.files1
      if (this.editData) {
        this.$api.appManagement.voteQuestion.edit(data).then(res => {
          this.$message.success('编辑投票项成功')
          this.getList()
          this.isAdd = false
        })
      } else {
        this.$api.appManagement.voteQuestion.add(data).then(res => {
          this.$message.success('新增投票项成功')
          this.getList()
          this.isAdd = false
        })
      }
    },
    handleImportCallback (val) {
      if (val) {
        this.getList()
      }
      this.isImport = false
    },
    handleDownload () {
      this.$api.appManagement.voteQuestion.template()
    }
  }
}
</script>
<style lang="scss">
.vote-item {
  width: 100%;
  height: 100%;
}
</style>
