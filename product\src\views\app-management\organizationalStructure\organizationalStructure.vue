<template>
  <div class="organizational-structure">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="0"
                       :buttonNumber="2">
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
      <template slot="button">
       <!-- <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:add'"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   v-permissions="'auth:zyinfodetail:dels'"
                   @click="deleteClick">删除</el-button> -->
        <el-button type="primary"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   @click="deleteClick">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="information-box">
      <div class="information-tree-box">
        <div class="information-tree-text">选择栏目</div>
        <div class="information-tree">
          <zy-tree :tree="tree"
                   v-model="treeId"
                   :props="{ children: 'children', label: 'name'}"
                   @on-tree-click="choiceClick"></zy-tree>
        </div>
      </div>
      <div class="information-data-box">
        <div class="plenum-data">
          <organizationalStructureIndex v-if="treeId==='1'" @updateSelectNode="updateSelectNode"></organizationalStructureIndex>
          <organizationalStructureContent ref="childList" :id="id" v-else></organizationalStructureContent>
        </div>
      </div>
    </div>

    <zy-pop-up v-model="show"
               :title="id?'编辑':'新增'">
      <committeeDataAdd :id="id"
                        :parentId="treeId"
                        @callback="addCallback"></committeeDataAdd>
    </zy-pop-up>
  </div>
</template>
<script>
import committeeDataAdd from './components/committeeDataAdd'
import organizationalStructureIndex from './organizationalStructureIndex'
import organizationalStructureContent from './organizationalStructureContent'
export default {
  name: 'organizationalStructure',
  data () {
    return {
      keyword: '',
      treeId: '1',
      tree: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false
    }
  },
  inject: ['newTab'],
  components: {
    committeeDataAdd,
    organizationalStructureIndex,
    organizationalStructureContent
  },
  mounted () {
    this.informationColumnTree()
  },
  methods: {
    updateSelectNode(data) {
      this.treeId = data.id
      this.$nextTick(() => {
        this.$refs.childList.informationList(data.id)
      })
    },
    search () {
      this.page = 1
      this.informationColumnTree()
    },
    reset () {
      this.keyword = ''
    },
    newData () {
      this.id = ''
      this.show = true
    },
    addCallback () {
      this.informationColumnTree()
      this.show = false
    },
    async informationColumnTree () {
      const res = await this.$api.appManagement.organizationTree()
      var { data } = res
      var arr = [{ children: [], id: '1', name: '首页' }]
      this.tree = arr.concat(data)
    },
    choiceClick (item) {
      if (this.treeId !== '1') {
        this.id = item.id
        this.$refs.childList.informationList(this.id)
      }
    },
    deleteClick (row) {
      if (this.treeId !== '1') {
        this.$confirm('此操作将删除当前选中的栏目, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.informationListDel(this.treeId).then(res => {
            this.informationColumnTree()
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        if (this.treeId === '1') {
          this.$message({
            message: '首页不支持删除',
            type: 'warning'
          })
        } else {
          this.$message({
            message: '请至少选择一条数据',
            type: 'warning'
          })
        }
      }
    },
    async informationListDel (id) {
      const res = await this.$api.appManagement.organizationDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.informationColumnTree()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.organizational-structure {
  width: 100%;
  height: 100%;

  .information-box {
    height: calc(100% - 64px);
    width: 100%;
    display: flex;

    .information-tree-box {
      width: 222px;
      height: 100%;

      .zy-tree {
        width: 222px;
        min-width: 222px;
      }

      .information-tree-text {
        height: 52px;
        line-height: 52px;
        padding-left: 24px;
        font-size: 16px;
        background-color: #e6e5e8;
      }

      .information-tree {
        height: calc(100% - 52px);
      }
    }

    .information-data-box {
      width: calc(100% - 222px);
      height: 100%;

      .plenum-data {
        height: calc(100% - 52px);
        width: 100%;
      }
    }
  }
}
</style>
