<template>
  <div class="poll-info">
    <p class="title">{{info.name}}</p>
    <p class="describe">{{info.instructions}}</p>
    <div class="poll-list">
      <template v-if="info.type === 1">
        <el-radio-group v-model="answer1">
          <p v-for="op in list" :key="op.id" class="options-item">
            <el-radio :label="op.id">{{op.name}}</el-radio>
            <span v-if="op.attachmentList.length>0" @click="openFile(op.attachmentList)">[附件]</span>
          </p>
        </el-radio-group>
      </template>
      <template v-if="info.type === 2">
        <el-checkbox-group v-model="answer2">
          <p v-for="op in list" :key="op.id" class="options-item">
            <el-checkbox :label="op.id">{{op.name}}</el-checkbox>
            <span v-if="op.attachmentList.length>0" @click="openFile(op.attachmentList)">[附件]</span>
          </p>
        </el-checkbox-group>
      </template>
    </div>
    <zy-pop-up v-model="isInfo" title="附件列表">
      <div class="file">
        <p v-for="file in files" :key="file.id" @click="download(file.url)">{{file.name}}</p>
      </div>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  data() {
    return {
      info: {},
      list: [],
      answer1: '',
      answer2: [],
      files: [],
      isInfo: false
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    // 查看详情
    getInfo() {
      const id = this.$route.query.id
      this.$api.appManagement.pollInfo(id).then(res => {
        this.info = res.data
      })
      this.$api.appManagement.pollOptionsList({ voteId: id, pageNo: 1, pageSize: 1000 }).then(res => {
        this.list = res.data
      })
    },
    openFile(list) {
      this.files = list.map(v => {
        return {
          name: v.fileName,
          size: v.fileSize,
          type: v.fileType,
          url: v.filePath,
          id: v.id,
          uid: v.uid
        }
      })
      this.isInfo = true
    },
    download(val) {
      window.open(val)
    }
  }
}
</script>
<style lang="scss" scoped>
.poll-info {
  padding: 0 80px;
  box-sizing: border-box;
  // height: calc(100% - 54px);
  // overflow-y: scroll;
  .title {
    font-size: 20px;
    text-align: center;
    line-height: 56px;
  }
  .describe {
    color: #8c8c8c;
    line-height: 32px;
    font-size: 16px;
  }
  .poll-list {
    .el-radio-group {
      width: 100%;
    }
    .options-item {
      color: #262626;
      width: 100%;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      .el-radio {
        white-space: normal;
      }
      > span {
        color: $zy-color;
        cursor: pointer;
      }
    }
  }
  .file {
    width: 600px;
    padding: 10px;
    > p {
      line-height: 30px;
      height: 30px;
      cursor: pointer;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
