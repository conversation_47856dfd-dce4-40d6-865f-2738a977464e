<template>
  <div class="presentCurrentBehalf">
    <generalCurrentBehalf :memberType="3"
                          type="present"
                          has="auth:member:"></generalCurrentBehalf>
  </div>
</template>
<script>
import generalCurrentBehalf from './generalCurrentBehalf'
export default {
  name: 'presentCurrentBehalf',
  components: {
    generalCurrentBehalf
  }
}
</script>
<style lang="scss">
.presentCurrentBehalf {
  width: 100%;
  height: 100%;
}
</style>
