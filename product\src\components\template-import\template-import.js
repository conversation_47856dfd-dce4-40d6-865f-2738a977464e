export default {
  name: 'XylTemplateImport',
  props: {
    templateUrl: {
      type: String,
      default: ''
    },
    uploadUrl: {
      type: String,
      default: ''
    },
    fileName: {
      type: String,
      default: 'file'
    },
    params: {
      type: Array,
      default: () => []
    },
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  render () {
    return (<div class="xyl-template-import">
      <div class="xyl-template-import-text">第一步：请先下载模板。 <el-button type="primary" on-click={this.importemplate}>点击下载导入模板</el-button></div>
      <div class="xyl-template-import-text">第二步：请您拿到模板后在自己的电脑上进行数据编辑。</div>
      <div class="xyl-template-import-text">第三步：请将处理好的模板文件上传。</div>
      <el-upload
        class="xyl-template-import-upload"
        drag
        action="/"
        show-file-list={false}
        http-request={this.fileUpload}
        multiple>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip">请不要修改模板的扩展名</div>
      </el-upload>
      {this.errorShow === null && this.isShow ? (<div class="TransitionText"><i class="el-icon-loading"></i> 文件正在上传，请您耐心等待，不要关闭此页面。 </div>) : null}
      {this.errorShow === false ? (<div class="xyl-template-import-success">文件导入完成，如要查看导入结果，请点击下载查看 <el-button type="primary" on-click={this.importResults}>下载导入结果</el-button></div>) : null}
      {this.errorShow === true ? (<div class="xyl-template-import-error">文件导入失败，请检查网络稍后重新导入</div>) : null}
    </div>)
  },
  data () {
    return {
      blob: null,
      errorShow: null,
      isShow: false
    }
  },
  mounted () {
  },
  methods: {
    importemplate () {
      this.$api.general.generalImportemplate(this.templateUrl, this.data)
    },
    fileUpload (files) {
      this.isShow = true
      const param = new FormData()
      this.params.forEach(item => {
        param.append(item.name, item.value)
      })
      param.append(this.fileName, files.file)
      this.$api.general.generalFile(this.uploadUrl, param).then(res => {
        console.log(res)
        const content = res
        this.blob = new Blob([content])
        this.errorShow = false
      }).catch(() => {
        this.errorShow = true
        files.onError()
      })
    },
    importResults () {
      const blob = this.blob
      const fileName = '导入结果.xlsx'
      if ('download' in document.createElement('a')) { // 非IE下载
        const elink = document.createElement('a')
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
      } else { // IE10+下载
        navigator.msSaveBlob(blob, fileName)
      }
    }
  }
}
