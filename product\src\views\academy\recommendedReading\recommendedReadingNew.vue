<template>
  <div class="recommendedReadingNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="标题"
                    prop="mainTitle"
                    class="form-title">
        <el-input placeholder="请输入标题"
                  v-model="form.mainTitle"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="副标题"
                    class="form-title">
        <el-input placeholder="请输入副标题"
                  v-model="form.subhead"
                  clearable>
        </el-input>
      </el-form-item>
      <!-- <el-form-item class="form-input"
                    prop="sort"
                    label="序号">
        <el-input placeholder="请输入序号"
                  type="number"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item> -->

      <el-form-item label="关联书籍"
                    class="form-title">
        <el-button type="primary"
                   @click="BooksClick"
                   class="recommendedReadingNewButton">选择书籍</el-button>
        <div class="recommendedReadingNewItemBox">
          <div class="recommendedReadingNewItem"
               v-for="(item, index) in book"
               :key="index">
            <div class="recommendedReadingNewItemImg">
              <img :src="item.coverImgUrl"
                   alt="">
            </div>
            <div class="recommendedReadingNewItemsBox">
              <div class="recommendedReadingNewItemName">{{item.bookName}}</div>
              <div class="recommendedReadingNewItemIntroduction">{{item.bookDescription}}</div>
              <div class="recommendedReadingNewItemAuthor">
                <div class="recommendedReadingNewItemAuthorText">{{item.authorName}}</div>
                <div class="libraryAllItemButton">
                  <el-button type="text"
                             @click.stop="bookDel(item)">删除</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
    <xyl-popup-window v-model="show"
                      title="选择书籍">
      <RelatedBooks :data="book"
                    @callback="callback"></RelatedBooks>
    </xyl-popup-window>
  </div>
</template>
<script>
import RelatedBooks from '../RelatedBooks/RelatedBooks'
export default {
  name: 'recommendedReadingNew',
  data () {
    return {
      form: {
        mainTitle: '',
        subhead: '',
        sort: ''
      },
      rules: {
        mainTitle: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        // subhead: [
        //   { required: true, message: '请输入副标题', trigger: 'blur' }
        // ],
        sort: [
          { required: true, message: '请输入序号', trigger: 'blur' }
        ]
      },
      show: false,
      book: []
    }
  },
  props: ['id'],
  components: {
    RelatedBooks
  },
  mounted () {
    if (this.id) {
      this.sySuggestedReadingInfo()
    }
  },
  methods: {
    async sySuggestedReadingInfo () {
      const res = await this.$api.academy.sySuggestedReadingInfo({ id: this.id })
      var { data } = res
      this.form.mainTitle = data.mainTitle
      this.form.subhead = data.subhead
      this.form.sort = data.sort
      if (data.books) {
        data.books.forEach(item => {
          item.id = item.bookId
        })
        this.book = data.books
      }
    },
    BooksClick () {
      this.show = true
    },
    callback (data, type) {
      if (data.length) {
        this.book = data
      }
      this.show = false
    },
    bookDel (item) {
      var books = this.book
      this.book = books.filter(book => book.id !== item.id)
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var book = []
          this.book.forEach(item => {
            book.push(item.id)
          })
          let url = '/sySuggestedReading/add'
          if (this.id) {
            url = '/sySuggestedReading/edit'
          }
          this.$api.general.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            mainTitle: this.form.mainTitle,
            subhead: this.form.subhead,
            sort: this.form.sort,
            bookIds: book.join(',')
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.recommendedReadingNew {
  width: 682px;
  .recommendedReadingNewButton {
    height: 36px;
    padding: 0 16px;
  }
  .recommendedReadingNewItemBox {
    display: flex;
    flex-wrap: wrap;
    padding-top: 12px;
    .recommendedReadingNewItem {
      display: flex;
      justify-content: space-between;
      margin-right: 24px;
      margin-bottom: 24px;
      width: 362px;
      height: 128px;
      cursor: pointer;
      .recommendedReadingNewItemImg {
        height: 128px;
        width: 95px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .recommendedReadingNewItemsBox {
        width: 252px;
        height: 100%;
        position: relative;
        .recommendedReadingNewItemName {
          color: #333;
          line-height: 21px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-size: 16px;
          margin-bottom: 7px;
        }
        .recommendedReadingNewItemIntroduction {
          line-height: 24px;
          color: #666;
          letter-spacing: 0.93px;
          height: 72px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          font-size: 13px;
        }
        .recommendedReadingNewItemAuthor {
          position: absolute;
          left: 0;
          bottom: -2px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .recommendedReadingNewItemAuthorText {
            font-size: 13px;
            color: #999;
            letter-spacing: 1px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .libraryAllItemButton {
            .el-button {
              font-size: 13px;
              padding: 0;
              span {
                display: inline-block;
                line-height: 16px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
