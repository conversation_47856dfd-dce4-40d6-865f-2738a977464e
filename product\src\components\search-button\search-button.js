import completely from './completely'
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
const delay = (function () {
  let timer = 0
  return function (callback, ms) {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
export default {
  name: 'XylSearchButton',
  render () {
    const search = this.$slots.search ? (<el-popover width={this.w} transition="el-zoom-in-top" placement="bottom-end" value={this.searchShow} onInput={(value) => { this.searchShow = value }} visible-arrow={false} popper-class="xyl-search-content">
      {this.$slots.search}
      <div class="xyl-search-content-button">
        <el-button type="primary" on-click={this.search}>查询</el-button>
        <el-button on-click={this.reset}>重置</el-button>
      </div>
      {this.$slots.search.length > this.searchNumber ? (<el-button slot="reference">高级搜索</el-button>) : null}
    </el-popover>) : null
    const button = this.endShow ? (<el-popover width="112" value={this.show} onInput={(value) => { this.show = value }} transition="el-zoom-in-top" placement="right-start" visible-arrow={false} popper-class="xyl-button-content">
      <el-button slot="reference" class={['xyl-button-more']}>
        <span class="point"></span><span class="point"></span><span class="point"></span>
      </el-button>
      <div ref="buttonShow" on-click={() => { this.show = false }}></div>
    </el-popover>) : null
    return (<div class="xyl-search-button" ref="searchButton">
      <div class="xyl-button-box" ref="buttonBox">
        <div ref="buttonShowBox" class="buttonShowBox">{this.$slots.button}</div>{button}
      </div>
      {this.$slots.search ? <div class="xyl-search-box">
        {this.$slots.search.slice(0, this.searchNumber)}
        <div class="search-area-button">
          <el-button type="primary" on-click={this.search}>查询</el-button>
          {this.pattern ? search : this.$slots.search.length > this.searchNumber ? (<el-button on-click={this.completely}>高级搜索</el-button>) : null}
          {this.$slots.search.length > this.searchNumber ? null : (<el-button on-click={this.reset}>重置</el-button>)}
        </div>
      </div> : null}
      <completely value={this.isNone}>
        <div class="xyl-search-completely-input" ref="completelyInput">{this.$slots.search.slice(this.searchNumber)}</div>
        <div class={this.className}>
          <el-button type="primary" on-click={this.search}>查询</el-button>
          <el-button on-click={this.reset}>重置</el-button>
        </div>
      </completely>
    </div>)
  },
  computed: {
  },
  data () {
    return {
      show: false,
      isShow: false,
      isNone: false,
      searchShow: false,
      w: 0,
      endShow: true,
      className: 'xyl-search-completely-button'
    }
  },
  props: {
    buttonNumber: {
      type: Number,
      default: 3
    },
    searchNumber: {
      type: Number,
      default: 1
    },
    pattern: {
      type: Boolean,
      default: false
    }
  },
  components: {
    completely
  },
  mounted () {
    const that = this
    erd.listenTo(this.$refs.searchButton, (element) => {
      that.$nextTick(() => {
        if (that.searchShow) {
          that.isShow = true
        }
        that.searchShow = false
        that.w = element.offsetWidth
        if (that.isShow) {
          delay(() => {
            that.searchShow = true
            that.isShow = false
          }, 99)
        }
      })
    })
    this.buttonCalculate()
  },
  updated () {
    if (this.$refs.buttonShowBox) {
      var buttonShowBox = this.$refs.buttonShowBox
      if (buttonShowBox.childNodes.length) {
        this.buttonUpdated()
      }
    }
  },
  methods: {
    search () {
      this.$emit('search')
    },
    reset () {
      this.$emit('reset')
    },
    completely () {
      this.isNone = !this.isNone
      this.$nextTick(() => {
        if (this.$refs.completelyInput) {
          if (this.$refs.completelyInput.offsetHeight > 52) {
            this.className = 'xyl-search-completely-button xyl-search-completely-button-width'
          } else {
            this.className = 'xyl-search-completely-button'
          }
        } else {
          this.className = 'xyl-search-completely-button'
        }
      })
    },
    buttonCalculate () {
      if (this.$refs.buttonShowBox) {
        var buttonShowBox = this.$refs.buttonShowBox
        var buttonBox = this.$refs.buttonBox
        var buttonShow = this.$refs.buttonShow
        var arrStart = []
        var arrEnd = []
        buttonShowBox.childNodes.forEach((item, index) => {
          if (index < this.buttonNumber) {
            arrStart.push(item)
          } else {
            arrEnd.push(item)
          }
        })
        for (let index = arrStart.length - 1; index >= 0; index--) {
          buttonBox.prepend(arrStart[index])
        }
        if (arrEnd.length) {
          arrEnd.forEach(item => {
            buttonShow.appendChild(item)
          })
        } else {
          this.endShow = false
        }
      }
    },
    buttonUpdated () {
      if (this.$refs.buttonShowBox) {
        var buttonShowBox = this.$refs.buttonShowBox
        var buttonBox = this.$refs.buttonBox
        var buttonShow = this.$refs.buttonShow
        var arrStart = []
        var arrEnd = []
        buttonShowBox.childNodes.forEach((item, index) => {
          if (index < this.buttonNumber - buttonBox.childNodes.length) {
            arrStart.push(item)
          } else {
            arrEnd.push(item)
          }
        })
        for (let index = arrStart.length - 1; index >= 0; index--) {
          buttonBox.prepend(arrStart[index])
        }
        if (arrEnd.length) {
          this.endShow = true
          arrEnd.forEach(item => {
            buttonShow.appendChild(item)
          })
        } else {
          this.endShow = false
        }
      }
    }
  },
  beforeDestroy () {
    erd.uninstall(this.$refs.searchButton)
  }
}
