<template>
  <div class="nationalTime">
    <generalTime :memberType="memberType"
                 has="auth:qg:historycircles:"></generalTime>
  </div>
</template>
<script>
import generalTime from './generalTime'
export default {
  name: 'nationalTime',
  components: {
    generalTime
  },
  data () {
    return {
      memberType: this.$route.query.memberType || 4
    }
  }
}
</script>
<style lang="scss">
.nationalTime {
  width: 100%;
  height: 100%;
}
</style>
