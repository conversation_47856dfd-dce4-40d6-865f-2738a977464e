.vote-list {
  height: 100%;
  width: 100%;

  .tableData {
    height: calc(100% - 180px);
    width: 100%;

    .t-color {
      color: #199bc5;
      cursor: pointer;
    }

    .table-icon {
      .el-icon-check {
        font-size: 16px;
        color: #35BE38;
      }

      .el-icon-close {
        font-size: 16px;
        color: #E24C4B;
      }
    }
  }

  .question-info {
    width: 450px;
    padding: 10px 20px;

    >.title {
      line-height: 45px;
    }

    .question-option {
      >p {
        line-height: 36px;
      }
    }

    .question-blank {
      min-height: 45px;
    }
  }

  .check-color {
    color: #35be38;
  }

  .close-color {
    color: red;
  }

  .source-box {
    display: flex;
    padding: 10px 20px;
    width: 600px;
    box-sizing: border-box;
    .el-input-number {
      width: 380px;
      margin-right: 24px;
    }
  }
}

.question {
  border-top: 1px solid #efefef;
  padding: 0 80px;
  box-sizing: border-box;
  height: calc(100% - 54px);
  overflow-y: scroll;

  .title {
    font-size: 20px;
    text-align: center;
    line-height: 56px;
  }

  .describe {
    color: #8C8C8C;
    line-height: 22px;
    font-size: 14px
  }

  .question-item {
    .question-name {
      font-size: 16px;
      line-height: 48px;
    }

    .el-radio-group {
      width: 100%;
    }

    .options-item {
      color: #262626;
      line-height: 36px;
      width: 100%;

      .el-radio {
        white-space: normal;
      }
    }
  }

  .check-color {
    color: #35be38;
  }

  .close-color {
    color: red;
  }

  .more-choice {
    color: #8C8C8C;
  }
}