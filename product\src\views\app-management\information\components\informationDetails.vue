<template>
  <div class="informationDetails details">
    <div class="details-title">详情</div>
    <div class="details-item-box">
      <div class="details-item-title">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.title}}</div>
      </div>
      <!-- <div class="details-item">
        <div class="details-item-label">副标题</div>
        <div class="details-item-value">{{details.secondTitle}}</div>
      </div> -->
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">所属栏目</div>
          <div class="details-item-value">{{details.structureName}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">资讯类型</div>
          <div class="details-item-value">{{details.infoClassName}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">发布人</div>
          <div class="details-item-value">{{details.createBy}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">来源</div>
          <div class="details-item-value">{{details.source}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">显示类型</div>
          <div class="details-item-value"
               v-if="details.infoType=='1'">左侧图</div>
          <div class="details-item-value"
               v-if="details.infoType=='2'">置顶图</div>
          <div class="details-item-value"
               v-if="details.infoType=='3'">大张图</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">发布时间</div>
          <div class="details-item-value">{{details.publishDate}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">外部链接</div>
        <div class="details-item-value">
          <el-link type="primary"
                   :href="details.externalLinks"
                   target="_blank">{{details.externalLinks}}</el-link>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">附件</div>
        <div class="details-item-value">
          <div class="details-item-file"
               v-for="(item, index) in details.attachmentList"
               :key="index"
               @click="fileClick(item)">{{item.fileName}}</div>
        </div>
      </div>
      <div class="details-content"
           v-html="details.content"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'informationDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.informationListInfo()
  },
  methods: {
    async informationListInfo () {
      const res = await this.$api.appManagement.informationListInfo(this.id)
      var { data } = res
      this.details = data
      console.log(this.details.attachList)
    },
    fileClick (row) {
      this.$api.proposal.downloadFile({ id: row.id }, row.fileName)
    }
  }
}
</script>
<style lang="scss">
.informationDetails {
  height: 100%;
  padding: 24px;

  .details-content {
    width: 100%;
    padding: 40px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
