<template>
  <el-form :model="form" class="qd-form vote-add-form" ref="form" label-position="right" label-width="150px" inline :rules="rules">
    <div class="base-form-item">
      <div class="base-form-item-label">基本信息</div>
      <div class="base-form-item-content">
        <el-form-item label="投票主题" prop="theme" class="form-item-wd100">
          <el-input v-model="form.theme" style="width:90%"></el-input>
        </el-form-item>
        <el-form-item label="活动时间" prop="time" class="form-item-wd100">
          <el-date-picker v-model="form.time" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
      </div>
    </div>
    <div class="base-form-item border-top">
      <div class="base-form-item-label">开关设置</div>
      <div class="base-form-item-content">
        <el-form-item label="是否发布" class="form-item-wd100">
          <el-radio-group v-model="form.isPublish">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否所有人" class="form-item-wd100">
          <el-radio-group v-model="form.isAlluser">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="添加用户" class="form-item-wd100 form-item-user" v-if="form.isAlluser === 0">
          <el-button size="small" plain @click="isShow=true"></el-button>
          <div class="user-list" ref="userRef" :style="{paddingRight: (isMore ? 60:0) + 'px'}">
            <el-tag class="tag" v-for="(item,index) in userData" :key="index" @close="deleteMan(index)" closable>
              {{item.name}}
            </el-tag>
            <div class="ellipsis" v-if="isMore">
              <span></span><span></span><span></span>
            </div>
          </div>
          <el-button v-if="userData.length>0" type="primary" @click="isVisible = true">查看全部</el-button>
        </el-form-item>
        <el-form-item label="发布平台" class="form-item-wd100">
          <el-checkbox-group v-model="form.publishSet">
            <el-checkbox :label="1">APP</el-checkbox>
            <el-checkbox :label="2">小程序</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>
    </div>
    <el-form-item label="投票规则" class="form-item-wd100">
      <wang-editor v-model='form.voteRule'></wang-editor>
    </el-form-item>
    <zy-pop-up v-model="isShow" title="添加参与人员">
      <candidates-user point="point_21" :data="userData" @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
    <zy-pop-up v-model="isVisible" title="全部人员">
      <zy-filter-user :userList.sync="userData"></zy-filter-user>
    </zy-pop-up>
  </el-form>

</template>

<script>
export default {
  data() {
    return {
      form: {
        theme: '',
        time: [],
        isPublish: 1,
        isAlluser: 1,
        publishSet: [1],
        voteRule: ''
      },
      rules: {
        theme: [{ required: true, message: '请输入投票主题', trigger: 'blur' }],
        time: [{ required: true, message: '请选择投票时间', trigger: 'change' }]
      },
      isShow: false,
      isMore: false,
      userData: [],
      isVisible: false
    }
  },
  methods: {
    userCallback(data, type) {
      if (type) {
        this.userData = data
      }
      this.isShow = false
      this.$nextTick(() => {
        this.collapse()
      })
    },
    deleteMan(index) {
      this.userData.splice(index, 1)
    },
    collapse() {
      var userRef = this.$refs.userRef
      if (userRef) {
        var width = 0
        for (let index = 0; index < userRef.childNodes.length; index++) {
          if (userRef.childNodes[index].offsetWidth !== undefined) {
            width += userRef.childNodes[index].offsetWidth + 10
          }
        }
        if (userRef.offsetWidth < width) {
          this.isMore = true
        } else {
          this.isMore = false
        }
      }
    },
    handleCallback(arr) {
      this.isVisible = false
      arr.map(v => {
        this.userData.splice(this.userData.findIndex(item => item.userId === v), 1)
      })
    },
    validForm() {
      let result = false
      this.$refs.form.validate((valid) => { result = valid })
      return result
    }
  }
}
</script>

<style lang="scss">
.vote-add-form {
  margin: 25px auto;
  .form-item-user {
    .el-form-item__content {
      width: calc(100% - 150px);
    }
  }
  .user-list {
    height: 54px;
    display: flex;
    align-items: center;
    width: 100%;
    border: 1px solid #ededed;
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
    .tag {
      margin-left: 10px;
    }
  }
  .ellipsis {
    height: 54px;
    width: 36px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    > span {
      width: 4px;
      height: 4px;
      background-color: #007bff;
      border-radius: 50%;
      margin-right: 4px;
    }
  }
}
</style>
