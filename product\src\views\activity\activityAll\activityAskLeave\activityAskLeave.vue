<template>
  <div class="activityAskLeave">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :buttonNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   @click="deleteClick">删除</el-button>
        <el-button type="primary"
                   @click="stateClick(1)">审核通过</el-button>
        <el-button type="primary"
                   @click="stateClick(2)">审核不通过</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="请假人"
                           width="160">
            <template slot-scope="scope">
              <el-button @click="details(scope.row)"
                         type="text">{{scope.row.userNames}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="请假时间"
                           width="160">
            <template slot-scope="scope">{{scope.row.leaveDatetime|datefmt('YYYY-MM-DD')}}</template>
          </el-table-column>
          <el-table-column label="请假理由"
                           min-width="220"
                           prop="reason"></el-table-column>
          <el-table-column label="审核状态"
                           width="120"
                           prop="status">
            <!-- <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.status==='审核通过'"></i>
                <i class="el-icon-close"
                   v-if="scope.row.status==='审核不通过'"></i>
              </div>
            </template> -->
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="190">
            <template slot-scope="scope">
              <el-button @click="trial(scope.row)"
                         type="primary"
                         plain
                         size="mini">审核</el-button>
              <el-button @click="editor(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="show"
                      :title="id==''?'新增请假':'编辑请假'">
      <activityAskLeaveNew :id="id"
                           :meetId="meetId"
                           @newCallback="newCallback"></activityAskLeaveNew>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      title="请假详情">
      <activityAskLeaveDetails :id="id"></activityAskLeaveDetails>
    </xyl-popup-window>
    <xyl-popup-window v-model="auditShow"
                      title="请假审核">
      <askLeaveAudit :id="id"
                     @newCallback="newCallback"></askLeaveAudit>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import activityAskLeaveNew from './activityAskLeaveNew'
import activityAskLeaveDetails from './activityAskLeaveDetails'
import askLeaveAudit from './askLeaveAudit'
export default {
  name: 'activityAskLeave',
  data () {
    return {
      meetId: this.$route.query.meetId, // 活动会议id
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false,
      detailsShow: false,
      auditShow: false
    }
  },
  mixins: [tableData],
  components: {
    activityAskLeaveNew,
    activityAskLeaveDetails,
    askLeaveAudit
  },
  mounted () {
    this.leaveList()
  },
  methods: {
    search () {
      this.page = 1
      this.leaveList()
    },
    reset () {
      this.keyword = ''
      this.leaveList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    trial (row) {
      this.id = row.id
      this.auditShow = true
    },
    editor (row) {
      this.id = row.id
      this.show = true
    },
    newCallback () {
      this.leaveList()
      this.show = false
      this.auditShow = false
    },
    async leaveList () {
      const res = await this.$api.activity.leaveList({
        pageNo: this.page,
        pageSize: this.pageSize,
        meetId: this.meetId,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.leaveList()
    },
    whatPage (val) {
      this.leaveList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的活动请假, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.leaveDels(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async leaveDels (id) {
      const res = await this.$api.activity.leaveDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.leaveList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    stateClick (type) {
      if (this.choose.length) {
        this.editStatus(this.choose.join(','), type)
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async editStatus (id, type) {
      const res = await this.$api.activity.editStatus({
        ids: id,
        status: type
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.leaveList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.activityAskLeave {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
