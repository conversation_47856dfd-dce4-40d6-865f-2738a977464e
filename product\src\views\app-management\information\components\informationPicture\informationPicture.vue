<template>
  <div class="informationPicture">
    <div class="button-box-list">
      <el-button type="primary"
                 v-permissions="'auth:zyinforeportpic:scrolling:pic:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 v-permissions="'auth:zyinforeportpic:dels'"
                 @click="deleteClick">删除</el-button>
    </div>
    <div class="tableData tableSmall">
      <zy-table>
        <el-table slot="zytable"
                  ref="multipleTable"
                  :data="tableData"
                  style="width: 100%"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           width="48">
          </el-table-column>
          <el-table-column label="序号"
                           width="80"
                           prop="sort">
          </el-table-column>
          <el-table-column label="标题"
                           prop="title"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="创建人"
                           prop="createBy"
                           width="120"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="创建时间"
                           width="160"
                           prop="createDate"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="操作"
                           v-if="$hasPermission(['auth:zyinforeportpic:scrolling:pic:edit'])"
                           width="80">
            <template slot-scope="scope">
              <el-button @click="handleClick(scope.row)"
                         type="text"
                         size="small">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>

    <xyl-popup-window v-model="show"
                      :title="mosaicId==''?'新增滚动图片':'编辑滚动图片'">
      <informationPictureNew :id="id"
                             :mosaicId="mosaicId"
                             @newCallback="newCallback"></informationPictureNew>
    </xyl-popup-window>
  </div>
</template>
<script>
import informationPictureNew from './informationPictureNew'
export default {
  name: 'informationPicture',
  data () {
    return {
      tableData: [],
      multipleSelection: [],
      mosaicId: '',
      show: false
    }
  },
  props: ['id'],
  components: {
    informationPictureNew
  },
  mounted () {
    if (this.id) {
      this.pictureList()
    }
  },
  methods: {
    async pictureList () {
      const res = await this.$api.appManagement.pictureList({ detailId: this.id, pageNo: 1, pageSize: 1000 })
      var { data } = res
      this.tableData = data
    },
    handleClick (row) {
      this.mosaicId = row.id
      this.show = true
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
    },
    newData () {
      this.mosaicId = ''
      this.show = true
    },
    newCallback () {
      this.pictureList()
      this.show = false
    },
    deleteClick () {
      if (this.multipleSelection.length) {
        this.$confirm('此操作将删除当前选中的滚动图片, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var arr = []
          this.multipleSelection.forEach(item => {
            arr.push(item.id)
          })
          var idSets = arr.join(',')
          this.picDel(idSets)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async picDel (id) {
      const res = await this.$api.appManagement.picDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.pictureList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.informationPicture {
  width: 988px;
  height: calc(85vh - 52px);
  padding: 16px 24px;

  .button-box-list {
    height: 36px;
    margin-bottom: 12px;

    .el-button {
      width: 64px;
      max-width: 64px;
      min-width: 64px;
      padding: 0;
      height: 36px;
      font-size: 12px;
    }
  }

  .tableData {
    height: calc(100% - 48px);
  }
}
</style>
