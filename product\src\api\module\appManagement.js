// 导入封装的方法
import { post, get, postform, exportFile } from '../http'
const appManagement = {
  informationList (params) {
    return post('/zyinfodetail/list?', params)
  },
  informationListInfo (params) {
    return post(`/zyinfodetail/info/${params}`)
  },
  getstructure (params) {
    return post('/valley/getstructure', params)
  },
  pushInfo (params) {
    return post('/valley/pushInfo', params)
  },
  officeList (params) {
    return post('/infostructurerelation/list?', params)
  },
  infostructurerelation (params) {
    return post('/infostructurerelation/dels', params)
  },
  informationListDel (params) {
    return post('/zyinfodetail/dels', params)
  },
  editInfo (params) {
    return post('/valley/editInfo', params)
  },
  informationBatchUpdate (params) {
    return post('/zyinfodetail/batchUpdate', params)
  },
  picList (params) {
    return post('/zyinforeportpic/pic/list?', params)
  },
  picInfo (params) {
    return post(`/zyinforeportpic/pic/info/${params}`)
  },
  picDel (params) {
    return post('/zyinforeportpic/dels', params)
  },
  reportList (params) {
    return post('/zyinforeportpic/scrolling/report/list?', params)
  },
  reportInfo (params) {
    return post(`/zyinforeportpic/scrolling/report/${params}`)
  },
  pictureList (params) {
    return post('/zyinforeportpic/scrolling/pic/list?', params)
  },
  pictureInfo (params) {
    return post(`/zyinforeportpic/scrolling/pic/${params}`)
  },
  associatedList (params) {
    return post('/inforelation/list?', params)
  },
  associatedAddList (params) {
    return post('/inforelation/addList?', params)
  },
  associatedAdd (params) {
    return post('/inforelation/add', params)
  },
  associatedDel (params) {
    return post('/inforelation/dels', params)
  },
  informationColumn (params) {
    return post('/zyinfostructure/list?', params)
  },
  informationColumnTree (params) {
    return post('/zyinfostructure/tree', params)
  },
  informationColumnInfo (params) {
    return post(`/zyinfostructure/info/${params}`)
  },
  informationColumnDel (params) {
    return post('/zyinfostructure/dels', params)
  },
  customTopicList (params) {
    return post('/specialsubjectinfo/list?', params)
  },
  customTopicListInfo (params) {
    return post(`/specialsubjectinfo/info/${params}`)
  },
  customTopicListDel (params) {
    return post('/specialsubjectinfo/dels', params)
  },
  customTopicColumnList (params) {
    return post('/zySpecialsubjectColumn/list', params)
  },
  customTopicColumnTree (params) {
    return post('/zySpecialsubjectColumn/treeDataBySubject', params)
  },
  customTopicColumnInfo (params) {
    return post('/zySpecialsubjectColumn/form', params)
  },
  customTopicColumnDel (params) {
    return post('/zySpecialsubjectColumn/delete', params)
  },
  customTopicinformation (params) {
    return post('/specialsubjectnews/list?', params)
  },
  customTopicinformationInfo (params) {
    return post(`/specialsubjectnews/info/${params}`)
  },
  customTopicinformationDel (params) {
    return post('/specialsubjectnews/dels', params)
  },
  projectAssociatedList (params) {
    return post('/zySpecialsubjectRelateinfo/list', params)
  },
  projectAssociatedrecord (params) {
    return post('/zySpecialsubjectRelateinfo/subjectNews', params)
  },
  projectAssociatednew (params) {
    return post('/zySpecialsubjectRelateinfo/add', params)
  },
  projectAssociatedDel (params) {
    return post('/zySpecialsubjectRelateinfo/delete', params)
  },
  // postname (params) {
  //   return post('http://192.168.1.109:80/fillgeneral/saveFillGeneral', params)
  // },
  /**
     * 群成员列表
     */
  voteList (params) {
    return post('/paper/list?', params)
  },
  // 获取考试题目列表
  votequestionList (params) {
    return post('/paperquestionrelation/list', params)
  },
  // 获取题库题目列表
  questionList (params) {
    return post('/paperquestion/list', params)
  },
  // 新增题目
  questionAdd (params) {
    return post('/paperquestion/add', params)
  },
  // 修改题目
  questionEdit (params) {
    return post('/paperquestion/edit', params)
  },
  // 题目设分值
  questionSource (data) {
    return post('/paperquestionrelation/updateValue', data)
  },
  // 题目详情
  questionInfo (id) {
    return post(`/paperquestion/info/${id}`)
  },
  // 删除题目
  questionDelete (ids, paperId) {
    const data = { ids: ids }
    if (paperId) {
      data.paperId = paperId
    }
    return post('/paperquestion/dels', data)
  },
  questionImport () {
    return exportFile('/paperquestion/importemplate')
  },
  zxQuestionImport () {
    return exportFile('/paperquestion/importemplate?')
  },
  // 考试题目的删除
  voteQuestionDelete (ids, paperId) {
    const data = { ids: ids }
    if (paperId) {
      data.paperId = paperId
    }
    return post('/paperquestionrelation/dels', data)
  },
  // 新增投票
  voteAdd (params) {
    return post('/paper/add', params)
  },
  // 投票详情
  voteInfo (id) {
    return post(`/paper/info/${id}`)
  },
  // 删除投票
  voteDelete (ids) {
    return post('/paper/dels', { ids: ids })
  },
  // 参与人数列表
  joinVoteUserList (data) {
    return post('/paper/getPaperUser', data)
  },
  // 投票导入题目
  voteImportQuestion (params) {
    return post('/paperquestion/addModelQues', params)
  },
  // 题目排序
  voteQuestionSort (params) {
    return post('/paperquestion/editSort', params)
  },
  // 投票统计
  voteCount (params) {
    return post('/paperquestion/getQuestionStatistics', params)
  },
  // 投票结果
  // voteAnswer (params) {
  //   return post(`/paperanswer/list`, params)
  // },
  // 投票答案列表
  voteAnswerList (params) {
    return post('/paperanswer/getPaperAnswers', params)
  },
  // 投票选项人数
  voteOptionManNum (params) {
    return post('/paperanswer/list', params)
  },
  // 获取用户答题结果
  voteUserAnswer (params) {
    return post('/paperanswer/getUserAnswers', params)
  },
  // 题库导入excel
  questionExport (data) {
    return postform('/paperquestion/import', data)
  },
  /* 政协投票模块 */
  // 投票列表
  pollList (data) {
    return post('/vote/list', data)
  },
  // 投票详情
  pollInfo (id) {
    return post(`/vote/info/${id}`)
  },
  // 投票新增
  pollAdd (data) {
    return post('/vote/add', data)
  },
  // 投票编辑
  pollEdit (data) {
    return post('/vote/edit', data)
  },
  // 投票发布与取消发布
  pollRelease (params) {
    return post('/vote/editIsRelease', params)
  },
  // 投票删除
  pollDels (ids) {
    return post('/vote/dels', { ids: ids })
  },
  /* 投票选项 */
  pollOptionsList (params) {
    return post('/voteoptions/list', params)
  },
  pollOptionsAdd (params) {
    return post('/voteoptions/add', params)
  },
  pollOptionsEdit (params) {
    return post('/voteoptions/edit', params)
  },
  pollOptionsDels (ids) {
    return post('/voteoptions/dels', { ids: ids })
  },
  pollOptionsInfo (id) {
    return post(`/voteoptions/info/${id}`)
  },
  pollOptionsCount (id) {
    return post('/vote/getStatistical', { id: id })
  },
  pollOptionsCountJoinMan (id) {
    return post('/vote/getStatisticalDetail', { id: id })
  },
  // 获取和app扫码配置值
  getAppconfigCode () {
    return get('/readonfig?codes=rongCloudIdPrefix')
  },
  yearsummarygrouplist (data) { // 分组列表
    return post('/yearsummarygroup/list?', data)
  },
  yearsummarygroup (url, data) { // 添加 修改
    return post(url, data)
  },
  yearsummarygroupdels (data) { // 删除分组
    return post('/yearsummarygroup/dels', data)
  },
  yearsummarygroupinfo (data) { // 详情
    return post(`yearsummarygroup/info/${data}`)
  },
  yearsummarylist (data) { // 模块列表
    return post('/yearsummary/list?', data)
  },
  yearsummary (url, data) { // 添加 修改
    return post(url, data)
  },
  yearsummarydels (data) { // 删除模块
    return post('/yearsummary/dels', data)
  },
  yearsummaryinfo (data) { // 详情
    return post(`/yearsummary/info/${data}`)
  },
  yearsummaryselects (data) { // 详情
    return post('/yearsummary/selects')
  },
  yearsummarygenerate (data) { // 生成年度报告
    return post('/yearsummary/generate', data)
  },
  // 上传附件
  uploadFile (data) {
    return postform('/attachment/uploadFile', data, { timeout: 80000 })
  },
  // 考试发布
  publicPaper (data) {
    return post('/paper/publicPaper', data)
  },
  // 考试取消发布
  rePublicPaper (data) {
    return post('/paper/rePublicPaper', data)
  },

  voteZXList (params) {
    return post('/qdvote/list', params)
  },
  // add (params) {
  //   return post('/qdvote/add', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  // },
  // edit (params) {
  //   return post('/qdvote/edit', params, { 'Content-Type': 'application/json;charset=UTF-8' })
  // },
  voteZXInfo (id) {
    return get(`/qdvote/info/${id}`)
  },
  voteZXDel (ids) {
    return post('/qdvote/dels', { ids })
  },
  voteZXPublish (params) {
    return post('/qdvote/updIsPublish', params)
  },
  voteZXPreviewCode (id) {
    return post('/qdvote/getQrVote', { id })
  },
  jobcontentList (params) { // 工作内容列表
    return post('/jobcontent/list', params)
  },
  jobcontentInfo (id) { // 工作内容详情
    return get(`/jobcontent/info/${id}`)
  },
  jobcontentAdd (params) { // 工作内容新增
    return post('/jobcontent/add', params)
  },
  jobcontentEdit (params) { // 工作内容编辑
    return post('/jobcontent/edit', params)
  },
  jobcontentDels(params) { // 工作内容批量删除
    return post('jobcontent/dels', params)
  },
  jobcontentGetWorkTask (params) { // 工作任务列表
    return post('/jobcontent/getWorkTask', params)
  },
  tasklistList (params) { // 任务清单列表
    return post('/tasklist/list', params)
  },
  tasklistAdd (params) { // 新增任务清单列表
    return post('/tasklist/add', params)
  },
  tasklistEdit (params) { // 编辑任务清单列表
    return post('/tasklist/edit', params)
  },
  tasklistDel(id) { // 任务清单删除
    return post(`/tasklist/del/${id}`)
  },
  tasklistGetSmsTemplateList (id) { // 短信模板列表
    return get(`/tasklist/getSmsTemplateList/${id}`)
  },
  organizationList (params) { // 组织架构列表
    return post('/organization/list', params)
  },
  organizationTree (params) { // 组织架构列表
    return post('/organization/tree', params)
  },
  organizationInfo (id) { // 组织架构详情
    return get(`/organization/info/${id}`)
  },
  organizationAdd (url, params) { // 新增，编辑组织架构
    return post(url, params)
  },
  organizationDels (params) { // 删除组织架构
    return post('organization/dels', params)
  },
  organizationcontentList (params) { // 组织架构内容列表
    return post('/organizationcontent/list', params)
  },
  organizationcontentInfo (id) { // 组织架构内容详情
    return get(`/organizationcontent/info/${id}`)
  },
  organizationcontentAdd (url, params) { // 内容
    return post(url, params)
  },
  organizationcontentDel (id) { // 删除组织架构内容
    return post(`/organizationcontent/del/${id}`)
  },
  incumbentinfoList (params) { // 在职人员列表
    return post('/incumbentinfo/list', params)
  },
  incumbentinfoInfo (id) { // 在职人员详情
    return get(`/incumbentinfo/info/${id}`)
  },
  incumbentinfoDels (params) { // 删除在职人员信息
    return post('incumbentinfo/dels', params)
  },
  retireeinfoList (params) { // 退休人员列表
    return post('/retireeinfo/list', params)
  },
  retireeinfoInfo (id) { // 退休人员详情
    return get(`/retireeinfo/info/${id}`)
  },
  retireeinfoDels (params) { // 删除退休人员信息
    return post('retireeinfo/dels', params)
  },
  materialsubmitMyList (params) { // 我的材料提交
    return post('/materialsubmit/MyList', params)
  },
  materialsubmitList (params) { // 所有材料提交
    return post('/materialsubmit/list', params)
  },
  materialsubmitDels (params) { // 删除材料提交
    return post('materialsubmit/dels', params)
  },
  materialsubmitInfo (id) { // 材料提交详情
    return get(`/materialsubmit/info/${id}`)
  }
}
export default appManagement
