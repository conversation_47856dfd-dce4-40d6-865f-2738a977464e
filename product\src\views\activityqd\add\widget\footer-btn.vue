<template>
  <div class="add-footer-btn">
    <el-button type="primary"
               @click="handleSubmit"
               icon="el-icon-s-promotion">提交
    </el-button>
    <el-button @click="handleStep('prev')"
               v-show="active > 1"
               icon="el-icon-arrow-left">上一步</el-button>
    <el-button @click="handleStep('next')"
               icon="el-icon-arrow-right"
               v-show="active < 3">下一步</el-button>
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Number,
      default: 1
    }
  },
  methods: {
    handleStep (type) {
      this.$emit('update:active', type === 'next' ? this.active + 1 : this.active - 1)
    },
    handleSubmit () {
      this.$emit('submit')
    }
  }
}
</script>

<style lang="scss" >
.add-footer-btn {
  z-index: 1000;
  padding-left: 40px;
  .el-button {
    margin-right: 10px;
    margin-left: 0px;
  }
}
</style>
