<template>
  <div class="projectAssociated">
    <div class="button-box-list">
      <el-button type="primary"
                 v-permissions="'auth:zySpecialsubjectRelateinfo:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 v-permissions="'auth:zySpecialsubjectRelateinfo:delete'"
                 @click="deleteClick">删除</el-button>
      <el-input placeholder="请输入内容"
                clearable
                v-model="name"
                @keyup.enter.native="search">
        <div slot="prefix"
             class="input-search"></div>
      </el-input>
    </div>
    <div class="projectAssociatedBox">
      <div class="projectAssociatedTree scrollBar">
        <el-tree :data="tree"
                 :props="defaultProps"
                 :expand-on-click-node="false"
                 @node-click="handleNodeClick"></el-tree>
      </div>
      <div class="projectAssociatedTable">
        <div class="tableData tableSmall">
          <zy-table>
            <el-table :data="tableData"
                      ref="table"
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               width="48">
              </el-table-column>
              <el-table-column label="序号"
                               prop="sort"
                               width="60">
              </el-table-column>
              <!-- <el-table-column label="标题"
                             show-overflow-tooltip>
              <template slot-scope="scope">
                <el-button @click="details(scope.row)"
                           type="text"
                           size="small">{{scope.row.title}}</el-button>
              </template>
            </el-table-column> -->
              <el-table-column label="标题"
                               prop="title"
                               show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="发布时间"
                               width="180"
                               prop="createDate"
                               show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="操作"
                               v-if="$hasPermission(['auth:zySpecialsubjectRelateinfo:updateSort'])"
                               width="80">
                <template slot-scope="scope">
                  <el-button @click="handleClick(scope.row)"
                             type="text"
                             size="small">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="$pageSizes()"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
    <xyl-popup-window v-model="show"
                      title="专题关联新增">
      <projectAssociated-new :id="id"
                             :columnId="treeId"
                             @newCallback="newCallback"></projectAssociated-new>
    </xyl-popup-window>
    <xyl-popup-window v-model="addShow"
                      title="专题关联编辑">
      <projectAssociated-add :id="projectId"
                             @newCallback="addCallback"></projectAssociated-add>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import projectAssociatedNew from './projectAssociatedNew'
import projectAssociatedAdd from './projectAssociatedAdd'
export default {
  name: 'projectAssociated',
  data () {
    return {
      name: '',
      treeId: '',
      tree: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      show: false,
      projectId: '',
      addShow: false
    }
  },
  mixins: [tableData],
  props: ['id'],
  components: {
    projectAssociatedNew,
    projectAssociatedAdd
  },
  mounted () {
    this.customTopicColumnTree()
  },
  methods: {
    async customTopicColumnTree () {
      const res = await this.$api.appManagement.customTopicColumnTree({ subjectId: this.id })
      var { data } = res
      this.tree = data
    },
    handleNodeClick (data) {
      this.treeId = data.id
      this.projectAssociatedList()
    },
    async projectAssociatedList () {
      const res = await this.$api.appManagement.projectAssociatedList({
        subjectId: this.id,
        columnId: this.treeId,
        pageNo: this.page,
        pageSize: this.pageSize,
        title: this.name
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    search () {
      this.page = 1
      this.projectAssociatedList()
    },
    newData () {
      if (this.treeId) {
        this.show = true
      } else {
        this.$message({
          message: '请先选择栏目再新增',
          type: 'warning'
        })
      }
    },
    newCallback () {
      this.projectAssociatedList()
      this.show = false
    },
    handleClick (row) {
      this.projectId = row.id
      this.addShow = true
    },
    addCallback () {
      this.projectAssociatedList()
      this.addShow = false
    },
    howManyArticle (val) {
      this.projectAssociatedList()
    },
    whatPage (val) {
      this.projectAssociatedList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的专题资讯, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.projectAssociatedDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async projectAssociatedDel (id) {
      const res = await this.$api.appManagement.projectAssociatedDel({
        id: this.treeId,
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.projectAssociatedList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.projectAssociated {
  width: 1120px;
  height: calc(85vh - 52px);
  padding: 16px 24px;
  padding-bottom: 0;

  .button-box-list {
    height: 36px;
    margin-bottom: 12px;

    .el-button {
      width: 64px;
      max-width: 64px;
      min-width: 64px;
      padding: 0;
      height: 36px;
      font-size: 12px;
    }

    .el-input {
      width: 268px;
      float: right;

      .el-input__inner {
        height: 36px;
        line-height: 36px;
        padding-left: 31px;
        font-size: 12px;
        border-radius: 2px;
      }

      .el-input__prefix {
        width: 31px;
        height: 100%;
        display: flex;
        align-items: center;
        left: 0;
        padding-left: 9px;
        box-sizing: border-box;

        .input-search {
          width: 14px;
          height: 14px;
          background: url("../../../../../assets/img/input-search.png");
          background-size: 100% 100%;
        }
      }
    }
  }

  .projectAssociatedBox {
    display: flex;
    height: calc(100% - 52px);

    .projectAssociatedTree {
      width: 222px;
      height: 100%;
      border: 1px solid #d9d9d9;

      .el-tree {
        display: inline-block !important;
        min-width: 100%;
        padding-bottom: 17px;

        .el-tree-node__content {
          height: 40px;
          line-height: 40px;

          &:hover {
            background-color: $zy-withColor;
          }
        }

        .is-current > .el-tree-node__content {
          background-color: $zy-withColor;
          color: $zy-color;
          font-weight: 600;
        }
      }
    }

    .projectAssociatedTable {
      width: calc(100% - 222px);
      height: 100%;

      .tableData {
        height: calc(100% - 52px) !important;
      }
    }
  }
}
</style>
