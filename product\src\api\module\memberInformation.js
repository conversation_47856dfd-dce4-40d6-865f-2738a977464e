import {
  get,
  post,
  postform,
  exportFile,
  fileRequest
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const memberInformation = {
  memberList (params) {
    return post('/member/list?', params)
  },
  memberDel (params) {
    return post('/member/batch/del', params)
  },
  memberStartuse (params) {
    return post('/member/batch/startuse', params)
  },
  memberStopuse (params) {
    return post('/member/batch/stopuse', params)
  },
  memberResetpwd (params) {
    return post('/member/batch/resetpwd', params)
  },
  memberInfo (params) {
    return post(`/member/info/${params}`)
  },
  memberDetails (params) {
    return post('/member/look?', params)
  },
  historymemberList (params) {
    return post('/historymember/list?', params)
  },
  historymemberSelect (params) {
    return post('/historycircles/select?', params)
  },
  historymemberInfo (params) {
    return post(`/historymember/look/${params}`)
  },
  territory (params) {
    return post('/territory', params)
  },
  historycirclesList (params) {
    return post('/historycircles/list?', params)
  },
  historycirclesInfo (params) {
    return post(`/historycircles/info/${params}`)
  },
  historycirclesDel (params) {
    return post('/historycircles/dels?', params)
  },
  memberCount (params) {
    return post('/member/count?', params)
  },
  // 换届
  changejc (params) {
    return post('/member/changejc', params)
  },
  // 出缺
  batchOut (params) {
    return post('/member/batch/out', params)
  },
  // 取消出缺
  nobatchOut (params) {
    return post('/member/batch/nout', params)
  },
  // 委员信息锁定日志
  lockLogList (params) {
    return post('/memberlocklog/list', params)
  },
  // 锁定委员列表
  lockMemberTable (params) {
    return post('/memberlock/lockMemberTable', params)
  },
  // 查询锁定状态
  lockMemberState (params) {
    return post('/memberlock/lockMemberState', params)
  },
  // 下载模板
  importemplate (params) {
    exportFile('/member/importemplate', params)
  },
  // 导入代表或全国代表
  import (params, text) {
    fileRequest('/member/import', params, text)
  },
  // 代表信息审核
  userauditList (params) {
    return post('/useraudit/list?', params)
  },
  // 代表信息审核详情
  userauditInfo (params) {
    return post('/useraudit/info?', params)
  },
  // 代表信息审核删除
  userauditDels (params) {
    return post('/useraudit/dels?', params)
  },
  // 同步代表
  same (params) {
    return post('/member/same', params)
  },
  // 查询用户id
  existsusers (params) {
    return post('/wholeuser/existsusers?', params)
  },
  // 换届预览
  changePreview (params) {
    return postform('/member/changePreview?', params)
  },
  // 一键换届
  changeJc (params) {
    return postform('/member/changeJc?', params)
  },
  // 删除往届
  historymemberDel (params) {
    return post('/historymember/dels?', params)
  },
  // 导出代表名单
  exportnames (params) {
    exportFile('/member/exportnames?', params)
  },
  // 导出代表名册
  exportnamelist (params) {
    exportFile('/member/exportnamelist?', params)
  },
  // 导出代表照片”/“导出委员照片
  downloadHeadImg (params) {
    exportFile('/member/downloadHeadImg?', params)
  }
}
export default memberInformation
