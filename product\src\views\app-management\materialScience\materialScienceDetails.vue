<template>
  <div class="materialScienceDetails">
    <el-scrollbar class="materialScienceDetailsBox">
      <div class="details">
        <div class="details-title">{{name}}材料
          <div class="wordDownload"
               @click="wordClick"></div>
        </div>
        <div class="details-item-box">
          <div class="details-item-title">
            <div class="details-item-label">提交单位</div>
            <div class="details-item-value">
              <div class="PublicOpinionTitleBox">
                <div class="PublicOpinionTitle">{{details.submitUnitName}}</div>
              </div>
            </div>
          </div>
          <div class="details-item-title">
            <div class="details-item-label">材料名称</div>
            <div class="details-item-value">
              <div class="PublicOpinionTitleBox">
                <div class="PublicOpinionTitle">{{details.materialName}}</div>
              </div>
            </div>
          </div>
          <div class="details-item-title">
            <div class="details-item-label">活动事项</div>
            <div class="details-item-value">
              <div class="PublicOpinionTitleBox">
                <div class="PublicOpinionTitle">{{details.activityItemName}}</div>
              </div>
            </div>
          </div>
          <div class="details-item-title">
            <div class="details-item-label">形成时间</div>
            <div class="details-item-value">
              <div class="PublicOpinionTitleBox">
                <div class="PublicOpinionTitle">{{details.formationDate}}</div>
              </div>
            </div>
          </div>
          <div class="details-item">
            <div class="details-item-label">材料</div>
            <div class="details-item-value">
              <div class="details-item-file"
                  v-for="(item, index) in details.attachmentList"
                  :key="index"
                  @click="fileClick(item)">{{item.fileName}}</div>
            </div>
          </div>
          <div class="details-item-title">
            <div class="details-item-label">备注</div>
            <div class="details-item-value">
              <div class="PublicOpinionTitleBox">
                <div class="PublicOpinionTitle" v-html="details.remarks" style="white-space:pre-line"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script>
import exportWord from '@/mixins/exportWord'
export default {
  name: 'materialScienceDetails',
  data () {
    return {
      id: this.$route.query.id,
      name: this.$route.query.name,
      details: {}
    }
  },
  mounted () {
    if (this.id) {
      this.socialinfolook()
    }
  },
  activated () {
    if (this.id) {
      this.socialinfolook()
    }
  },
  mixins: [exportWord],
  methods: {
    async socialinfolook () {
      var res = await this.$api.appManagement.materialsubmitInfo(this.id)
      var { data } = res
      this.details = data
    },
    fileClick (row) {
      this.$api.proposal.downloadFile({ id: row.id }, row.fileName)
    },
    wordClick () {
      let contentArr = []
      if (this.details.remarks) {
        contentArr = this.details.remarks.split('\n')
      }
      var obj = {
        submitUnitName: this.details.submitUnitName,
        materialName: this.details.materialName,
        activityItemName: this.details.activityItemName,
        formationDate: this.$format(this.details.formationDate, 'YYYY年MM月DD日'),
        titile: this.name,
        contentArr: contentArr
      }
      this.exportWord('word/materialScience.docx', this.name + '材料详情导出', obj)
    }
  }
}
</script>
<style lang="scss">
.materialScienceDetails {
  width: 100%;
  height: 100%;
  .details-title {
    position: relative;
    .wordDownload {
      position: absolute;
      top: 50%;
      right: 24px;
      transform: translateY(-50%);
      width: 32px;
      height: 32px;
      background: url("../../../assets/images/WORD.png");
      background-size: 100% 100%;
      cursor: pointer;
    }
  }
  .form-upload-demo {
    padding: 12px;
    .el-upload {
      width: 100%;

      .el-upload-dragger {
        height: 79px;
        width: 100%;
        max-width: 952px;
        background-color: #e6e5e8;

        .el-upload__text {
          padding-top: 22px;
          font-size: 14px;
          line-height: 22px;
        }

        .el-upload__tip {
          font-size: 12px;
          line-height: 20px;
          color: #6e6e6e;
          margin-top: 0;
        }
      }
    }
  }
  .details-item-box .details-item-column + .details-item-column{
      border-left: none;
  }
}
.materialScienceDetailsBox {
  width: 100%;
  height: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .is-vertical {
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.8);
    }
  }
}
.details {
  margin: auto;
  padding-bottom: 24px;
}
</style>
