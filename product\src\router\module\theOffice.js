/*
 *  机关办公
*/

// 周管理
const weekManagement = () => import('@/views/theOffice/arrangeWeek/weekManagement/weekManagement')
// 年管理
const yearManagement = () => import('@/views/theOffice/arrangeWeek/yearManagement/yearManagement')
// 一周安排新增、编辑
const yearManagementAdd = () => import('@/views/theOffice/arrangeWeek/yearManagement/yearManagementAdd')
// 一周安排详情
const weekArrangeDetail = () => import('@/views/theOffice/arrangeWeek/yearManagement/weekArrangeDetail')
// 值班管理
const onDutyArrangement = () => import('@/views/theOffice/onDutyArrangement/onDutyArrangement')
// 值班管理详情
const dutyArrangementDetail = () => import('@/views/theOffice/onDutyArrangement/detail/dutyArrangementDetail')

// 月列表-值班管理
const arrangeMonthList = () => import('@/views/theOffice/onDutyArrangement/arrangeMonthList')
// 日志管理
const logbook = () => import('@/views/theOffice/logbook/logbook')
// 日志管理详情
const logbookDetail = () => import('@/views/theOffice/logbook/detail/logbookDetail')
// 月列表-日志管理
const logMonthList = () => import('@/views/theOffice/logbook/logMonthList')

const theOffice = [
  { // 周管理
    path: '/weekManagement',
    name: 'weekManagement',
    component: weekManagement
  },
  { // 年管理
    path: '/yearManagement',
    name: 'yearManagement',
    component: yearManagement
  },
  { // 一周安排新增、编辑
    path: '/yearManagementAdd',
    name: 'yearManagementAdd',
    component: yearManagementAdd
  },
  { // weekArrangeDetail 一周安排详情
    path: '/weekArrangeDetail',
    name: 'weekArrangeDetail',
    component: weekArrangeDetail
  },
  { // 值班安排管理
    path: '/onDutyArrangement',
    name: 'onDutyArrangement',
    component: onDutyArrangement
  },
  { // dutyArrangementDetail 值班安排详情
    path: '/dutyArrangementDetail',
    name: 'dutyArrangementDetail',
    component: dutyArrangementDetail
  },
  { // 月列表(值班安排管理)
    path: '/arrangeMonthList',
    name: 'arrangeMonthList',
    component: arrangeMonthList
  },
  { // 值班日志管理
    path: '/logbook',
    name: 'logbook',
    component: logbook
  },
  { // dutyArrangementDetail 日志详情
    path: '/logbookDetail',
    name: 'logbookDetail',
    component: logbookDetail
  },
  { // 月列表(值班日志管理)
    path: '/logMonthList',
    name: 'logMonthList',
    component: logMonthList
  }
]
export default theOffice
