<template>
  <div class="committeeDataAdd">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="组织架构名称"
                    prop="title"
                    class="form-title">
        <el-input placeholder="请输入组织架构名称"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="所属结构"
                    prop="structureId"
                    class="form-input">
        <zy-select width="296"
                   node-key="id"
                   :props="{children: 'children',label: 'name'}"
                   v-model="form.structureId"
                   :data="structureIdData"
                   placeholder="请选择所属结构"></zy-select>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'committeeDataAdd',
  data () {
    return {
      form: {
        title: '',
        structureId: '1'
      },
      rules: {
        title: [
          { required: true, message: '请输入组织架构名称', trigger: 'blur' }
        ],
        structureId: [
          { required: true, message: '请选择所属结构', trigger: 'blur' }
        ]
      },
      structureIdData: []
    }
  },
  props: ['id', 'parentId'],
  mounted () {
    this.informationColumnTree()
    if (this.id) {
      this.informationListInfo()
    } else {
      if (this.parentId && this.parentId !== '1') {
        this.form.structureId = this.parentId
      }
    }
  },
  methods: {
    async informationColumnTree () {
      const res = await this.$api.appManagement.organizationTree({ })
      var { data } = res
      this.structureIdData = data
    },
    async informationListInfo () {
      const res = await this.$api.appManagement.informationListInfo(this.id)
      var { data } = res
      this.form.title = data.title
      this.form.structureId = data.structureId
    },
    /**
   * 提交
  */
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/organization/add'
          if (this.id) {
            url = '/organization/edit'
          }
          this.$api.appManagement.organizationAdd(url, {
            name: this.form.title,
            parentId: this.form.structureId
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.committeeDataAdd {
  width: 988px;
  .form-img {
    width: 100%;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
