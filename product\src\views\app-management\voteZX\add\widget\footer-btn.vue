<template>
  <div class="add-footer-btn">
    <el-tooltip effect="dark" content="上一步" placement="left">
      <el-button @click="handleStep('prev')" circle v-show="active>1" icon="el-icon-arrow-up"></el-button>
    </el-tooltip>
    <el-tooltip effect="dark" content="下一步" placement="left">
      <el-button @click="handleStep('next')" circle icon="el-icon-arrow-down" v-show="active<4"></el-button>
    </el-tooltip>
    <el-tooltip effect="dark" content="提交" placement="left">
      <el-button type="primary" @click="handleSubmit" icon="el-icon-s-promotion" circle></el-button>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Number,
      default: 1
    }
  },
  methods: {
    handleStep(type) {
      this.$emit('update:active', type === 'next' ? this.active + 1 : this.active - 1)
    },
    handleSubmit() {
      this.$emit('submit')
    }
  }
}
</script>

<style lang="scss" >
.add-footer-btn {
  position: fixed;
  bottom: 120px;
  right: 45px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  .el-button {
    margin-bottom: 10px;
    margin-left: 0;
  }
}
</style>
