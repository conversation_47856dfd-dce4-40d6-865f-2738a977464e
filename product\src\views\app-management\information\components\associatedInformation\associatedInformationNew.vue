<template>
  <div class="associatedInformationNew">
    <div class="button-box-list">
      <el-button type="primary"
                 @click="newData">关联</el-button>
    </div>
    <div class="tableData tableSmall">
      <zy-table>
        <el-table slot="zytable"
                  :data="tableData"
                  ref="table"
                  style="width: 100%"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           width="48">
          </el-table-column>
          <el-table-column label="标题"
                           prop="title"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="所属栏目"
                           width="120"
                           show-overflow-tooltip
                           prop="structureName">
          </el-table-column>
          <el-table-column label="资讯类型"
                           width="90"
                           prop="infoClass">
          </el-table-column>
          <el-table-column label="显示类型"
                           width="90"
                           prop="infoType">
          </el-table-column>
          <el-table-column label="来源"
                           width="120"
                           prop="source"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="发布人"
                           width="120"
                           prop="createBy">
          </el-table-column>
          <el-table-column label="发布时间"
                           width="160"
                           prop="publishDate">
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
export default {
  name: 'associatedInformationNew',
  data () {
    return {
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      mosaicId: '',
      show: false
    }
  },
  mixins: [tableData],
  props: ['id'],
  mounted () {
    if (this.id) {
      this.associatedAddList()
    }
    this.value = 'relateId'
  },
  methods: {
    async associatedAddList () {
      const res = await this.$api.appManagement.associatedAddList({
        notId: this.id,
        pageNo: this.page,
        pageSize: this.pageSize
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.associatedAddList()
    },
    whatPage (val) {
      this.associatedAddList()
    },
    newData () {
      if (this.choose.length) {
        this.$confirm('此操作将关联当前选中的数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.associatedAdd(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消关联'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async associatedAdd (id) {
      const res = await this.$api.appManagement.associatedAdd({
        detailId: this.id,
        relationIds: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        this.$emit('newCallback')
      }
    }
  }
}
</script>
<style lang="scss">
.associatedInformationNew {
  width: 1100px;
  height: calc(85vh - 52px);
  padding: 16px 24px;
  padding-bottom: 0;

  .button-box-list {
    height: 36px;
    margin-bottom: 12px;

    .el-button {
      width: 64px;
      max-width: 64px;
      min-width: 64px;
      padding: 0;
      height: 36px;
      font-size: 12px;
    }
  }
  .tableData {
    height: calc(100% - 100px) !important;
  }
}
</style>
