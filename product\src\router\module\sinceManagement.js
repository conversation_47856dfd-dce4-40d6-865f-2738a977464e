// 我的履职 政协
const IStartedZX = () => import('@/views/sinceManagementZX/IStartedZX/IStartedZX')
// 履职情况统计 政协
const SinceSituationZX = () => import('@/views/sinceManagementZX/SinceSituationZX/SinceSituationZX')
// 履职情况统计详情 政协
const SinceDetailsZX = () => import('@/views/sinceManagementZX/SinceDetailsZX/SinceDetailsZX')
// 我的年度述职报告 政协
const MySinceReportZX = () => import('@/views/sinceManagementZX/SinceReportZX/MySinceReportZX')
// 年度述职报告管理 政协
const SinceReportManagementZX = () => import('@/views/sinceManagementZX/SinceReportZX/SinceReportManagementZX')
// 履职项配置 政协
const SinceConfigurationZX = () => import('@/views/sinceManagementZX/SinceSituationZX/SinceConfigurationZX')
// 我的履职 人大
const IStarted = () => import('@/views/sinceManagement/IStarted/IStarted')
// 履职情况统计 人大
const SinceSituation = () => import('@/views/sinceManagement/SinceSituation/SinceSituation')
// 履职情况统计 人大
const SinceSituationAudit = () => import('@/views/sinceManagement/SinceSituation/SinceSituationAudit')
// 履职情况统计详情 人大
const SinceDetails = () => import('@/views/sinceManagement/SinceDetails/SinceDetails')
// 我的年度述职报告 人大
const MySinceReport = () => import('@/views/sinceManagement/SinceReport/MySinceReport')
// 年度述职报告管理 人大
const SinceReportManagement = () => import('@/views/sinceManagement/SinceReport/SinceReportManagement')
// 手动修改履职记录
const PerformanceRecord = () => import('@/views/sinceManagementZX/PerformanceRecord/PerformanceRecord')
const sinceManagement = [
  {
    path: '/IStartedZX',
    name: 'IStartedZX',
    component: IStartedZX
  },
  {
    path: '/SinceSituationZX',
    name: 'SinceSituationZX',
    component: SinceSituationZX
  },
  {
    path: '/SinceDetailsZX',
    name: 'SinceDetailsZX',
    component: SinceDetailsZX
  },
  {
    path: '/MySinceReportZX',
    name: 'MySinceReportZX',
    component: MySinceReportZX
  },
  {
    path: '/SinceReportManagementZX',
    name: 'SinceReportManagementZX',
    component: SinceReportManagementZX
  },
  {
    path: '/SinceConfigurationZX',
    name: 'SinceConfigurationZX',
    component: SinceConfigurationZX
  },
  {
    path: '/PerformanceReport', // 履职填报
    name: 'PerformanceReport',
    component: resolve => (require(['@/views/sinceManagementZX/PerformanceReport/PerformanceReport'], resolve))
  },
  {
    path: '/duty-home',
    name: 'duty-home',
    component: () => import(/* 履职服务首页 */'@/views/main/duty-home/duty-home.vue')
  },
  {
    path: '/IStarted',
    name: 'IStarted',
    component: IStarted
  },
  {
    path: '/SinceSituation',
    name: 'SinceSituation',
    component: SinceSituation
  },
  {
    path: '/SinceSituationAudit',
    name: 'SinceSituationAudit',
    component: SinceSituationAudit
  },
  {
    path: '/SinceDetails',
    name: 'SinceDetails',
    component: SinceDetails
  },
  {
    path: '/MySinceReport',
    name: 'MySinceReport',
    component: MySinceReport
  },
  {
    path: '/SinceReportManagement',
    name: 'SinceReportManagement',
    component: SinceReportManagement
  },
  {
    path: '/representativeAll',
    name: 'representativeAll',
    component: resolve => (require(['@/views/sinceManagement/representative-All/representative-All'], resolve))
  },
  {
    path: '/PerformanceFiles',
    name: 'PerformanceFiles',
    component: resolve => (require(['@/views/sinceManagement/PerformanceFiles/PerformanceFiles'], resolve))
  },
  {
    path: '/PerformanceAllocation',
    name: 'PerformanceAllocation',
    component: resolve => (require(['@/views/sinceManagement/PerformanceAllocation/PerformanceAllocation'], resolve))
  },
  {
    path: '/PerformanceItem',
    name: 'PerformanceItem',
    component: resolve => (require(['@/views/sinceManagement/PerformanceItem/PerformanceItem'], resolve))
  },
  {
    path: '/PerformanceRecord',
    name: 'PerformanceRecord',
    component: PerformanceRecord
  }
]
export default sinceManagement
