.zy-select-checkbox {
  width: 100%;
  min-height: 40px;

  .zy-select-checkbox-input {
    width: 100%;
    min-height: 40px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 3px 16px;
    padding-right: 40px;
    overflow: hidden;
    position: relative;

    .zy-select-checkbox-input-text {
      color: #999;
      font-size: 14px;
    }

    .zy-select-checkbox-input-icon {
      position: absolute;
      top: -1px;
      right: -1px;
      z-index: 2;
      height: 100%;
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;

      .el-icon-arrow-down {
        color: #999;
        transition-duration: 0.4s;
        transform: rotate(0);
      }
    }

    .zy-select-checkbox-input-icon-a {
      .el-icon-arrow-down {
        transition-duration: 0.4s;
        transform: rotate(-180deg);
      }
    }
    .el-tag {
      margin: 3px 0;
      margin-right: 12px;
    }

    &:hover {
      border-color: #c0c4cc;
    }

    &:focus {
      border-color: $zy-color;
    }
  }

  .zy-select-checkbox-input-disabled {
    background-color: #e6e5e8;
    border-color: #e4e7ed !important;
    cursor: not-allowed;

    .zy-select-checkbox-input-icon {
      background-color: #e6e5e8;
    }
  }
}

.zy-select-checkbox-popover {
  padding: 0;

  .zy-select-checkbox-box-box {
    height: 260px;
    width: 100%;

    .el-scrollbar__wrap {
      overflow-x: hidden;

      .el-input {
        width: calc(100% - 0.5px);
      }
    }
  }

  .zy-select-checkbox-box {
    height: 220px;
    width: 100%;

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
  }
}

.zy-select-checkbox-tree {
  display: inline-block !important;
  min-width: 100%;

  .el-tree-node__content {
    height: 40px;
    line-height: 40px;

    &:hover {
      background-color: $zy-withColor;
    }
  }
  .el-tree-node.is-current {
    .el-tree-node__content {
      background-color: $zy-withColor;
    }
  }
}

.zy-select-checkbox-tree-a {
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }

    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
