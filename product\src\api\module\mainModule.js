import { post, postform, fileRequest, exportFile } from '../http'

const mainModule = {
  generalAdd (url, params) {
    return post(url, params)
  },
  // 上传附件
  uploadFile (params) {
    return postform('/attachment/uploadFile', params, { timeout: 80000 })
  },
  // 下载附件
  downloadFile (params, text) {
    fileRequest('/attachment/downloadFile?', params, text)
  },
  // 导出excel表头字段列表
  excelHeadInfoList (params) {
    return post('/excel/head/info/list', params)
  },
  // 导出excel表头字段列表详情
  excelHeadInfoDetail (params) {
    return post(`/excel/head/info/detail/${params}`)
  },
  // 导出excel模块表头列表
  excelHeadRelationList (params) {
    return post('/excel/head/relation/list', params)
  },
  // 导出excel模块表头列表详情
  excelHeadRelationDetail (params) {
    return post(`/excel/head/relation/info/${params}`)
  },
  // 导出excel模块表头列表删除
  excelHeadRelationDel (params) {
    return post('/excel/head/relation/dels', params)
  },
  // 导出excel模块表头列表设置表头字段默认选中和不选中
  excelHeadRelationToChecked (params) {
    return post('/excel/head/relation/toChecked', params)
  },
  // 导出excel模块表头列表设置表头字段排序
  excelHeadRelationUpdateSort (params) {
    return post('/excel/head/relation/updateSort', params)
  },
  // 办理单位列表
  tagroupList (params) {
    return post('/flowgroup/list', params)
  },
  // 办理单位详情
  tagroupInfo (params) {
    return post(`/flowgroup/info/${params}`)
  },
  tagroupUserList (params) {
    return post('/flowgroup/userList?', params)
  },
  // 选择办理单位
  chooseListTree (params) {
    return post('/flowgroup/chooseList', params)
  },
  // 办理单位导入模板
  importemplate (params) {
    exportFile('/flowgroup/importemplate', params)
  },
  // 办理单位导入
  import (params) {
    return postform('/flowgroup/import', params)
  },
  // 办理单位删除
  tagroupDel (params) {
    return post(`/flowgroup/del/${params}`)
  },
  // 办理单位删除
  tagroupDels (params) {
    return post('/flowgroup/dels', params)
  },
  // 办理单位启用禁用
  batchUpdateState (params) {
    return post('/flowgroup/batchUpdateState', params)
  }
}
export default mainModule
