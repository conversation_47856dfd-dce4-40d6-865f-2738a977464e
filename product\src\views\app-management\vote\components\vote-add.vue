<template>
  <div class="vote-add">
    <el-form :model="addform"
             ref="addform"
             inline
             class="newForm"
             :rules="addRule"
             label-position="top">
      <el-form-item label="主题"
                    class="form-title"
                    prop="name">
        <el-input v-model="addform.name"></el-input>
      </el-form-item>
      <el-form-item label="简介"
                    class="form-title"
                    prop="instructions">
        <el-input type="textarea"
                  v-model="addform.instructions"></el-input>
      </el-form-item>
      <el-form-item label="开始时间-结束时间"
                    prop="time">
        <el-date-picker v-model="addform.time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="成绩公布方式"
                    class="form-input"
                    prop="issueType">
        <el-select v-model="addform.issueType"
                   placeholder="请选择">
          <el-option v-for="item in sueTypeList"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="成绩公布时间"
                    class="form-input"
                    v-if="addform.issueType === '2'">
        <el-date-picker v-model="addform.publishDate"
                        type="datetime"
                        placeholder="公布时间"
                        value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <!-- <el-row :gutter="20">
          <el-col :span="10">
            <el-form-item label="开始时间"
              prop="startTime">
              <el-date-picker v-model="addform.startTime"
                type="datetime"
                placeholder="开始时间"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="结束时间"
              prop="endTime">
              <el-date-picker v-model="addform.endTime"
                type="datetime"
                placeholder="结束时间"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row> -->
      <el-form-item label="组织方"
                    class="form-input">
        <zy-cascader width="296"
                     node-key="id"
                     clearable
                     v-model="addform.organize"
                     :data="organizeList"
                     placeholder="组织方">
        </zy-cascader>
      </el-form-item>
      <el-form-item label="参与用户"
                    class="form-title">
        <p>
          <span style="margin-left:10px;">所有人</span>
          <el-switch v-model="isAll"
                     active-color="#94070A"
                     inactive-color="#ddd">
          </el-switch>
        </p>
        <p v-if="!isAll">
          <el-button size="small"
                     type="primary"
                     @click="addMan">添加用户</el-button>
        </p>
        <div class="join-man"
             v-if="!isAll">
          <span class="label">已选人员:</span>
          <el-tag class="tag"
                  v-for="(item,index) in userData"
                  :key="index"
                  @close="deleteMan(index)"
                  closable>{{item.name}}</el-tag>
        </div>
      </el-form-item>
      <!-- <el-form-item label="是否分享"
          prop="isShare">
          <el-radio-group v-model="addform.isShare">
            <el-radio :label="0">是</el-radio>
            <el-radio :label="1">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
      <!-- <el-form-item label="是否发布"
                      prop="isPublish">
          <el-radio-group v-model="addform.isPublish">
            <el-radio :label="'1'">是</el-radio>
            <el-radio :label="'0'">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
      <!-- <el-form-item label="结果是否公开"
          prop="remarks">
          <el-radio-group v-model="addform.remarks">
            <el-radio :label="0">是</el-radio>
            <el-radio :label="1">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
      <!-- <el-form-item label="备注" >
            <el-input type="textarea" v-model="addform.content"></el-input>
          </el-form-item> -->
      <el-form-item label="成绩梯度">
        <p>
          <el-button type="primary"
                     size="small"
                     @click="addRating">新增梯度</el-button>
        </p>
        <div>
          <div class="option-item"
               v-for="(item,index) in RatingList"
               :key="index">
            <el-input v-model="item.startSource"
                      placeholder="分数"
                      class="input source source-left-none"
                      size="small">
            </el-input>
            至
            <el-input v-model="item.endSource"
                      placeholder="分数"
                      class="input source"
                      size="small"></el-input>
            <el-input v-model="item.text"
                      placeholder="请输入梯度说明"
                      class="input text"
                      size="small"></el-input>
            <div class="close"
                 @click="delOption(index)">
              <i class="el-icon-close"></i>
            </div>
          </div>
        </div>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="onSubmit('addform')">提交</el-button>
        <el-button @click="cancel('addform')">取消</el-button>
      </div>
    </el-form>
    <zy-pop-up v-model="userShow"
               title="选择参与用户">
      <candidates-user point="point_19"
                       :data="userData"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  props: {
    editData: Object
  },
  data () {
    return {
      isAll: true,
      addform: {
        // isPublish: '0',
        name: '',
        instructions: '',
        organize: '',
        // isShare: 1,
        // remarks: 1,
        time: [],
        issueType: '',
        publishDate: ''
      },
      organizeList: [],
      sueTypeList: [],
      addRule: {
        name: [{ required: true, message: '请输入主题', trigger: 'blur' }],
        instructions: [{ required: true, message: '请输入说明', trigger: 'blur' }],
        // startTime: [{ required: true, message: '请选择开始时间', trigger: 'blur' }],
        // endTime: [{ required: true, message: '请选择结束时间', trigger: 'blur' }],
        time: [{ required: true, message: '请选择时间', trigger: 'blur' }],
        isRelease: [{ required: true, message: '请选择是否发布', trigger: 'change' }],
        issueType: [{ required: true, message: '请选择成绩公布方式', trigger: 'change' }]
        // isShare: [{ required: true, message: '请选择是否分享', trigger: 'change' }],
        // remarks: [{ required: true, message: '请选择是否公开', trigger: 'change' }]
      },
      userData: [],
      userShow: false,
      RatingList: []
    }
  },
  mounted () {
    if (this.editData) {
      this.$api.appManagement.voteInfo(this.editData.id).then(res => {
        console.log(res)
        if (res.errcode === 200) {
          const data = res.data
          this.addform.name = data.name
          this.addform.instructions = data.instructions
          this.addform.time = [data.startTime, data.endTime]
          this.addform.issueType = data.issueType
          this.addform.organize = data.organize
          // console.log(this.addform)
          if (data.issueType === '2') {
            this.addform.publishDate = data.publishDate
          }
          this.isAll = data.isVisible !== 1
          if (!this.isAll) {
            this.userData = res.data.users.map(item => {
              return {
                userId: item.id,
                name: item.username,
                userName: item.username
              }
            })
          }
          // this.addform.isPublish = data.isPublish
          // console.log('这里')
          this.RatingList = JSON.parse(data.remarks)
        }
      })
    }
    this.getTypeList()
  },
  methods: {

    // 激活选人
    addMan () {
      this.userShow = !this.userShow
    },
    // 选人回调
    userCallback (data, type) {
      if (type) {
        this.userData = data
      }
      this.userShow = !this.userShow
    },
    // 删除已选人员
    deleteMan (index) {
      this.userData.splice(index, 1)
    },
    // 提交
    onSubmit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const data = this.addform
          if (this.isAll) {
            data.isVisible = 0
          } else {
            data.isVisible = 1
            if (this.userData.length === 0) {
              return this.$message.warning('请选择参与人员')
            }
            data.joinUserId = this.userData.map(item => item.userId).join(',')
          }
          data.startTime = this.addform.time[0]
          data.endTime = this.addform.time[1]
          if (this.RatingList.length === 0) {
            return this.$message.warning('请选择成绩梯度')
          } else {
            if (this.RatingList.some(v => v.startSource === '')) {
              return this.$message.warning('请输入成绩梯度开始分数')
            }
            if (this.RatingList.some(v => v.endSource === '')) {
              return this.$message.warning('请输入成绩梯度结束分数')
            }
            if (this.RatingList.some(v => parseFloat(v.startSource).toString() === 'NaN' || parseFloat(v.endSource).toString() === 'NaN')) {
              return this.$message.warning('分数请输入数值')
            }
          }
          data.remarks = JSON.stringify(this.RatingList)
          if (this.editData) {
            data.id = this.editData.id
          }
          this.$api.appManagement.voteAdd(data).then(res => {
            if (res.errcode === 200) {
              if (this.editData) {
                this.$message.success('修改考试成功')
              } else {
                this.$message.success('新增考试成功')
              }
              this.$emit('cancel', true)
            }
          })
        } else {
          return false
        }
      })
    },
    // 取消
    cancel (formName) {
      this.$refs[formName].resetFields()
      this.userData = []
      this.$emit('cancel')
    },
    // 获取所属分类
    getTypeList () {
      this.$api.systemSettings.dictionaryPubkvs({ types: 'per_issue_type' }).then(res => {
        if (res.errcode === 200) {
          this.sueTypeList = res.data.per_issue_type
        }
      })
      this.$api.systemSettings.treeList({ treeType: 1 }).then(res => {
        if (res.errcode === 200) {
          this.organizeList = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
    },
    // 新增梯度
    addRating () {
      this.RatingList.push({ startSource: '', endSource: '', text: '' })
    },
    // 删除梯度q
    delOption (index) {
      this.RatingList.splice(index, 1)
    }
  }
}
</script>

<style lang="scss">
.vote-add {
  width: 682px;
  .option-item {
    display: flex;
    align-items: center;
    width: 100%;
    .source {
      width: 60px;
      margin: 0 10px;
    }
    .source-left-none {
      margin-left: 0px;
    }
    .text {
      width: calc(100% - 200px);
    }
    .close {
      display: none;
      width: 32px;
      height: 32px;
      font-size: 18px;
      color: red;
      text-align: center;
      line-height: 32px;
      cursor: pointer;
    }
  }
  .option-item:hover {
    .close {
      display: block;
    }
  }
  .join-man {
    width: 100%;
    border: 1px solid #ededed;
    border-radius: 4px;
    padding: 10px;
    overflow: hidden;
    max-height: 150px;
    .label {
      color: #94070a;
    }
    .tag {
      margin-left: 10px;
    }
  }
  .join-man:hover {
    overflow-y: overlay;
  }
  .join-man::-webkit-scrollbar {
    width: 6px;
  }
  .join-man::-webkit-scrollbar-track {
    border-radius: 6px;
  }
  .join-man::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.1);
  }
}
</style>
