<template>
  <div class="projectAssociatedNew">
    <div class="button-box-list">
      <el-button type="primary"
                 @click="newData">关联</el-button>
      <el-input placeholder="请输入内容"
                clearable
                v-model="name"
                @keyup.enter.native="search">
        <div slot="prefix"
             class="input-search"></div>
      </el-input>
    </div>
    <div class="projectAssociatedBox">
      <div class="projectAssociatedTree scrollBar">
        <el-tree :data="tree"
                 :props="defaultProps"
                 :expand-on-click-node="false"
                 @node-click="handleNodeClick"></el-tree>
      </div>
      <div class="projectAssociatedTable">
        <div class="tableData tableSmall">
          <zy-table>
            <el-table :data="tableData"
                      ref="table"
                      slot="zytable"
                      @select="selected"
                      @select-all="selectedAll">
              <el-table-column type="selection"
                               width="48">
              </el-table-column>
              <el-table-column label="标题"
                               prop="title"
                               show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="发布时间"
                               width="180"
                               prop="createDate"
                               show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="操作"
                               width="80">
                <template slot-scope="scope">
                  <el-button @click="projectAssociatednew(scope.row.relateRecordId)"
                             type="text"
                             size="small">关联</el-button>
                </template>
              </el-table-column>
            </el-table>
          </zy-table>
        </div>
        <div class="paging_box">
          <el-pagination @size-change="howManyArticle"
                         @current-change="whatPage"
                         :current-page.sync="page"
                         :page-sizes="$pageSizes()"
                         :page-size.sync="pageSize"
                         background
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
export default {
  name: 'projectAssociatedNew',
  data () {
    return {
      name: '',
      treeId: '',
      tree: [],
      defaultProps: {
        children: 'children',
        label: 'value'
      },
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize()
    }
  },
  mixins: [tableData],
  props: ['id', 'columnId'],
  mounted () {
    this.dictionaryPubkvs()
    this.value = 'relateRecordId'
  },
  methods: {
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'subject_related_info_type'
      })
      var { data } = res
      if (data.subject_related_info_type.length) {
        this.tree = data.subject_related_info_type
      } else {
        if (this.$system() === '人大') {
          this.tree = [
            { id: '5', value: '资讯' },
            // { id: '26', name: '会议' },
            { id: '4', value: '意见征集' },
            { id: '27', value: '通知公告' },
            { id: '53', value: '专题资讯' },
            { id: '49', value: '活动' }
          ]
        } else {
          this.tree = [
            { id: '5', value: '资讯' },
            // { id: '26', name: '会议' },
            { id: '4', value: '网络议政' },
            { id: '27', value: '通知公告' },
            { id: '53', value: '专题资讯' },
            { id: '49', value: '活动' }
          ]
        }
      }
    },
    handleNodeClick (data) {
      this.treeId = data.id
      this.choose = []
      this.selectObj = []
      this.projectAssociatedrecord()
    },
    async projectAssociatedrecord () {
      const res = await this.$api.appManagement.projectAssociatedrecord({
        subjectId: this.id,
        columnId: this.columnId,
        relateType: this.treeId,
        pageNo: this.page,
        pageSize: this.pageSize,
        title: this.name
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    search () {
      if (this.treeId) {
        this.page = 1
        this.projectAssociatedrecord()
      } else {
        this.$message({
          message: '请选择要关联的类型在进行搜索！',
          type: 'warning'
        })
      }
    },
    newData () {
      if (this.choose.length) {
        this.$confirm('此操作将关联当前选中的数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.projectAssociatednew(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消关联'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async projectAssociatednew (id) {
      const res = await this.$api.appManagement.projectAssociatednew({
        relateRecordIds: id,
        subjectId: this.id,
        columnId: this.columnId,
        relateType: this.treeId
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        this.$emit('newCallback')
      }
    },
    howManyArticle (val) {
      this.projectAssociatedrecord()
    },
    whatPage (val) {
      this.projectAssociatedrecord()
    }
  }
}
</script>
<style lang="scss">
.projectAssociatedNew {
  width: 1120px;
  height: calc(85vh - 52px);
  padding: 16px 24px;
  padding-bottom: 0;

  .button-box-list {
    height: 36px;
    margin-bottom: 12px;

    .el-button {
      width: 64px;
      max-width: 64px;
      min-width: 64px;
      padding: 0;
      height: 36px;
      font-size: 12px;
    }

    .el-input {
      width: 268px;
      float: right;

      .el-input__inner {
        height: 36px;
        line-height: 36px;
        padding-left: 31px;
        font-size: 12px;
        border-radius: 2px;
      }

      .el-input__prefix {
        width: 31px;
        height: 100%;
        display: flex;
        align-items: center;
        left: 0;
        padding-left: 9px;
        box-sizing: border-box;

        .input-search {
          width: 14px;
          height: 14px;
          background: url("../../../../../assets/img/input-search.png");
          background-size: 100% 100%;
        }
      }
    }
  }

  .projectAssociatedBox {
    display: flex;
    height: calc(100% - 52px);

    .projectAssociatedTree {
      width: 222px;
      height: 100%;
      border: 1px solid #d9d9d9;

      .el-tree {
        display: inline-block !important;
        min-width: 100%;
        padding-bottom: 17px;

        .el-tree-node__content {
          height: 40px;
          line-height: 40px;

          &:hover {
            background-color: $zy-withColor;
          }
        }

        .is-current > .el-tree-node__content {
          background-color: $zy-withColor;
          color: $zy-color;
          font-weight: 600;
        }
      }
    }

    .projectAssociatedTable {
      width: calc(100% - 222px);
      height: 100%;

      .tableData {
        height: calc(100% - 52px) !important;
      }
    }
  }
}
</style>
