/**
 * 提案者
 */
// 提交提案
const proposalNew = () => import('@/views/mainModule/proposal/proposalMember/proposalNew')
// 我领衔的提案
const proposalLedBy = () => import('@/views/mainModule/proposal/proposalMember/proposalLedBy')
// 我联名的提案
const proposalJoint = () => import('@/views/mainModule/proposal/proposalMember/proposalJoint')
// 提案草稿箱
const proposalDraftBox = () => import('@/views/mainModule/proposal/proposalMember/proposalDraftBox')
// 提案线索
const proposalClues = () => import('@/views/mainModule/proposal/proposalMember/proposalClues/proposalClues')

// 提案详情
const proposalDetails = () => import('@/views/mainModule/proposal/components/proposalDetails')
/**
 * 提案管理
 */
// 智能并案
const proposalIncorporate = () => import('@/views/mainModule/proposal/proposalManagement/proposalIncorporate/proposalIncorporate')
// 已并案
const proposalHasIncorporate = () => import('@/views/mainModule/proposal/proposalManagement/proposalIncorporate/proposalHasIncorporate')
// 提交提案
const proposalAll = () => import('@/views/mainModule/proposal/proposalManagement/proposalAll/proposalAll')
// 通用标识的提案列表
const proposalLogo = () => import('@/views/mainModule/proposal/proposalManagement/proposalLogo/proposalLogo')
// 专委会审查
const stayBranchReview = () => import('@/views/mainModule/proposal/proposalManagement/proposalReview/stayBranchReview')
// 待审查提案
const proposalReview = () => import('@/views/mainModule/proposal/proposalManagement/proposalReview/proposalReview')
// 待复审提案
const proposalRecheck = () => import('@/views/mainModule/proposal/proposalManagement/proposalReview/proposalRecheck')
// 待审定提案
const proposalApproval = () => import('@/views/mainModule/proposal/proposalManagement/proposalReview/proposalApproval')
// 不予立案
const NotToPutOnRecord = () => import('@/views/mainModule/proposal/proposalManagement/NotToPutOnRecord/NotToPutOnRecord')
// 转社情民意
const turnPublicOpinion = () => import('@/views/mainModule/proposal/proposalManagement/NotToPutOnRecord/turnPublicOpinion')
// 转来信
const turnLetterFrom = () => import('@/views/mainModule/proposal/proposalManagement/NotToPutOnRecord/turnLetterFrom')
// 撤案
const cancel = () => import('@/views/mainModule/proposal/proposalManagement/NotToPutOnRecord/cancel')
// 政协交办中
const cppccAssignedBy = () => import('@/views/mainModule/proposal/proposalManagement/proposalAssigned/cppccAssignedBy')
// 政府交办中
const governmentProposal = () => import('@/views/mainModule/proposal/proposalManagement/proposalAssigned/governmentProposal')
// 党委交办中
const partyCommitteeProposal = () => import('@/views/mainModule/proposal/proposalManagement/proposalAssigned/partyCommitteeProposal')
// 两院交办中
const bothHousesProposal = () => import('@/views/mainModule/proposal/proposalManagement/proposalAssigned/bothHousesProposal')
// 法院交办中
const courtProposal = () => import('@/views/mainModule/proposal/proposalManagement/proposalAssigned/courtProposal')
// 检察院交办中
const procuratorateProposal = () => import('@/views/mainModule/proposal/proposalManagement/proposalAssigned/procuratorateProposal')
// 待签收提案
const waitingSignProposal = () => import('@/views/mainModule/proposal/proposalManagement/proposalAssigned/waitingSignProposal')
// 待签收申请调整
const applyAdjustProposal = () => import('@/views/mainModule/proposal/proposalManagement/proposalAssigned/applyAdjustProposal')
// 办理中提案
const DealtWithInProposal = () => import('@/views/mainModule/proposal/proposalManagement/DealtWithInProposal/DealtWithInProposal')
// 已答复提案
const haveReplyProposal = () => import('@/views/mainModule/proposal/proposalManagement/haveReplyProposal/haveReplyProposal')
// 已办结提案
const haveTransferredProposal = () => import('@/views/mainModule/proposal/proposalManagement/haveTransferredProposal/haveTransferredProposal')
// 申请延期提案
const applyForDelayProposal = () => import('@/views/mainModule/proposal/proposalManagement/applyForDelayProposal/applyForDelayProposal')
// 跟踪办理
const trackingHandleProposal = () => import('@/views/mainModule/proposal/proposalManagement/trackingHandleProposal/trackingHandleProposal')
// 申请调整
const unitApplyForAdjustProposal = () => import('@/views/mainModule/proposal/proposalManagement/unitApplyForAdjustProposal/unitApplyForAdjustProposal')
// 申请修改
const applyUpdateProposal = () => import('@/views/mainModule/proposal/proposalManagement/applyUpdateProposal/applyUpdateProposal')
// 统计分析
const proposalStatistical = () => import('@/views/mainModule/proposal/proposalManagement/proposalStatistical/proposalStatistical')
// 统计分析详情
const proposalStatisticalList = () => import('@/views/mainModule/proposal/proposalManagement/proposalStatistical/proposalStatisticalList')
// 统计分析详情
const proposalStatisticalList2 = () => import('@/views/mainModule/proposal/proposalManagement/proposalStatistical/proposalStatisticalList2')
/**
 * 承办单位
 */
// 单位所有提案
const proposalAllUnit = () => import('@/views/mainModule/proposal/undertakeUnit/proposalAllUnit/proposalAllUnit')
// 待签收提案
const waitingSignProposalUnit = () => import('@/views/mainModule/proposal/undertakeUnit/assignedByUnit/waitingSignProposalUnit')
// 待签收申请调整
const applyAdjustProposalUnit = () => import('@/views/mainModule/proposal/undertakeUnit/assignedByUnit/applyAdjustProposalUnit')
// 历史调整记录
const AdjustRecordProposal = () => import('@/views/mainModule/proposal/undertakeUnit/assignedByUnit/AdjustRecordProposal')
// 办理中提案
const UnitDealtWithInProposal = () => import('@/views/mainModule/proposal/undertakeUnit/UnitDealtWithInProposal/UnitDealtWithInProposal')
// 已答复提案
const UnitHaveReplyProposal = () => import('@/views/mainModule/proposal/undertakeUnit/UnitHaveReplyProposal/UnitHaveReplyProposal')
// 已办结提案
const UnitHaveTransferredProposal = () => import('@/views/mainModule/proposal/undertakeUnit/UnitHaveTransferredProposal/UnitHaveTransferredProposal')
// 跟踪办理
const UnittrackingHandleProposal = () => import('@/views/mainModule/proposal/undertakeUnit/UnittrackingHandleProposal/UnittrackingHandleProposal')
/**
 * 配置管理
 */
// 界次编号
const proposalTimeSerialNumber = () => import('@/views/mainModule/proposal/proposalConfiguration/proposalTimeSerialNumber/proposalTimeSerialNumber')
// 界次年份
const proposalTimeYear = () => import('@/views/mainModule/proposal/proposalConfiguration/proposalTimeYear/proposalTimeYear')
// 提案分类
const proposalType = () => import('@/views/mainModule/proposal/proposalConfiguration/proposalType/proposalType')
// 提案分类用户
const proposalBusinessAndUsers = () => import('@/views/mainModule/proposal/proposalConfiguration/proposalBusinessAndUsers/proposalBusinessAndUsers')
// 集体提案账号管理
const collectiveProposalUnit = () => import('@/views/mainModule/proposal/proposalConfiguration/collectiveProposalUnit/collectiveProposalUnit')

const proposal = [
  {
    path: '/proposalNew',
    name: 'proposalNew',
    component: proposalNew
  },
  {
    path: '/proposalLedBy',
    name: 'proposalLedBy',
    component: proposalLedBy
  },
  {
    path: '/proposalJoint',
    name: 'proposalJoint',
    component: proposalJoint
  },
  {
    path: '/proposalDraftBox',
    name: 'proposalDraftBox',
    component: proposalDraftBox
  },
  {
    path: '/proposalClues',
    name: 'proposalClues',
    component: proposalClues
  },
  {
    path: '/proposalIncorporate',
    name: 'proposalIncorporate',
    component: proposalIncorporate
  },
  {
    path: '/proposalHasIncorporate',
    name: 'proposalHasIncorporate',
    component: proposalHasIncorporate
  },
  {
    path: '/proposalAll',
    name: 'proposalAll',
    component: proposalAll
  },
  {
    path: '/proposalLogo',
    name: 'proposalLogo',
    component: proposalLogo
  },
  {
    path: '/stayBranchReview',
    name: 'stayBranchReview',
    component: stayBranchReview
  },
  {
    path: '/proposalReview',
    name: 'proposalReview',
    component: proposalReview
  },
  {
    path: '/proposalRecheck',
    name: 'proposalRecheck',
    component: proposalRecheck
  },
  {
    path: '/proposalApproval',
    name: 'proposalApproval',
    component: proposalApproval
  },
  {
    path: '/NotToPutOnRecord',
    name: 'NotToPutOnRecord',
    component: NotToPutOnRecord
  },
  {
    path: '/turnPublicOpinion',
    name: 'turnPublicOpinion',
    component: turnPublicOpinion
  },
  {
    path: '/turnLetterFrom',
    name: 'turnLetterFrom',
    component: turnLetterFrom
  },
  {
    path: '/cancel',
    name: 'cancel',
    component: cancel
  },
  {
    path: '/cppccAssignedBy',
    name: 'cppccAssignedBy',
    component: cppccAssignedBy
  },
  {
    path: '/governmentProposal',
    name: 'governmentProposal',
    component: governmentProposal
  },
  {
    path: '/partyCommitteeProposal',
    name: 'partyCommitteeProposal',
    component: partyCommitteeProposal
  },
  {
    path: '/bothHousesProposal',
    name: 'bothHousesProposal',
    component: bothHousesProposal
  },
  {
    path: '/courtProposal',
    name: 'courtProposal',
    component: courtProposal
  },
  {
    path: '/procuratorateProposal',
    name: 'procuratorateProposal',
    component: procuratorateProposal
  },
  {
    path: '/waitingSignProposal',
    name: 'waitingSignProposal',
    component: waitingSignProposal
  },
  {
    path: '/applyAdjustProposal',
    name: 'applyAdjustProposal',
    component: applyAdjustProposal
  },
  {
    path: '/DealtWithInProposal',
    name: 'DealtWithInProposal',
    component: DealtWithInProposal
  },
  {
    path: '/haveReplyProposal',
    name: 'haveReplyProposal',
    component: haveReplyProposal
  },
  {
    path: '/haveTransferredProposal',
    name: 'haveTransferredProposal',
    component: haveTransferredProposal
  },
  {
    path: '/applyForDelayProposal',
    name: 'applyForDelayProposal',
    component: applyForDelayProposal
  },
  {
    path: '/applyUpdateProposal',
    name: 'applyUpdateProposal',
    component: applyUpdateProposal
  },
  {
    path: '/trackingHandleProposal',
    name: 'trackingHandleProposal',
    component: trackingHandleProposal
  },
  {
    path: '/unitApplyForAdjustProposal',
    name: 'unitApplyForAdjustProposal',
    component: unitApplyForAdjustProposal
  },
  {
    path: '/proposalDetails',
    name: 'proposalDetails',
    component: proposalDetails
  },
  {
    path: '/proposalAllUnit',
    name: 'proposalAllUnit',
    component: proposalAllUnit
  },
  {
    path: '/waitingSignProposalUnit',
    name: 'waitingSignProposalUnit',
    component: waitingSignProposalUnit
  },
  {
    path: '/applyAdjustProposalUnit',
    name: 'applyAdjustProposalUnit',
    component: applyAdjustProposalUnit
  },
  {
    path: '/AdjustRecordProposal',
    name: 'AdjustRecordProposal',
    component: AdjustRecordProposal
  },
  {
    path: '/proposalStatistical',
    name: 'proposalStatistical',
    component: proposalStatistical
  },
  {
    path: '/proposalStatisticalList',
    name: 'proposalStatisticalList',
    component: proposalStatisticalList
  },
  {
    path: '/proposalStatisticalList2',
    name: 'proposalStatisticalList2',
    component: proposalStatisticalList2
  },
  {
    path: '/UnitDealtWithInProposal',
    name: 'UnitDealtWithInProposal',
    component: UnitDealtWithInProposal
  },
  {
    path: '/UnitHaveReplyProposal',
    name: 'UnitHaveReplyProposal',
    component: UnitHaveReplyProposal
  },
  {
    path: '/UnitHaveTransferredProposal',
    name: 'UnitHaveTransferredProposal',
    component: UnitHaveTransferredProposal
  },
  {
    path: '/UnittrackingHandleProposal',
    name: 'UnittrackingHandleProposal',
    component: UnittrackingHandleProposal
  },
  { // 界次编号
    path: '/proposalTimeSerialNumber',
    name: 'proposalTimeSerialNumber',
    component: proposalTimeSerialNumber
  },
  { // 界次年份
    path: '/proposalTimeYear',
    name: 'proposalTimeYear',
    component: proposalTimeYear
  },
  { // 提案分类
    path: '/proposalType',
    name: 'proposalType',
    component: proposalType
  },
  { // 用户关系管理
    path: '/proposalBusinessAndUsers',
    name: 'proposalBusinessAndUsers',
    component: proposalBusinessAndUsers
  },
  { // 集体提案账号管理
    path: '/collectiveProposalUnit',
    name: 'collectiveProposalUnit',
    component: collectiveProposalUnit
  }
]
export default proposal
