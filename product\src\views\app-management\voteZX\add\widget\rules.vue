<template>
  <el-form :model="form" class="qd-form vote-rules-form" ref="form" label-position="right" label-width="150px" inline>
    <div class="base-form-item">
      <div class="base-form-item-label">投票设置</div>
      <div class="base-form-item-content">
        <el-form-item label="周期设置" class="form-item-wd100">
          <el-radio-group v-model="form.periodSet">
            <el-radio :label="1">固定次数</el-radio>
            <el-radio :label="2">周期次数</el-radio>
          </el-radio-group>
          <div>
            每个用户{{form.periodSet ===1?'整个活动期间只能投':'每天可以投'}}1次
          </div>
          <!-- <div v-if="form.periodSet === 1">
            每个微信号整个活动期间只能投<el-input-number :min="1" size="mini" v-model="form.voteNum"></el-input-number>次
          </div>
          <div v-if="form.periodSet === 2">
            每个微信号每天可以投<el-input-number :min="1" size="mini" v-model="form.voteNum"></el-input-number>次
          </div> -->
        </el-form-item>
        <el-form-item label="选项设置" class="form-item-wd100">
          <el-radio-group v-model="form.voteType" :disabled="isEdit">
            <el-radio :label="1">单选</el-radio>
            <el-radio :label="2">多选</el-radio>
          </el-radio-group>
          <div v-if="form.voteType ===2">
            最多选择<el-input-number :min="1" size="mini" :disabled="isEdit" v-model="form.playerMax"></el-input-number>个投票项
          </div>
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script>
export default {
  props: { isEdit: Boolean },
  data() {
    return {
      form: {
        periodSet: 1,
        voteNum: 1,
        playerMax: 0,
        voteType: 1
      }
    }
  },
  methods: {
    reset() {
      this.$refs.form.resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.vote-rules-form {
  width: 100%;
  height: 100%;
  margin: 25px auto;
}
</style>
