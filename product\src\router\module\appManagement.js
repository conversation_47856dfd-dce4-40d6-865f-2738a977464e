const informationList = () => import('@/views/app-management/information/informationList')
const informationColumn = () => import('@/views/app-management/information/informationColumn')
const customTopicList = () => import('@/views/app-management/custom-topic/custom-topic-list')
const voteList = () => import('@/views/app-management/vote/vote-list/vote-list')
const voteCount = () => import('@/views/app-management/vote/vote-list/vote-count')
const questionBank = () => import('@/views/app-management/vote/question-bank/question-bank')
const question = () => import('@/views/app-management/vote/vote-list/question')
const questionPurview = () => import('@/views/app-management/vote/vote-list/question-purview')
const committeeData = () => import('@/views/app-management/committee-data/committee-data')
const committeeDataColumn = () => import('@/views/app-management/committee-data/committee-data-column/committee-data-column')
const committeeDataCustomTopic = () => import('@/views/app-management/committee-data-custom-topic/committee-data-custom-topic')
const voteZXList = () => import('@/views/app-management/voteZX/voteZXList') // 投票管理
const voteZXNew = () => import('@/views/app-management/voteZX/voteZXNew') // 投票管理新增
const jobContent = () => import('@/views/app-management/jobContent/jobContent') // 工作内容
const taskList = () => import('@/views/app-management/taskList/taskList') // 任务清单
const organizationalStructure = () => import('@/views/app-management/organizationalStructure/organizationalStructure') // 组织架构
const personnelAdministrationJobAdd = () => import('@/views/app-management/personnelAdministration/personnelAdministrationAdd/personnelAdministrationJobAdd') // 提交在职人员信息
const personnelAdministrationRetireAdd = () => import('@/views/app-management/personnelAdministration/personnelAdministrationAdd/personnelAdministrationRetireAdd') // 提交退休人员信息
const personnelAdministrationJob = () => import('@/views/app-management/personnelAdministration/personnelAdministrationJob/personnelAdministrationJob') // 在职人员信息管理
const personnelAdministrationRetire = () => import('@/views/app-management/personnelAdministration/personnelAdministrationRetire/personnelAdministrationRetire') // 退休人员信息管理
const personnelAdministrationDetails = () => import('@/views/app-management/personnelAdministration/personnelAdministrationDetails') // 详情
const materialScienceAdd = () => import('@/views/app-management/materialScience/materialScienceAdd/materialScienceAdd') // 材料提交
const materialScienceAll = () => import('@/views/app-management/materialScience/materialScienceAll/materialScienceAll') // 我的材料、所有材料
const materialScienceDetails = () => import('@/views/app-management/materialScience/materialScienceDetails')// 详情

const appManagement = [
  { // 投票管理
    path: '/voteZXList',
    name: 'voteZXList',
    component: voteZXList
  },
  { // 投票管理新增
    path: '/voteZXNew',
    name: 'voteZXNew',
    component: voteZXNew
  },
  { // 资讯列表
    path: '/informationList',
    name: 'informationList',
    component: informationList
  },
  { // 资讯栏目
    path: '/informationColumn',
    name: 'informationColumn',
    component: informationColumn
  },
  { // 自定义专题列表
    path: '/customTopicList',
    name: 'customTopicList',
    component: customTopicList
  },
  { // 投票
    path: '/voteList',
    name: 'voteList',
    component: voteList
  },
  { // 投票
    path: '/voteCount',
    name: 'voteCount',
    component: voteCount
  },
  { // 投票
    path: '/question',
    name: 'question',
    component: question
  },
  { // 投票详情
    path: '/questionPurview',
    name: 'questionPurview',
    component: questionPurview
  },
  { // 题目管理
    path: '/questionBank',
    name: 'questionBank',
    component: questionBank
  },
  {
    path: '/zx-voteList',
    name: 'zx-voteList',
    component: () => import(/* 问卷列表 */'@/views/app-management/vote-zx/vote-list/vote-list')
  },
  {
    path: '/zx-voteCount',
    name: 'zx-voteCount',
    component: () => import(/* 问卷统计 */'@/views/app-management/vote-zx/vote-list/vote-count')
  },
  {
    path: '/zx-question',
    name: 'zx-question',
    component: () => import(/* 问卷题目 */'@/views/app-management/vote-zx/vote-list/question')
  },
  {
    path: '/zx-questionPurview',
    name: 'zx-questionPurview',
    component: () => import(/* 问卷详情 */'@/views/app-management/vote-zx/vote-list/question-purview')
  },
  { // 题目管理
    path: '/zx-questionBank',
    name: 'zx-questionBank',
    component: () => import(/* 问卷题库 */'@/views/app-management/vote-zx/question-bank/question-bank')
  },
  {
    path: '/zx-examination',
    name: 'zx-examination',
    component: () => import(/* 问卷统计 */ '@/views/app-management/vote-zx/examination/examination')
  },
  {
    path: '/poll-list',
    name: 'poll-list',
    component: () => import(/* 投票列表 */ '@/views/app-management/poll/poll')
  },
  {
    path: '/poll-info',
    name: 'poll-info',
    component: () => import(/* 投票详情 */ '@/views/app-management/poll/poll-info')
  },
  {
    path: '/poll-count',
    name: 'poll-count',
    component: () => import(/* 投票统计 */ '@/views/app-management/poll/count')
  },
  {
    path: '/poll-man',
    name: 'poll-man',
    component: () => import(/* 投票参与人 */ '@/views/app-management/poll/join-man')
  },
  {
    path: '/poll-options',
    name: 'poll-options',
    component: () => import(/* 投票参与人 */ '@/views/app-management/poll/options')
  },
  { // 履职参阅
    path: '/committeeData',
    name: 'committeeData',
    component: committeeData
  },
  { // 履职参阅栏目
    path: '/committeeDataColumn',
    name: 'committeeDataColumn',
    component: committeeDataColumn
  },
  { // 履职参阅专题
    path: '/committeeDataCustomTopic',
    name: 'committeeDataCustomTopic',
    component: committeeDataCustomTopic
  },
  {
    path: '/newsConfigRule',
    name: 'newsConfigRule',
    component: () => import(/* 资讯抓取配置 */ '@/views/app-management/newsConfig/newsConfig.vue')
  },
  {
    path: '/newsOverallAnalysis',
    name: 'newsOverallAnalysis',
    component: () => import(/* 资讯综合分析 */ '@/views/app-management/newsOverallAnalysis/newsOverallAnalysis.vue')
  },
  { // 工作内容
    path: '/jobContent',
    name: 'jobContent',
    component: jobContent
  },
  { // 任务清单
    path: '/taskList',
    name: 'taskList',
    component: taskList
  },
  { // 组织架构
    path: '/organizationalStructure',
    name: 'organizationalStructure',
    component: organizationalStructure
  },
  { // 提交在职人员信息
    path: '/personnelAdministrationJobAdd',
    name: 'personnelAdministrationJobAdd',
    component: personnelAdministrationJobAdd
  },
  { // 提交退休人员信息
    path: '/personnelAdministrationRetireAdd',
    name: 'personnelAdministrationRetireAdd',
    component: personnelAdministrationRetireAdd
  },
  { // 在职人员信息管理
    path: '/personnelAdministrationJob',
    name: 'personnelAdministrationJob',
    component: personnelAdministrationJob
  },
  { // 退休人员信息管理
    path: '/personnelAdministrationRetire',
    name: 'personnelAdministrationRetire',
    component: personnelAdministrationRetire
  },
  { // 详情
    path: '/personnelAdministrationDetails',
    name: 'personnelAdministrationDetails',
    component: personnelAdministrationDetails
  },
  { // 材料提交
    path: '/materialScienceAdd',
    name: 'materialScienceAdd',
    component: materialScienceAdd
  },
  { // 我的材料、所有材料
    path: '/materialScienceAll',
    name: 'materialScienceAll',
    component: materialScienceAll
  },
  { // 详情
    path: '/materialScienceDetails',
    name: 'materialScienceDetails',
    component: materialScienceDetails
  }
]
export default appManagement
