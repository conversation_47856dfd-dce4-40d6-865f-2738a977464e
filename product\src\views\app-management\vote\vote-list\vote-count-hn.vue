<template>
  <div class="vote-list">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input placeholder="请输入内容"
                v-model="keyword"
                clearable
                @keyup.enter.native="search"></el-input>
      <el-select v-model="questionId"
                 placeholder="请选择题目">
        <el-option v-for="item in questionList"
                   :key="item.id"
                   :label="item.name"
                   :value="item.id">
        </el-option>
      </el-select>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="handleExport">导出</el-button>
      <el-button type="primary"
                 @click="handleCount">投票统计</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="题目"
                           min-width="260"
                           prop="paperQuestion.name"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="选项"
                           min-width="250">
            <template slot-scope="scope">
              <div v-if="scope.row.paperQuestion.topic === 'single'|| scope.row.paperQuestion.topic === 'multi'">
                <p v-for="(item,index) in scope.row.answer"
                   :key="index">{{item}}</p>
              </div>
              <div v-if="scope.row.paperQuestion.topic === 'judge'">
                <i v-if="scope.row.answer === '1'"
                   class="el-icon-close  close-color"></i>
                <i v-if="scope.row.answer === '0'"
                   class="el-icon-check  check-color"></i>
              </div>
              <div v-if="scope.row.paperQuestion.topic === 'text'">
                <p>{{scope.row.answer}}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="题目类型"
                           min-width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.paperQuestion.topic === 'single'">单选</span>
              <span v-if="scope.row.paperQuestion.topic === 'multi'">多选</span>
              <span v-if="scope.row.paperQuestion.topic === 'judge'">判断</span>
              <span v-if="scope.row.paperQuestion.topic === 'text'">文本</span>
            </template>
          </el-table-column>
          <el-table-column label="投票人"
                           prop="userName"
                           min-width="160"></el-table-column>
          <el-table-column label="电话"
                           min-width="160"
                           prop="tel"></el-table-column>
          <el-table-column label="投票时间"
                           min-width="240"
                           prop="createDate"></el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up title="投票统计"
               v-model="isStatic">
      <vote-Static :id="$route.query.id"
                   @cancel="handleCancel"></vote-Static>
    </zy-pop-up>
    <zy-pop-up title="投票统计"
               v-model="isNum">
      <vote-Num :id="$route.query.id"
                :question="question"
                :option="option"></vote-num>
    </zy-pop-up>
  </div>
</template>

<script>

import voteStatic from '../components/vote-static'
import voteNum from '../components/vote-num'
export default {
  data () {
    return {
      keyword: '',
      questionId: '',
      questionList: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      selectionList: [],
      isStatic: false,
      isNum: false,
      question: '',
      option: ''
    }
  },
  components: { voteStatic, voteNum },
  mounted () {
    this.getQuestionList()
    this.getList()
  },
  methods: {
    // 列表
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        paperId: this.$route.query.id
      }
      if (this.keyword !== '') {
        data.keyword = this.keyword
      }
      this.$api.appManagement.voteAnswerList(data).then(res => {
        const { errcode, data } = res
        if (errcode === 200) {
          this.tableData = data.list.map(item => {
            if (item.paperQuestion.topic === 'single' || item.paperQuestion.topic === 'multi') {
              item.answer = item.answer.split('||').filter(item => item)
            }
            return item
          })
          this.total = data.total
        }
      })
    },
    // 获取题目列表
    getQuestionList () {
      const data = {
        pageNo: 1,
        pageSize: 100,
        paperId: this.$route.query.id
      }
      this.$api.appManagement.questionList(data).then(res => {
        if (res.errcode === 200) {
          this.questionList = res.data
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.questionId = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 导出
    handleExport () {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择想要导出的项')
      }
    },
    // 统计
    handleCount () {
      this.isStatic = true
    },
    handleCancel (val, option) {
      this.isStatic = false
      if (val) {
        this.option = option
        this.question = val
        this.isNum = true
      }
    }

  }
}
</script>

<style lang="scss">
@import "./vote-list.scss";
</style>
