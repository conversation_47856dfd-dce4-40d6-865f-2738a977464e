<template>
  <div class="personnelAdministrationRetireAdd">
    <personnelAdministrationRetireNew :id="id"
                      v-if="show"
                      @callback="callback"></personnelAdministrationRetireNew>
  </div>
</template>
<script>
const personnelAdministrationRetireNew = () => import('../personnelAdministrationRetireNew')
export default {
  name: 'personnelAdministrationRetireAdd',
  data () {
    return {
      show: true,
      id: this.$route.query.id,
      toId: this.$route.query.toId
    }
  },
  components: {
    personnelAdministrationRetireNew
  },
  inject: ['tabDel'],
  methods: {
    callback () {
      if (this.id) {
        this.tabDel(this.id, this.toId)
      } else {
        this.show = false
        setTimeout(() => {
          this.show = true
        }, 200)
      }
    }
  }
}
</script>
