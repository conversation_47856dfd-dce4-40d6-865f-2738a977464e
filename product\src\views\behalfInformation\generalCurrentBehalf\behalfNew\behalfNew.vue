<template>
  <div class="behalfNew">
    <div class="behalfNewTitle">代表信息</div>
    <div class="behalfNewForm">
      <div class="behalfNewPortrait">
        <el-upload class="memberUploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <div class="memberUploader-img"
               v-if="file.fullUrl"><img :src="file.fullUrl"></div>
          <div v-else
               class="memberUploader-icon">上传头像</div>
        </el-upload>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel"><span>*</span>姓名</div>
        <div class="behalfNewcontent">
          <el-input placeholder="请输入姓名"
                    v-model="form.userName"
                    @input="e => form.userName = $validForbid(e)"
                    @blur="nameAccount"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">性别</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.sex"
                     clearable
                     placeholder="请选择性别">
            <el-option v-for="item in sex"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel"><span>*</span>账号</div>
        <div class="behalfNewcontent">
          <el-input placeholder="请输入账号"
                    @input="e => form.account = $validForbid(e)"
                    v-model="form.account"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">民族</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.nation"
                     clearable
                     placeholder="请选择民族">
            <el-option v-for="item in nation"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel"><span>*</span>手机号</div>
        <div class="behalfNewcontent">
          <el-input placeholder="请输入手机"
                    v-model="form.mobile"
                    onkeyup="value=value.replace(/[^\d]/g,'')"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">代表证号</div>
        <div class="behalfNewcontent">
          <el-input placeholder="请输入代表证号"
                    v-model="form.userNumber"
                    @input="e => form.userNumber = $validForbid(e)"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">出生年月</div>
        <div class="behalfNewcontent">
          <el-date-picker v-model="form.birthday"
                          type="date"
                          value-format="timestamp"
                          placeholder="选择出生年月">
          </el-date-picker>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">籍贯</div>
        <div class="behalfNewcontent fill">
          <el-input placeholder="请输入籍贯"
                    @input="e => form.nativePlace = $validForbid(e)"
                    v-model="form.nativePlace"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">党派</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.party"
                     clearable
                     placeholder="请选择党派">
            <el-option v-for="item in party"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem points">
        <div class="behalfNewLabel">提名方式</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.belectedType"
                     clearable
                     placeholder="请选择提名方式">
            <el-option v-for="item in belectedType"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">职称</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.professional"
                     clearable
                     placeholder="请选择职称">
            <el-option v-for="item in professional"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem points">
        <div class="behalfNewLabel">是否常委会组成成员</div>
        <div class="behalfNewcontent">
          <el-radio-group v-model="form.isRoutineMember">
            <el-radio :label="item.id"
                      v-for="(item) in isRoutineMember"
                      :key="item.id">{{item.value}}</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">所属结构</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.representerElement"
                     clearable
                     placeholder="请选择所属结构">
            <el-option v-for="item in representerElement"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem points">
        <div class="behalfNewLabel">代表团</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.representerTeam"
                     clearable
                     placeholder="请选择代表团">
            <el-option v-for="item in representerTeam"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">学历</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.education"
                     clearable
                     placeholder="请选择学历">
            <el-option v-for="item in education"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem points">
        <div class="behalfNewLabel">学位</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.degree"
                     clearable
                     placeholder="请选择学位">
            <el-option v-for="item in degree"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">毕业院校</div>
        <div class="behalfNewcontent">
          <el-input placeholder="请输入毕业院校"
                    v-model="form.school"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem points">
        <div class="behalfNewLabel">邮政编码</div>
        <div class="behalfNewcontent">
          <el-input placeholder="请输入邮政编码"
                    v-model="form.postcode"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">健康状况</div>
        <div class="behalfNewcontent">
          <el-select v-model="form.health"
                     clearable
                     placeholder="请选择健康状况">
            <el-option v-for="item in health"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id">
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="behalfNewFormItem points">
        <div class="behalfNewLabel">办公电话</div>
        <div class="behalfNewcontent">
          <el-input placeholder="请输入办公电话"
                    v-model="form.officePhone"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">单位及职务</div>
        <div class="behalfNewcontent all">
          <el-input placeholder="请输入单位及职务"
                    v-model="form.position"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">通讯地址</div>
        <div class="behalfNewcontent all">
          <el-input placeholder="请输入通讯地址"
                    v-model="form.callAddress"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem textarea">
        <div class="behalfNewLabel">个人简历</div>
        <div class="behalfNewcontent all">
          <el-input placeholder="请输入简历"
                    type="textarea"
                    resize="none"
                    :rows="3"
                    v-model="form.introduction"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem textarea">
        <div class="behalfNewLabel">社会荣誉</div>
        <div class="behalfNewcontent all">
          <el-input placeholder="请输入社会荣誉"
                    type="textarea"
                    resize="none"
                    :rows="3"
                    v-model="form.honorInfo"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem textarea">
        <div class="behalfNewLabel">社会贡献</div>
        <div class="behalfNewcontent all">
          <el-input placeholder="请输入社会贡献"
                    type="textarea"
                    resize="none"
                    :rows="3"
                    v-model="form.contribute"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem textarea">
        <div class="behalfNewLabel">个人备注</div>
        <div class="behalfNewcontent all">
          <el-input placeholder="请输入备注"
                    type="textarea"
                    resize="none"
                    :rows="3"
                    v-model="form.remarks"
                    clearable>
          </el-input>
        </div>
      </div>
      <div class="behalfNewFormItem">
        <div class="behalfNewLabel">是否启用</div>
        <div class="behalfNewcontent">
          <el-radio-group v-model="form.isUsing">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="behalfNewFormItem points">
        <div class="behalfNewLabel">接受系统短信</div>
        <div class="behalfNewcontent">
          <el-radio-group v-model="form.isReceiveMsg">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="behalfNewFormItem"
           v-if="$isAppShow()">
        <div class="behalfNewLabel">APP安装情况</div>
        <div class="behalfNewcontent all">
          <el-radio-group v-model="form.isJoinApp">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="behalfNewButton">
      <el-button @click="resetForm">取消</el-button>
      <el-button type="primary"
                 @click="submitForm">提交</el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'behalfNew',
  data () {
    return {
      id: this.$route.query.id,
      toId: this.$route.query.toId,
      memberType: this.$route.query.memberType,
      form: {
        userName: '',
        mobile: '',
        account: '',
        userNumber: '',
        oldName: '',
        sex: '',
        nation: '',
        birthday: '',
        professional: '',
        belectedType: '',
        introduction: '',
        honorInfo: '',
        contribute: '',
        remarks: '',
        isRoutineMember: '',
        nativePlace: '',
        party: '',
        representerElement: '',
        position: '',
        education: '',
        degree: '',
        health: '',
        officePhone: '',
        postcode: '',
        school: '',
        callAddress: '',
        representerTeam: '',
        isUsing: 1,
        isReceiveMsg: 1,
        isJoinApp: 0
      },
      sex: [],
      belectedType: [],
      isRoutineMember: [],
      representerElement: [],
      nation: [],
      representerTeam: [],
      professional: [],
      degree: [],
      party: [],
      education: [],
      health: [],
      file: {}
    }
  },
  inject: ['tabDel', 'refresh'],
  created () {
    this.dictionaryPubkvs()
    if (this.id) {
      this.memberInfo()
    }
  },
  methods: {
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'representer_element,nation_type,work_professional,edudegree_type,sex,belected_type,yes_no,representer_team,party_type,edu_type,health_type'
      })
      var { data } = res
      this.representerElement = data.representer_element
      this.nation = data.nation_type
      this.sex = data.sex
      this.health = data.health_type
      this.education = data.edu_type
      this.party = data.party_type
      this.representerTeam = data.representer_team
      this.isRoutineMember = data.yes_no
      this.belectedType = data.belected_type
      this.professional = data.work_professional
      this.degree = data.edudegree_type
    },
    async memberInfo () {
      const res = await this.$api.memberInformation.memberInfo(`${this.id}?memberType=${this.memberType}`)
      var { data } = res
      this.form.userName = data.userName
      this.form.mobile = data.mobile
      this.form.account = data.account
      this.form.userNumber = data.userNumber
      this.form.oldName = data.oldName
      this.form.sex = data.sex ? data.sex + '' : ''
      this.form.nation = data.nation
      this.form.birthday = data.birthday
      this.form.professional = data.professional.code ? data.professional.code : ''
      this.form.belectedType = data.belectedType ? data.belectedType + '' : ''
      this.form.introduction = data.introduction
      this.form.honorInfo = data.honorInfo
      this.form.contribute = data.contribute
      this.form.remarks = data.remarks
      this.form.isRoutineMember = data.isRoutineMember
      this.form.nativePlace = data.nativePlace
      this.form.party = data.party
      this.form.representerElement = data.representerElement
      this.form.position = data.position
      this.form.education = data.education
      this.form.degree = data.degree
      this.form.health = data.health
      this.form.officePhone = data.officePhone
      this.form.postcode = data.postcode
      this.form.school = data.school
      this.form.callAddress = data.callAddress
      this.form.representerTeam = data.representerTeam
      this.form.isUsing = data.isUsing
      this.form.isReceiveMsg = data.isReceiveMsg
      this.form.isJoinApp = data.isJoinApp
      if (data.fullHeadImg) {
        var arr = {}
        arr.fullUrl = data.fullHeadImg
        arr.shortName = data.headImg
        this.file = arr
      }
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const param = new FormData()
      param.append('file', files.file)
      this.$api.general.fileImg(param).then(res => {
        var { data } = res
        this.file = data
      }).catch(() => {
        files.onError()
      })
    },
    phone () {
      this.$api.systemSettings.randmobile({}).then(res => {
        var { data } = res
        this.form.mobile = data
      })
    },
    nameAccount () {
      if (this.id) return
      if (this.form.userName === '') return
      this.$api.systemSettings.buildaccount({ userName: this.form.userName }).then(res => {
        var { data } = res
        this.form.account = data
      })
    },
    submitForm () {
      if (!this.form.userName) {
        this.$message({
          message: '请输入姓名!',
          type: 'warning'
        })
        return
      }
      if (!this.form.account) {
        this.$message({
          message: '请输入账号!',
          type: 'warning'
        })
        return
      }
      if (!this.form.mobile) {
        this.$message({
          message: '请输入手机号!',
          type: 'warning'
        })
        return
      }
      var url = '/member/add'
      if (this.id) {
        url = '/member/edit'
      }
      this.$api.general.generalAdd(url, {
        empty: '1', // 置空过滤标识
        id: this.id,
        memberType: this.memberType,
        headImg: this.file.shortName,
        userName: this.form.userName,
        mobile: this.form.mobile,
        account: this.form.account,
        userNumber: this.form.userNumber,
        oldName: this.form.oldName,
        sex: this.form.sex,
        nation: this.form.nation,
        birthday: this.form.birthday,
        professional: this.form.professional,
        belectedType: this.form.belectedType,
        introduction: this.form.introduction,
        honorInfo: this.form.honorInfo,
        contribute: this.form.contribute,
        remarks: this.form.remarks,
        isRoutineMember: this.form.isRoutineMember,
        nativePlace: this.form.nativePlace,
        party: this.form.party,
        representerElement: this.form.representerElement,
        position: this.form.position,
        education: this.form.education,
        degree: this.form.degree,
        health: this.form.health,
        officePhone: this.form.officePhone,
        postcode: this.form.postcode,
        school: this.form.school,
        callAddress: this.form.callAddress,
        representerTeam: this.form.representerTeam,
        isUsing: this.form.isUsing,
        isReceiveMsg: this.form.isReceiveMsg,
        isJoinApp: this.form.isJoinApp
      }).then(res => {
        var { errcode, errmsg } = res
        if (errcode === 200) {
          this.$message({
            message: errmsg,
            type: 'success'
          })
          if (this.id) {
            this.tabDel(this.id, this.toId)
          } else {
            this.refresh()
          }
        }
      })
    },
    resetForm () {
      if (this.id) {
        this.tabDel(this.id, this.toId)
      } else {
        this.refresh()
      }
    }
  }
}
</script>
<style lang="scss">
.behalfNew {
  width: 1020px;
  background: #ffffff;
  border-radius: 10px;
  margin: 30px auto;
  padding: 0 52px;
  background: linear-gradient(179deg, #ffffff 0%, #fdfdfe 100%);
  box-shadow: 0px 3px 15px rgba(0, 0, 0, 0.16);
  .behalfNewTitle {
    font-size: 36px;
    font-family: SimSun;
    font-weight: bold;
    color: #ff0000;
    line-height: 118px;
    border-bottom: 4px solid #f00;
    text-align: center;
    margin-bottom: 58px;
    box-sizing: border-box;
    padding-top: 12px;
  }
  .behalfNewForm {
    box-shadow: 1px 0px 0px 0px #ebebeb, -1px 0px 0px 0px #ebebeb,
      0px 0px 0px 0px #ebebeb, 0px -1px 0px 0px #ebebeb;
    .behalfNewPortrait {
      width: 130px;
      height: 159px;
      background: #ffffff;
      transform: translateY(0.1px);
      float: right;
      box-shadow: 0px 0px 0px 0px #ebebeb, -1px 0px 0px 0px #ebebeb,
        0px 1px 0px 0px #ebebeb, 0px 0px 0px 0px #ebebeb;

      .memberUploader {
        height: 100%;
        width: 100%;

        .memberUploader-icon {
          color: #8c939d;
          width: 130px;
          height: 159px;
          background: url("../../../../assets/img/userqd.png") no-repeat;
          background-size: 84px 90px;
          background-position: center;
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 500;
          color: #999999;
          line-height: 159px;
        }

        .memberUploader-img {
          width: 130px;
          height: 159px;
          display: flex;
          justify-content: center;
          overflow: hidden;
          img {
            height: 100%;
            display: inline-block;
          }
        }
      }
    }
    .behalfNewFormItem {
      display: inline-block;
      box-shadow: 0px 1px 0px 0px #ebebeb;
      overflow: hidden;
      .behalfNewLabel {
        width: 160px;
        height: 52px;
        height: 100%;
        line-height: 52px;
        text-align: center;
        float: left;
        background: #f5f7fb;
        color: #333333;
        font-family: PingFang SC;
        font-weight: 500;
        span {
          color: #ff0000;
        }
      }
      .behalfNewcontent {
        width: 233px;
        height: 52px;
        margin-left: 160px;
        background: #ffffff;
        padding: 0 2px;
        .el-input__inner {
          border: 0;
          height: 50px;
        }
        .el-date-editor.el-input,
        .el-date-editor.el-input__inner {
          width: 100%;
        }
        .el-select {
          width: 100%;
        }
        .el-radio-group {
          width: 100%;
          height: 100%;
          padding-left: 16px;
          display: inline-flex;
          align-items: center;
        }
      }
      .fill {
        width: 363px;
      }
      .all {
        width: 756px;
      }
    }
    .textarea {
      .behalfNewLabel {
        height: 75px;
        line-height: 75px;
      }
      .behalfNewcontent {
        height: 75px;
      }
    }
    .points {
      .behalfNewLabel {
        width: 160px;
      }
      .behalfNewcontent {
        width: 363px;
        margin-left: 160px;
      }
    }
    .special {
      .behalfNewLabel {
        width: 280px;
      }
      .behalfNewcontent {
        width: 140px;
        margin-left: 280px;
      }
    }
  }
  .behalfNewButton {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 96px;
    .el-button {
      width: 90px;
      padding: 0;
      height: 36px;
      line-height: 36px;
    }
  }
}
</style>
