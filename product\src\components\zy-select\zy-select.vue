<template>
  <div class="zy-tree-select"
       ref="zyTreeSelect">
    <el-popover :width="w"
                placement="bottom"
                trigger="manual"
                v-model="visible"
                popper-class="zy-tree-select-popover">
      <template slot="reference">
        <el-input :placeholder="inputvalue"
                  :readonly="!filterable"
                  @mouseover.native="mouseover"
                  @mouseleave.native="mouseleave"
                  :disabled="disabled"
                  v-model="input"
                  @focus="focus"
                  @blur="blur"
                  ref="input">
          <template slot="suffix">
            <i v-if="show"
               :class="['zy-tree-select-icon','el-icon-arrow-down',visible?'el-icon-arrow-down-a':'']"></i>
            <i v-if="!show"
               @click="empty"
               class="el-icon-circle-close"></i>
          </template>
        </el-input>
      </template>
      <el-scrollbar class="zy-tree-select-body"
                    :style="{height: h}">
        <div class="select-body"
             ref="selectBody"
             @mousedown="mousedown">
          <el-tree ref="tree"
                   :data="data"
                   :props="props"
                   highlight-current
                   :node-key="nodeKey"
                   @node-expand="switchClick"
                   @node-collapse="switchClick"
                   @node-click="handleNodeClick"
                   :filter-node-method="filterNode"
                   :expand-on-click-node="false"></el-tree>
        </div>
      </el-scrollbar>
    </el-popover>
  </div>
</template>

<script>
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
const delay = (function () {
  let timer = 0
  return function (callback, ms) {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
export default {
  name: 'zySelect',
  data () {
    return {
      h: 'auto',
      w: 0,
      visible: false,
      i: 0,
      show: true,
      input: '',
      inputvalue: '',
      index: 0,
      isShow: true
    }
  },
  props: {
    value: [String, Number, Array, Object],
    data: {
      type: Array,
      default: () => []
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label'
        }
      }
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否开启关键字搜索
    filterable: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择内容'
    },
    // 是否可以清空
    clearable: {
      type: Boolean,
      default: true
    },
    // 是否可以清空
    wh: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    visible (val) {
      if (val) {
        this.w = this.$refs.zyTreeSelect.offsetWidth
        this.$nextTick(() => {
          const that = this
          erd.listenTo(this.$refs.selectBody, (element) => {
            that.$nextTick(() => {
              if (element.offsetHeight > 260) {
                that.h = '260px'
              } else {
                that.h = element.offsetHeight + 'px'
                // that.h = 'auto'
              }
            })
          })
        })
        this.$refs.tree.filter(val)
      } else {
        erd.uninstall(this.$refs.selectBody)
      }
    },
    value (val) {
      if (val) {
        this.selectedMethods(this.data)
      } else {
        this.input = ''
        this.inputvalue = this.placeholder
        this.$nextTick(function () {
          this.$refs.tree.setCurrentKey()
        })
      }
    },
    data () {
      this.selectedMethods(this.data)
    },
    input (val) {
      if (this.index === 1 && this.wh) {
        this.index = 0
        return true
      } else {
        // if (!this.filterable) return
        // this.$refs.tree.filter(val)
      }
    }
  },
  mounted () {
    this.index = 1
    this.selectedMethods(this.data)
    this.inputvalue = this.placeholder
  },
  methods: {
    mousedown (e) {
      if (this.isShow) {
        e.preventDefault()
      }
    },
    focus () {
      delay(() => {
        this.visible = true
        if (this.filterable && this.value && this.i === 0) {
          this.inputvalue = this.input
          this.input = ''
        }
      }, 200)
    },
    blur () {
      delay(() => {
        this.visible = false
        if (this.filterable && this.i !== 1) {
          if (this.value && this.inputvalue !== this.placeholder) {
            this.input = this.inputvalue
          }
          this.inputvalue = this.placeholder
        }
        this.i = 0
      }, 200)
    },
    switchClick () {
      delay(() => {
        this.visible = true
      }, 200)
      if (this.filterable) {
        this.i = 2
      }
    },
    filterNode (value, data) {
      if (!this.filterable) return true
      if (!value) return true
      return data[this.props.label].indexOf(value) !== -1
    },
    handleNodeClick (data) {
      if (this.filterable) {
        this.i = 1
      }
      this.$emit('input', data[this.nodeKey])
      this.blurShow = false
      this.$refs.input.blur()
      this.blurShow = true
    },
    // 首次进来默认选中
    selectedMethods (data) {
      data.forEach(item => {
        if (item[this.nodeKey] === this.value) {
          this.input = item[this.props.label]
          this.$emit('select', item)
          this.$nextTick(function () {
            this.$refs.tree.setCurrentKey(item[this.nodeKey])
          })
        }
        if (item.children && item.children.length > 0) {
          this.selectedMethods(item.children)
        }
      })
    },
    empty () {
      this.i = 1
      this.$emit('input', '')
    },
    mouseover () {
      if (this.value && this.clearable) {
        this.show = false
      }
    },
    mouseleave () {
      this.show = true
    }
  }
}
</script>

<style lang="scss">
@import "./zy-tree-select.scss";
</style>
