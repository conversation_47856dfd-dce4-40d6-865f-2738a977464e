<template>
  <div class="recommendedReading">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="isIssue"
                   filterable
                   clearable
                   placeholder="请选择是否发布">
          <el-option v-for="item in isIssueData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button @click="newData"
                   type="primary">创建推荐</el-button>
        <!-- <div class="recommendedReadingText">温馨提示：为确保较好的使用体验，一个主题推荐建议关联最多6本书籍</div> -->
      </template>
    </xyl-search-button>
    <el-scrollbar class="recommendedReadingBox"
                  wrap-class="scrollbar-wrapper">
      <div class="recommendedReadingList"
           v-infinite-scroll="load"
           infinite-scroll-distance="52"
           infinite-scroll-delay="520">
        <div class="recommendedReadingListBox">
          <div class="recommendedReadingItem"
               v-for="(item) in tableData"
               :key="item.id">
            <div class="recommendedReadingItemHead">
              <div class="recommendedReadingItemTitle">{{item.mainTitle}} <span v-if="item.books">已关联{{item.books.length}}本书</span></div>
              <div class="recommendedReadingItemOperation">
                <el-select v-model="item.count"
                           @change="change(item)"
                           placeholder="请选择">
                  <el-option v-for="item in countData"
                             :key="item.id"
                             :label="item.value"
                             :value="item.id">
                  </el-option>
                </el-select>
                <el-button type="primary"
                           @click="release(item)">{{item.isIssue==1?'撤下发布':'一键发布'}}</el-button>
                <el-button type="primary"
                           @click="Modify(item)">修改</el-button>
                <el-button type="primary"
                           @click="bookDel(item)">删除</el-button>
                <!-- <div class="recommendedReadingsort">{{item.sort==1?'已置顶显示':`排序：${item.sort}`}}</div> -->
              </div>
            </div>
            <zy-sliding-box>
              <div class="RelatedBooksList">
                <div class="RelatedBooksItem"
                     v-for="(items) in item.books"
                     :key="items.bookId">
                  <div class="operation">
                    <i class="el-icon-zoom-in"
                       @click="bookdetails(items)"></i>
                    <i class="el-icon-delete"
                       @click="deleteBookClick(item.id,items)"></i>
                  </div>
                  <div class="RelatedBooksItemImg">
                    <img :src="items.coverImgUrl"
                         alt="">
                  </div>
                  <div class="RelatedBooksItemName">{{items.bookName}}</div>
                  <div class="RelatedBooksItemAuthor">{{items.authorName}}</div>
                  <el-input v-model="items.recommendWord"
                            @change="changeInput(item.id,items.bookId,items.recommendWord)"
                            placeholder="请输入推荐词"></el-input>
                </div>
                <div class="associated"
                     @click="associated(item)"> <i class="el-icon-plus associatedIcon"></i></div>
              </div>
            </zy-sliding-box>
          </div>
        </div>
        <div class="recommendedReadingTetx"
             v-if="infinite">正在加载中...</div>
        <div class="recommendedReadingTetx"
             v-else>没有更多了</div>
      </div>
    </el-scrollbar>
    <xyl-popup-window v-model="show"
                      title="选择书籍">
      <recommendedReadingNew :id="id"
                             @newCallback="callback"></recommendedReadingNew>
    </xyl-popup-window>
    <xyl-popup-window v-model="bookShow"
                      title="选择书籍">
      <RelatedBooks :data="book"
                    @callback="addcallback"></RelatedBooks>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      title="书籍详情">
      <libraryDetails :id="id"> </libraryDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import RelatedBooks from '../RelatedBooks/RelatedBooks'
import recommendedReadingNew from './recommendedReadingNew'
import libraryDetails from '../library/libraryDetails'
export default {
  name: 'recommendedReading',
  data () {
    return {
      keyword: '',
      isIssue: '',
      isIssueData: [
        { id: '1', value: '是' },
        { id: '0', value: '否' }
      ],
      infinite: false,
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      countData: [],
      value: '',
      id: '',
      show: false,
      bookShow: false,
      book: [],
      detailsShow: false
    }
  },
  components: {
    RelatedBooks,
    recommendedReadingNew,
    libraryDetails
  },
  mounted () {
    this.dictionaryPubkvs()
    this.sySuggestedReadingList()
  },
  methods: {
    search () {
      this.page = 1
      this.pageSize = 10
      this.tableData = []
      this.sySuggestedReadingList()
    },
    reset () {
      this.keyword = ''
      this.isIssue = ''
      this.page = 1
      this.pageSize = 10
      this.tableData = []
      this.sySuggestedReadingList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    bookdetails (row) {
      this.id = row.bookId
      this.detailsShow = true
    },
    deleteBookClick (id, row) {
      this.$confirm('此操作将删除该推荐关联的书籍, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.removebook(id, row.bookId)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async removebook (id, keyId) {
      const res = await this.$api.academy.removebook({
        id: id,
        bookIds: keyId
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.deleteBookData(id, keyId)
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    deleteBookData (id, keyId) {
      var tableData = this.tableData
      tableData.forEach(item => {
        if (item.id === id) {
          item.books = item.books.filter(tab => tab.bookId !== keyId)
        }
      })
      this.tableData = tableData
    },
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'sy_suggestedreading_displaytype'
      })
      var { data } = res
      this.countData = data.sy_suggestedreading_displaytype
    },
    change (value) {
      this.changecount(value.id, value.count)
    },
    async changecount (id, count) {
      const res = await this.$api.academy.changecount({
        id: id,
        count: count
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    changeInput (id, bookId, value) {
      this.addWord({ recommendId: id, bookId: bookId, recommendWord: value })
    },
    async addWord (data) {
      const res = await this.$api.academy.addWord(data)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    Modify (row) {
      this.id = row.id
      this.show = true
    },
    associated (row) {
      this.id = row.id
      if (row.books) {
        row.books.forEach(item => {
          item.id = item.bookId
        })
        this.book = row.books
      } else {
        this.book = []
      }
      this.bookShow = true
    },
    addcallback (data, type) {
      if (type) {
        if (data.length) {
          var arr = []
          data.forEach(item => {
            arr.push(item.id)
          })
          this.relevanceBook(arr.join(','))
        }
      } else {
        this.book = data
      }
      this.bookShow = false
    },
    async relevanceBook (id) {
      const res = await this.$api.academy.relevanceBook({
        id: this.id,
        bookIds: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.search()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    callback () {
      this.show = false
      this.search()
    },
    async sySuggestedReadingList () {
      const res = await this.$api.academy.sySuggestedReadingList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        isIssue: this.isIssue
      })
      var { data, total } = res
      data.forEach(item => {
        item.count = item.count + ''
      })
      this.tableData = this.tableData.concat(data)
      this.total = total
      this.infinite = false
    },
    load () {
      if (this.tableData.length < this.total) {
        this.infinite = true
        this.page = this.page + 1
        this.sySuggestedReadingList()
      }
    },
    release (row) {
      if (row.isIssue === 1) {
        this.sySuggestedReadingUnpublish(row.id)
      } else {
        this.sySuggestedReadingIssue(row.id)
      }
    },
    async sySuggestedReadingIssue (id) {
      const res = await this.$api.academy.sySuggestedReadingIssue({
        id: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.isIssueYN(id, 1)
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async sySuggestedReadingUnpublish (id) {
      const res = await this.$api.academy.sySuggestedReadingUnpublish({
        id: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.isIssueYN(id, 0)
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    isIssueYN (id, type) {
      var tableData = this.tableData
      tableData.forEach(item => {
        if (item.id === id) {
          item.isIssue = type
        }
      })
      this.tableData = tableData
    },
    bookDel (row) {
      this.$confirm('此操作将删除该推荐, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.sySuggestedReadingDels(row.id)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async sySuggestedReadingDels (id) {
      const res = await this.$api.academy.sySuggestedReadingDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.search()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.recommendedReading {
  width: 100%;
  height: 100%;
  .recommendedReadingText {
    display: inline-block;
    height: 40px;
    line-height: 40px;
    color: #e6a23c;
    font-size: 14px;
  }
  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }
  .recommendedReadingBox {
    width: 100%;
    height: calc(100% - 64px);

    .el-scrollbar__wrap {
      overflow-x: hidden;
    }

    .is-vertical {
      .el-scrollbar__thumb {
        background-color: rgba(144, 147, 153, 0.8);
      }
    }

    .recommendedReadingTetx {
      height: 52px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ccc;
      border-top: 1px solid #ededed;
      cursor: pointer;
    }

    .recommendedReadingList {
      padding: 0 24px;
      padding-right: 12px;
      .recommendedReadingListBox {
        display: flex;
        flex-wrap: wrap;
        .zy-sliding-box {
          height: 380px;
        }
        .recommendedReadingItem {
          overflow: hidden;
          width: 100%;
          height: 380px;
          margin-bottom: 12px;
          border: 1px solid #e6e6e6;
          padding: 0 12px;
          .recommendedReadingItemHead {
            height: 52px;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .recommendedReadingItemTitle {
              font-weight: 600;
              line-height: 52px;
              font-size: 18px;
              span {
                color: $zy-color;
                margin-left: 22px;
                font-size: 16px;
                font-weight: normal;
              }
            }
            .recommendedReadingItemOperation {
              display: flex;
              align-items: center;
              .el-input__inner {
                height: 33px;
                line-height: 33px;
              }
              .el-input__icon {
                line-height: 33px;
              }
              .el-button {
                padding: 0 12px;
                height: 32px;
                margin-left: 12px;
              }
              .recommendedReadingsort {
                padding: 0 12px;
                height: 32px;
                line-height: 32px;
                margin-left: 12px;
                color: #fff;
                background-color: $zy-color;
                border-radius: 5px;
              }
            }
          }
          .RelatedBooksList {
            display: flex;
            padding-top: 12px;
            .RelatedBooksItem {
              width: 128px;
              height: 280px;
              margin-right: 12px;
              cursor: pointer;
              position: relative;
              .operation {
                position: absolute;
                display: none;
                width: 100%;
                height: 170px;
                background-color: rgba(0, 0, 0, 0.5);
                color: #fff;
                text-align: center;
                line-height: 170px;
                font-size: 22px;
                .el-icon-delete {
                  margin-left: 26px;
                }
              }
              &:hover {
                .operation {
                  display: block;
                }
              }
              .RelatedBooksItemImg {
                height: 170px;
                width: 128px;
                margin: auto;
                img {
                  height: 100%;
                  width: 100%;
                }
              }
              .RelatedBooksItemName {
                margin: 6px 0;
                font-size: 16px;
                font-weight: 600;
                line-height: 24px;
                height: 52px;
                white-space: normal !important;
                overflow: hidden;
                max-height: 48px;
              }
              .RelatedBooksItemAuthor {
                font-size: 13px;
                color: #999;
                letter-spacing: 1px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
                margin-bottom: 12px;
              }
            }
            .associated {
              height: 170px;
              width: 128px;
              border: 1px dashed #8c939d;
              cursor: pointer;
              &:hover {
                border-color: $zy-color;
              }
              .associatedIcon {
                font-size: 28px;
                color: #8c939d;
                width: 128px;
                height: 170px;
                line-height: 170px;
                text-align: center;
              }
            }
          }
        }
      }
    }
  }
}
</style>
