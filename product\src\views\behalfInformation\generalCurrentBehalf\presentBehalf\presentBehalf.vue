<template>
  <div class="presentBehalf">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="出缺类型"
                    class="form-input"
                    prop="vacantType">
        <el-select v-model="form.vacantType"
                   filterable
                   clearable
                   placeholder="请选择出缺类型">
          <el-option v-for="item in vacantType"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出缺备注"
                    class="form-title">
        <el-input type="textarea"
                  :rows="5"
                  placeholder="请输入出缺备注"
                  v-model="form.vacantRemarks">
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'presentBehalf',
  data () {
    return {
      form: {
        vacantType: '',
        vacantRemarks: ''
      },
      rules: {
        vacantType: [
          { required: true, message: '请选择出缺类型', trigger: 'change' }
        ]
      },
      vacantType: []
    }
  },
  props: ['id', 'memberType'],
  mounted () {
    this.dictionaryPubkvs()
  },
  methods: {
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'vacant_type,npc_vacant_type'
      })
      var { data } = res
      this.vacantType = this.$isMoreProject() ? data.npc_vacant_type : data.vacant_type
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.batchOut()
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    async batchOut () {
      const res = await this.$api.memberInformation.batchOut({
        memberType: this.memberType,
        vacantType: this.form.vacantType,
        vacantRemarks: this.form.vacantRemarks,
        ids: this.id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$emit('newCallback')
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.presentBehalf {
  width: 682px;
}
</style>
