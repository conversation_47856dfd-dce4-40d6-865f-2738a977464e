.xyl-search-button {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  .xyl-search-box {
    width: 62%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
    padding-right: 12px;
    .search-area-button {
      display: flex;
      align-items: center;
      height: 64px;
      .el-button--primary {
        margin-right: 0px;
      }
    }
    .el-date-editor {
      margin-right: 20px;
    }
    .el-input {
      width: 222px;
      margin-right: 20px;
      margin-bottom: 0;
    }
    .zy-tree-select {
      width: 222px;
      margin-right: 20px;
      .el-input {
        width: 222px;
        margin-right: 0;
      }
    }
  }
  .xyl-button-box {
    width: 38%;
    display: flex;
    align-items: center;
    height: 64px;
    padding-left: 12px;
    .xyl-button-more {
      padding: 0 20px;
      font-size: 38px;
      height: 40px;
      margin-left: 12px;
      span {
        display: block;
        height: 40px;
        line-height: 20px;
        .point {
          display: inline-block;
          width: 5px;
          height: 5px;
          background: #666666;
          border-radius: 50%;
        }
        .point + .point {
          margin-left: 5px;
        }
      }
    }
    .buttonShowBox {
      display: none;
    }
  }
  .xyl-search-completely {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    padding: 12px;
    padding-bottom: 0;
    border-top: 1px solid #eeeeee;
    .xyl-search-completely-input {
      width: calc(100% - 150px);
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
      .el-input {
        width: 222px;
        margin-right: 20px;
        margin-bottom: 12px;
      }
      .el-select {
        width: 222px;
        margin-right: 20px;
        margin-bottom: 12px;
        .el-input {
          width: 222px;
          margin-right: 0;
          margin-bottom: 0;
        }
      }
      .el-date-editor {
        margin-right: 20px;
        margin-bottom: 12px;
      }
      .zy-tree-select {
        width: 222px;
        margin-right: 20px;
        margin-bottom: 12px;
        .el-input {
          width: 222px;
          margin-right: 0;
          margin-bottom: 0;
        }
      }
      .el-date-editor {
        margin-right: 20px;
        margin-bottom: 12px;
      }
      .screening-checkbox {
        display: flex;
        align-items: center;
        height: 40px;
        margin-bottom: 12px;
        margin-right: 20px;
      }
      .urban-linkage {
        margin-right: 20px;
      }
    }
    .xyl-search-completely-button {
      width: 150px;
    }
    .xyl-search-completely-button-width {
      width: 70px;
      .el-button {
        margin: 0;
        margin-bottom: 12px;
      }
    }
  }
}
.xyl-search-content {
  padding: 12px 0 0 12px !important;
  margin: 0 !important;
  // margin-right: 12px !important;
  display: flex;
  flex-wrap: wrap;
  .el-input {
    width: 222px;
    margin-right: 20px;
    margin-bottom: 12px;
  }
  .el-select {
    width: 222px;
    margin-right: 20px;
    margin-bottom: 12px;
    .el-input {
      width: 222px;
      margin-right: 0;
      margin-bottom: 0;
    }
  }
  .zy-tree-select {
    width: 222px;
    margin-right: 20px;
    margin-bottom: 12px;
    .el-input {
      width: 222px;
      margin-right: 0;
      margin-bottom: 0;
    }
  }
  .el-date-editor {
    margin-right: 20px;
    margin-bottom: 12px;
  }
  .screening-checkbox {
    // width: 222px;
    display: flex;
    align-items: center;
    height: 40px;
    margin-bottom: 12px;
    margin-right: 20px;
  }
  .urban-linkage {
    margin-right: 20px;
  }
  .xyl-search-content-button {
    display: inline-block;
    margin-bottom: 12px;
    margin-right: 20px;
    width: 222px;
  }
}
.xyl-button-content {
  padding: 0 !important;
  margin: 0 !important;
  min-width: 88px;
  .el-button + .el-button {
    margin-left: 0;
  }
  .el-button {
    display: block;
    width: 100%;
    padding: 0;
    height: 40px;
    line-height: 40px;
    border-radius: 0px;
    background: #ffffff;
    border: 1px solid #dcdfe6;
    color: #606266;
    border-color: #dcdfe6;
    &:hover {
      color: $zy-color;
      border-color: rgba($color: $zy-color, $alpha: 0.22);
      background-color: rgba($color: $zy-color, $alpha: 0.1);
    }
    &:focus {
      color: $zy-color;
      border-color: rgba($color: $zy-color, $alpha: 0.22);
      background-color: rgba($color: $zy-color, $alpha: 0.1);
    }
  }
}
