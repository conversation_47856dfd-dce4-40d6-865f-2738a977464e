<template>
  <div class="activity-mine">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :buttonNumber="2">
      <template slot="search">
        <el-input v-model="searchParams.keyword"
                  placeholder="请输入关键字"
                  clearable
                  @keyup.enter.native="search"></el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="handleSignIn">签到</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  stripe
                  border
                  ref="table"
                  slot="zytable">
          <!-- <el-table-column type="selection" fixed="left" width="60"></el-table-column> -->
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="活动主题"
                           prop="meetName"
                           min-width="240"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="组织部门"
                           prop="organizer"
                           width="120"></el-table-column>
          <el-table-column label="活动类型"
                           prop="meetType"
                           width="120"></el-table-column>
          <el-table-column label="活动时间"
                           min-width="360">
            <template slot-scope="scope">{{scope.row.meetStartTime}}至{{scope.row.meetEndTime}}</template>
          </el-table-column>
          <el-table-column label="创建人"
                           prop="createName"
                           min-width="180"></el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           min-width="120">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="handleLeave(scope.row.id)">请假</el-button>
              <el-button type="text"
                         @click="handleSignUp(scope.row.id)">报名</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total"></el-pagination>
    </div>
    <zy-pop-up v-model="isSign"
               title="请假">
      <leave :info="info"
             :aid="id"
             :status="0"
             @callback="handleCallback"></leave>
    </zy-pop-up>
  </div>
</template>

<script>
import table from '@/mixins/table.js'
import { filterParams, checkParams } from '@/common/handleParams'
import leave from '../attendance/widget/leave'
export default {
  mixins: [table],
  components: { leave },
  data () {
    return {
      searchParams: {
        keyword: ''
      },
      info: {},
      isSign: false,
      id: null
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize
      }
      params = Object.assign(params, filterParams(this.searchParams))
      this.$api.activityqd.myList(params).then(res => {
        var { data, total } = res
        this.list = data
        this.total = total
      })
    },
    search () {
      if (!checkParams(this.searchParams)) {
        return this.$message.warning('请输入关键字')
      }
      this.getList()
    },
    reset () {
      this.searchParams = {
        keyword: ''
      }
      this.getList()
    },
    handleSignUp (id) {
      this.$confirm('此操作将报名选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activityqd.mySignUp({ activityId: id }).then((res) => {
          this.$message.success(res.errmsg)
          if (res.errcode === 200) {
            this.getList()
          }
        })
      }).catch(() => {
        this.$message.info('取消报名')
        return false
      })
    },
    handleSignIn () {
      this.$alert('请到现场使用青岛智慧政协APP扫码签到', '提示', {
        cancelButtonText: '取消'
      })
    },
    handleLeave (id) {
      this.id = id
      const user = JSON.parse(sessionStorage.getItem('user' + this.$logo()))
      this.info = {
        userName: user.userName,
        userId: user.id
      }
      this.isSign = true
    },
    handleCallback () {
      this.isSign = false
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.activity-mine {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
