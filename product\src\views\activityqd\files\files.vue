<template>
  <div class="activity-file-list">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input v-model="searchParams.keyword"
                  placeholder="请输入关键字"
                  clearable
                  @keyup.enter.native="search"></el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="isAdd = true">新增</el-button>
        <el-button type="primary"
                   @click="handleBatchDelete">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  stripe
                  border
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="材料名称"
                           min-width="240"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <div v-for="item in scope.row.attachmentList"
                   :key="item.id">
                <el-button type="text"
                           @click="downloadFile(item)">{{
                    item.fileName
                  }}</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="材料格式"
                           prop="format"
                           width="180"></el-table-column>
          <el-table-column label="创建人"
                           prop="createName"
                           min-width="180"></el-table-column>
          <el-table-column label="发布时间"
                           prop="date"
                           min-width="180"></el-table-column>
          <el-table-column label="是否公开"
                           width="120">
            <template slot-scope="scope">
              {{ scope.row.isAppShow === 1 ? "公开" : "不公开" }}
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 50, 80, 100, 200, 500]"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total"></el-pagination>
    </div>
    <zy-pop-up v-model="isAdd"
               title="添加材料">
      <add-file @callback="handleCallback"></add-file>
    </zy-pop-up>
  </div>
</template>

<script>
import AddFile from '../add/widget/widget/add-file'
import table from '@/mixins/table.js'
import { filterParams, checkParams } from '@/common/handleParams'
export default {
  components: { AddFile },
  mixins: [table],
  data () {
    return {
      searchParams: { keyword: '' },
      isAdd: false,
      id: null
    }
  },
  created () {
    this.id = this.$route.query.id
    this.getList()
  },
  methods: {
    getList () {
      let params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        meetId: this.id
      }
      params = Object.assign(params, filterParams(this.searchParams))
      this.$api.activityqd.material(params).then(res => {
        var { data, total } = res
        this.list = data
        this.total = total
      })
    },
    search () {
      if (!checkParams(this.searchParams)) {
        return this.$message.warning('请输入关键字')
      }
      this.getList()
    },
    reset () {
      this.searchParams = {
        keyword: ''
      }
      this.getList()
    },
    handleBatchDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.handleDelete(this.selectionList.map((v) => v.id).join(','))
    },
    handleDelete (ids) {
      this.$confirm('此操作将删除选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.activityqd.materialDels(ids).then((res) => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除成功')
          }
        })
      }).catch(() => {
        this.$message.info('取消删除')
        return false
      })
    },
    handleCallback (val) {
      if (!val) {
        this.isAdd = false
        return
      }
      if (val.length > 0) {
        const data = {
          activityId: this.id,
          isAppShow: val[0].isAppShow,
          attachmentIds: val.map(v => v.fileId).join(',')
        }
        this.$api.activityqd.materialAdd(data).then(res => {
          this.$message.success('新增活动材料成功')
          this.getList()
        })
      }
      this.isAdd = false
    },
    downloadFile (item) {
      this.$api.proposal.downloadFile({ id: item.id }, item.fileName)
    }
  }
}
</script>
<style lang="scss">
.activity-file-list {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
