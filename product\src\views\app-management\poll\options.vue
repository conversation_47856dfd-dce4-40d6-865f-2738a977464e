<template>
  <div class="poll-options">
    <screening-box @search-click="search"
                   @reset-click="reset">
      <el-input v-model="keyword"
                placeholder="请输入内容"></el-input>
    </screening-box>
    <div class="button-box">
      <el-button type="primary"
                 @click="handleAdd">新增</el-button>
      <el-button type="primary"
                 @click="handleBatchDelete">删除</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="选项"
                           min-width="260"
                           prop="name"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="选项说明"
                           width="400"
                           prop="instructions"></el-table-column>
          <el-table-column label="附件"
                           min-width="120">
            <template slot-scope="scope">
              <el-button v-if="scope.row.attachmentList.length>0"
                         type="text"
                         @click="downloadFile(scope.row.attachmentList)">附件</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           min-width="520">
            <template slot-scope="scope">
              <el-button @click="handleEdit(scope.row.id)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
              <el-button @click="handleDelete(scope.row.id)"
                         type="primary"
                         plain
                         size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="isAdd"
               :title="addTitle">
      <poll-options-Add :id="id"
                        :pollId="pollId"
                        @callback="handleCallBack"></poll-options-Add>
    </zy-pop-up>
  </div>
</template>

<script>
import table from '@/mixins/table.js'
import pollOptionsAdd from './widget/options-add.vue'
export default {
  mixins: [table],
  components: { pollOptionsAdd },
  data () {
    return {
      keyword: '',
      isAdd: false,
      addTitle: '新增',
      id: null,
      pollId: null
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      const params = {
        pageNo: this.page,
        pageSize: this.pageSize,
        voteId: this.$route.query.id
      }
      if (this.keyword !== '') {
        params.keyword = this.keyword
      }
      this.$api.appManagement.pollOptionsList(params).then(res => {
        const { errcode, total, data } = res
        if (errcode === 200) {
          this.total = total
          this.list = data
        }
      })
    },
    search () {
      if (this.keyword === '') {
        return this.$message.warning('请输入内容')
      }
      this.page = 1
      this.getList()
    },
    reset () {
      this.keyword = ''
      this.getList()
    },
    handleAdd () {
      this.addTitle = '新增投票选项'
      this.id = null
      this.pollId = this.$route.query.id
      this.isAdd = true
    },
    handleEdit (id) {
      this.addTitle = '编辑投票选项'
      this.id = id
      this.pollId = this.$route.query.id
      this.isAdd = true
    },
    handleDelete (ids) {
      this.$confirm('此操作将删除选中的投票选项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$api.appManagement.pollOptionsDels(ids).then(res => {
          if (res.errcode === 200) {
            this.getList()
            this.$message.success('删除投票选项成功')
          }
        })
      }).catch(() => {
        return false
      })
    },
    handleBatchDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要删除的项')
      }
      const ids = this.selectionList.map(item => item.id).join(',')
      this.handleDelete(ids)
    },
    downloadFile (files) {
      files.map(file => window.open(file.filePath))
    },
    handleCallBack () {
      this.isAdd = false
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.poll-options {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 180px);
    width: 100%;
  }
}
</style>
