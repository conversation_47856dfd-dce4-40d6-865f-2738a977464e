export default {
  name: 'XylTabItem',
  props: ['value'],
  render () {
    const extension = (<el-popover width="109" ref="tabPopover" on-show={() => { this.show = true }} on-hide={() => { this.show = false }} transition="el-zoom-in-top" placement="bottom-start" visible-arrow={false} popper-class="xyl-tab-popover">
      <div slot="reference" on-click={(e) => { e.stopPropagation() }} class={['tab-popover-icon', this.show ? 'tab-popover-icon-a' : '']}></div>
      <div class="xyl-tab-extension">
        {this.$parent.value === this.value ? (<div class="xyl-tab-extension-item" on-click={this.refresh}>刷新</div>) : null}
        {this.$parent.panes.length !== 1 ? (<div class="xyl-tab-extension-item" on-click={this.close}>关闭</div>) : null}
        {this.$parent.panes.length !== 1 ? (<div class="xyl-tab-extension-item" on-click={this.closeOther}>关闭其他</div>) : null}
      </div>
    </el-popover>)
    return (<div class={['xyl-tab-item', { 'is-active': this.$parent.value === this.value }]} on-click={this.tabClick}><span class="tabSlots">{this.$slots.default}</span>{extension}</div>)
  },
  data () {
    return {
      show: false
    }
  },
  methods: {
    tabClick () {
      this.$parent.$emit('input', this.value)
      this.$parent.$emit('tab-click', this, this.value)
    },
    refresh () {
      this.$parent.$emit('refresh', this.value)
      this.$refs.tabPopover.showPopper = false
    },
    close () {
      this.$parent.$emit('close', this.value)
      this.$refs.tabPopover.showPopper = false
    },
    closeOther () {
      this.$parent.$emit('closeOther', this.value)
      this.$refs.tabPopover.showPopper = false
      this.$parent.obtainActive()
    }
  }
}
