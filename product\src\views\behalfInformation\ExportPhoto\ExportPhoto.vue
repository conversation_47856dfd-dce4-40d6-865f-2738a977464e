<template>
  <div class="ExportPhoto">
    <div class="livingExampleBox">
      <div class="livingExample">
        <div class="livingExampleText">在进行批量导入照片之前，请先做好以下准备工作</div>
        <div class="livingExampleText">1、请确保照片的后缀名为<span>.jpg</span>/<span>.jpeg</span>/<span>.png</span></div>
        <div class="livingExampleText">2、请检查，照片文件大小<span>最大不能超过1M</span></div>
        <div class="livingExampleText">3、请将照片的<span>文件名统一</span>修改为：姓名-手机号码</div>
        <div class="livingExampleText">4、请将准备好的照片文件都统一放在一个压缩包内</div>
      </div>
      <div class="livingExample">
        <div>示例：</div>
        <div class="livingExampleImg">
          <img src="../../../assets/img/zp.png"
               alt="">
        </div>
        <div>张{{memberType=='1'?'委员':'代表'}}-18800001234.jpg</div>
      </div>
    </div>
    <div class="ExportPhotoText">准备完成之后，我们进行导入操作。</div>
    <div class="ExportPhotoText">一、请指定压缩包文件</div>
    <div class="ExportPhotoUpload">
      <el-upload drag
                 action="/"
                 :show-file-list="false"
                 :before-upload="handleFile"
                 :http-request="fileUpload"
                 multiple>
        <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
        <div class="el-upload__tip">仅支持zip格式</div>
      </el-upload>
    </div>
    <div class="ExportPhotoText">二、静待系统上传文件吧！这个过程受网络带宽影响较大，故请您耐心等待，不要关闭此页面。导入完成后，请您下载导入结果查看相关情况。</div>
    <div class="ExportPhotoText"
         v-if="errorShow === null && isShow"><i class="el-icon-loading"></i> 文件正在上传，请您耐心等待，不要关闭此页面。 </div>
    <div class="ExportPhotoTextSuccess"
         v-if="errorShow === false">文件导入完成，如要查看导入结果，请点击下载查看
      <el-button type="primary"
                 @click="importResults">下载导入结果</el-button>
    </div>
    <div class="ExportPhotoTextError"
         v-if="errorShow === true">文件导入失败，请检查网络稍后重新导入</div>
  </div>
</template>
<script>
export default {
  name: 'ExportPhoto',
  data () {
    return {
      blob: null,
      errorShow: null,
      isShow: false
    }
  },
  props: ['memberType'],
  methods: {
    /**
   * 限制上传附件的文件类型
  */
    handleFile (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension = testmsg === 'zip'
      if (!extension) {
        this.$message({
          message: '上传文件只能是zip格式!',
          type: 'warning'
        })
      }
      return extension
    },
    /**
   * 上传附件请求方法
  */
    fileUpload (files) {
      this.isShow = true
      const param = new FormData()
      param.append('file', files.file)
      this.$api.general.uploadimgs(param).then(res => {
        const content = res
        this.blob = new Blob([content])
        this.errorShow = false
      }).catch(() => {
        this.errorShow = true
        files.onError()
      })
    },
    importResults () {
      const blob = this.blob
      const fileName = '导入结果.xlsx'
      if ('download' in document.createElement('a')) { // 非IE下载
        const elink = document.createElement('a')
        elink.download = fileName
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
      } else { // IE10+下载
        navigator.msSaveBlob(blob, fileName)
      }
    }
  }
}
</script>
<style lang="scss">
.ExportPhoto {
  width: 1060px;
  height: 100%;
  padding: 24px;
  .livingExampleBox {
    width: 100%;
    display: flex;
    padding: 24px;
    border: 2px solid $zy-color;
    margin-bottom: 22px;
    background-color: $zy-withColor;
    .livingExample {
      width: 50%;
      .livingExampleText {
        line-height: 36px;
        span {
          font-weight: 600;
        }
      }
      .livingExampleImg {
        width: 125px;
        height: 152px;
        margin: 0 16px;
        img {
          height: 100%;
        }
      }
    }
    .livingExample + .livingExample {
      border-left: 1px dashed #999;
      display: flex;
      align-items: center;
      padding: 0 24px;
    }
  }
  .ExportPhotoText {
    line-height: 36px;
  }
  .ExportPhotoUpload {
    padding: 12px 0;
    padding-left: 32px;
    .el-upload {
      width: 100%;
      .el-upload-dragger {
        height: 82px;
        width: 100%;
        background-color: #e6e5e8;
        .el-upload__text {
          padding-top: 22px;
          font-size: 14px;
          line-height: 22px;
        }
        .el-upload__tip {
          font-size: 12px;
          line-height: 20px;
          color: #6e6e6e;
          margin-top: 0;
        }
      }
    }
  }
  .ExportPhotoTextSuccess {
    padding-top: 22px;
    padding-left: 32px;
    .el-button {
      margin-left: 22px;
      padding: 6px 22px;
    }
  }
}
</style>
