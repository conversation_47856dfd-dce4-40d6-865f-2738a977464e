<template>
  <div class="informationAdd">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="标题"
                    prop="title"
                    class="form-title">
        <el-input placeholder="请输入标题"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <!-- <el-form-item label="副标题"
                    class="form-title">
        <el-input placeholder="请输入副标题"
                  v-model="form.secondTitle"
                  clearable>
        </el-input>
      </el-form-item> -->

      <el-form-item label="所属结构"
                    prop="structureId"
                    class="form-input">
        <zy-select width="296"
                   node-key="id"
                   :props="{children: 'children',label: 'name'}"
                   v-model="form.structureId"
                   :data="structureIdData"
                   placeholder="请选择所属结构"></zy-select>
      </el-form-item>
      <el-form-item label="资讯类型"
                    prop="infoClass"
                    class="form-input">
        <el-select v-model="form.infoClass"
                   filterable
                   clearable
                   placeholder="请选择资讯类型">
          <el-option v-for="item in infoClassData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="显示类型"
                    prop="infoType"
                    class="form-input">
        <el-select v-model="form.infoType"
                   filterable
                   clearable
                   placeholder="请选择显示类型">
          <el-option v-for="item in infoTypeData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="来源"
                    class="form-input">
        <el-input placeholder="请输入来源"
                  v-model="form.source"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="发布时间"
                    prop="publishDate"
                    class="form-input">
        <el-date-picker v-model="form.publishDate"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input">
        <el-input-number v-model="form.sort"
                         placeholder="请输入排序"
                         :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="是否置顶"
                    class="form-input">
        <el-radio-group v-model="form.isTop">
          <el-radio label="1">置顶</el-radio>
          <el-radio label="0">不置顶</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上传原图"
                    class="form-img">
        <el-upload class="form-img-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="fileImg.filePath"
               :src="fileImg.filePath"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="上传附件"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleFile"
                   :http-request="fileUpload"
                   :file-list="file"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="外部链接"
                    v-if="$isAppShow()"
                    class="form-title">
        <el-input placeholder="请输入外部链接"
                  v-model="form.externalLinks"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-ue">
        <UEditor v-model="form.content"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'informationAdd',
  data () {
    return {
      form: {
        title: '',
        secondTitle: '',
        structureId: '',
        source: '',
        infoClass: '',
        infoType: '',
        sort: '',
        publishDate: '',
        externalLinks: '',
        isTop: '0',
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        structureId: [
          { required: true, message: '请选择所属结构', trigger: 'blur' }
        ],
        infoClass: [
          { required: true, message: '请选择资讯类型', trigger: 'blur' }
        ],
        infoType: [
          { required: true, message: '请选择显示类型', trigger: 'blur' }
        ],
        publishDate: [
          { required: true, message: '选择发布时间', trigger: 'blur' }
        ]
      },
      structureIdData: [],
      infoClassData: [],
      infoTypeData: [],
      file: [],
      fileImg: {},
      dataMigration: 0
    }
  },
  props: ['id', 'module', 'parentId'],
  mounted () {
    this.informationColumnTree()
    this.dictionaryPubkvs()
    if (this.id) {
      this.informationListInfo()
    } else {
      this.form.publishDate = this.$format(new Date(), 'YYYY-MM-DD HH:mm:ss')
      if (this.parentId && this.parentId !== '1') {
        this.form.structureId = this.parentId
      }
    }
  },
  methods: {
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'zy_info_type,info_show_type'
      })
      var { data } = res
      this.infoClassData = data.zy_info_type
      this.infoTypeData = data.info_show_type
    },
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: this.module })
      var { data } = res
      this.structureIdData = data
    },
    async informationListInfo () {
      const res = await this.$api.appManagement.informationListInfo(this.id)
      var { data } = res
      this.form.title = data.title
      this.form.secondTitle = data.secondTitle
      this.form.structureId = data.structureId
      this.form.source = data.source
      this.form.infoClass = data.infoClass
      this.form.infoType = data.infoType
      // this.form.sort = data.sort
      this.form.publishDate = data.publishDate
      this.form.externalLinks = data.externalLinks
      this.form.content = data.content
      this.form.isTop = data.isTop
      if (data.sort === null) {
        this.form.sort = undefined
      } else {
        this.form.sort = data.sort
      }
      if (data.image) {
        data.image.filePath = data.image.fullUrl
        this.fileImg = data.image
      }
      if (data.attachmentList) {
        data.attachmentList.forEach((item, index) => {
          item.uid = item.id
          item.name = item.fileName
        })
        this.file = data.attachmentList
      }
      this.dataMigration = data.dataMigration
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.fileImg = data[0]
      }).catch(() => {
        files.onError()
      })
    },
    /**
   * 限制上传附件的文件类型
  */
    handleFile (file, fileList) {
    },
    /**
   * 上传附件请求方法
  */
    fileUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.file.push(data[0])
      }).catch(() => {
        files.onError()
      })
    },
    /**
   * 删除附件
  */
    beforeRemove (file, fileList) {
      var fileData = this.file
      this.file = fileData.filter(item => item.id !== file.id)
    },
    /**
   * 提交提案
  */
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var attach = []
          this.file.forEach(item => {
            attach.push(item.id)
          })
          var url = '/zyinfodetail/add'
          if (this.id) {
            url = '/zyinfodetail/edit'
          }
          this.$api.general.generalAdd(url, {
            empty: '1', // 置空过滤标识
            id: this.id,
            title: this.form.title,
            secondTitle: this.form.secondTitle,
            structureId: this.form.structureId,
            source: this.form.source,
            infoClass: this.form.infoClass,
            infoType: this.form.infoType,
            sort: this.form.sort || '',
            publishDate: this.form.publishDate,
            image: this.fileImg.id,
            attachmentIds: attach.join(','),
            externalLinks: this.form.externalLinks,
            content: this.form.content,
            isTop: this.form.isTop,
            module: this.module
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              if (this.dataMigration == '1') { // eslint-disable-line
                this.$confirm('当前检查到您修改的数据有推送至官网的资讯, 是否同步修改?', '提示', {
                  confirmButtonText: '同步修改',
                  cancelButtonText: '不修改',
                  type: 'warning'
                }).then(() => {
                  this.editInfo()
                }).catch(() => {
                  this.$emit('callback')
                })
              } else {
                this.$emit('callback')
              }
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    },
    async editInfo () {
      const res = await this.$api.appManagement.editInfo({ id: this.id })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        this.$emit('callback')
      }
    }
  }
}
</script>
<style lang="scss">
.informationAdd {
  width: 988px;

  .form-img {
    width: 100%;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
