<template>
  <div class="personnelAdministrationRetireNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             :show-message="false"
             label-width="136px"
             class="newFormPaper">
      <div class="title">退休人员信息</div>
      <el-form-item label="姓名"
                    class="formInput"
                    prop="name">
          <el-input placeholder="请输入姓名"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="性别"
                    class="formInput"
                    prop="gender">
        <el-select v-model="form.gender"
                   filterable
                   clearable
                   placeholder="请选择性别">
          <el-option v-for="item in genderData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="民族"
                    class="formInput formBorder"
                    prop="nation">
         <el-select v-model="form.nation"
                   filterable
                   clearable
                   placeholder="请选择民族">
          <el-option v-for="item in nationData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="出生年月"
                    class="formInput">
         <el-date-picker type="month"
                        v-model="form.birthday"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        placeholder="请选择出生年月">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="政治面貌"
                    class="formInput formBorder"
                    prop="name">
        <el-select v-model="form.politicCountenance"
                   filterable
                   clearable
                   placeholder="请选择政治面貌">
          <el-option v-for="item in politicCountenanceData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="入党时间"
                    class="formInput">
        <el-date-picker type="month"
                        v-model="form.joinPartyDate"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        placeholder="请选择入党时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="原职务"
                    class="formInput formBorder">
          <el-select v-model="form.duty"
                   filterable
                   clearable
                   placeholder="请选择原职务">
            <el-option v-for="item in dutyData"
                      :key="item.id"
                      :label="item.value"
                      :value="item.id">
            </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="退休时间"
                    class="formInput"
                    prop="retireDate">
        <el-date-picker type="month"
                        v-model="form.retireDate"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        placeholder="请选择退休时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="级别"
                    class="formInput formBorder formLevel">
          <el-select v-model="form.rank"
                   filterable
                   clearable
                   placeholder="请选择级别">
            <el-option v-for="item in rankData"
                      :key="item.id"
                      :label="item.value"
                      :value="item.id">
            </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所获市级以上荣誉"
                    class="formUpload">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入内容"
          v-model="form.honor">
        </el-input>
      </el-form-item>
      <el-form-item label="备注"
                    class="formUpload">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入需要备注的内容"
          v-model="form.remarks">
        </el-input>
      </el-form-item>
      <div class="formButton">
        <el-button type="primary"
                   @click="submitForm('form')">提 交</el-button>
        <el-button @click="reset">{{id ? '取&nbsp;&nbsp;&nbsp;消' : '重&nbsp;&nbsp;&nbsp;置'}}</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        name: '',
        gender: '',
        nation: '',
        politicCountenance: '',
        joinPartyDate: '',
        retireDate: '',
        rank: '',
        duty: '',
        honor: '',
        remarks: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '请选择性别', trigger: 'blur' }
        ],
        nation: [
          { required: true, message: '请选择民族', trigger: 'blur' }
        ],
        politicCountenance: [
          { required: true, message: '请选择政治面貌', trigger: 'blur' }
        ],
        retireDate: [
          { required: true, message: '请选择退休时间', trigger: 'blur' }
        ]
      },
      genderData: [],
      nationData: [],
      dutyData: [],
      rankData: [],
      politicCountenanceData: [],
      id: this.$route.query.id
    }
  },
  inject: ['refresh'],
  mounted() {
    this.dictionaryPubkvs()
    if (this.id) {
      this.opinionexamineInfo()
    }
  },
  methods: {
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/retireeinfo/add'
          if (this.id) {
            url = '/retireeinfo/edit'
          }
          var form = this.form
          this.$api.systemSettings.generalAdd(url, {
            id: this.id,
            name: form.name,
            gender: form.gender,
            nation: form.nation,
            birthday: form.birthday,
            politicCountenance: form.politicCountenance,
            joinPartyDate: form.joinPartyDate,
            retireDate: form.retireDate,
            remarks: form.remarks,
            duty: form.duty,
            honor: form.honor,
            rank: form.rank
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
              this.refresh()
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    reset () {
      this.$confirm(`${this.id ? '取消' : '重置'}将不会保存当前已编辑的内容, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('callback')
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消${this.id ? '操作' : '重置'}`
        })
      })
    },
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'sex,nation_type,politic_countenance_type,retiree_rank_type,duty_type'
      })
      var { data } = res
      this.genderData = data.sex
      this.nationData = data.nation_type
      this.politicCountenanceData = data.politic_countenance_type
      this.dutyData = data.duty_type
      this.rankData = data.retiree_rank_type
    },
    async opinionexamineInfo () {
      const res = await this.$api.appManagement.retireeinfoInfo(this.id)
      var { data } = res
      this.form = data
    }
  }
}
</script>
<style lang="scss">
  .personnelAdministrationRetireNew {
    width: 1080px;
    padding: 24px;
    margin: 0 auto;
    .formBorder {
      border-left: none!important;
    }
  .formLevel{
    width: 100%;
  }
  }
</style>
