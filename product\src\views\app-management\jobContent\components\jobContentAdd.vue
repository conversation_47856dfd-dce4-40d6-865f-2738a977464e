<template>
  <div class="jobContentAdd">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="选择党组"
                    prop="title"
                    class="form-title">
        <!-- <el-input placeholder="工作内容"
                  v-model="form.leadingPartyGroup"
                  clearable>
        </el-input> -->
        <el-select v-model="form.leadingPartyGroup"
                   filterable
                   clearable
                   placeholder="请选择党组">
          <el-option v-for="item in leadingPartyGroupData"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input">
        <el-input-number v-model="form.sort"
                         placeholder="请输入排序"
                         :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="工作内容"
                    class="form-ue">
        <UEditor v-model="form.jobContent"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'jobContentAdd',
  data () {
    return {
      form: {
        sort: '',
        leadingPartyGroup: '',
        jobContent: ''
      },
      rules: {
      },
      leadingPartyGroupData: [
        { id: '1', name: '政协党组' },
        { id: '2', name: '机关党组' },
        { id: '3', name: '分党组' },
        { id: '4', name: '党总支、各支部' }
      ]
    }
  },
  props: ['id', 'module', 'parentId'],
  mounted () {
    if (this.id) {
      this.informationListInfo()
    } else {
      this.form.publishDate = this.$format(new Date(), 'YYYY-MM-DD HH:mm:ss')
      if (this.parentId && this.parentId !== '1') {
        this.form.structureId = this.parentId
      }
    }
  },
  methods: {
    async informationListInfo () {
      const res = await this.$api.appManagement.jobcontentInfo(this.id)
      var { data } = res
      this.form.leadingPartyGroup = data.leadingPartyGroup
      this.form.jobContent = data.jobContent
      this.form.sort = data.sort
    },
    /**
   * 提交
  */
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/jobcontent/add'
          if (this.id) {
            url = '/jobcontent/edit'
          }
          this.$api.general.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            leadingPartyGroup: this.form.leadingPartyGroup,
            jobContent: this.form.jobContent,
            type: 1, // 工作内容
            parentId: 0,
            sort: this.form.sort
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.jobContentAdd {
  width: 988px;
  .form-img {
    width: 100%;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
