export function parseTime (time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  // eslint-disable-next-line
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  // eslint-disable-next-line
  return time_str
}

export function exportexcel (multiHeader, tHeader, tableJson, filename, merges = []) {
  import('@/assets/export/Export-Excel').then(excel => {
    const styleArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    const styleList = []
    // console.log(tHeader.length)
    const styleList1 = styleArr.slice(0, tHeader.length)
    styleList1.forEach(item => {
      for (let i = 1; i < 4; i++) {
        styleList.push(item + i)
      }
    })
    // 进行所有表头的单元格合并，建议一行一行来，不然容易整乱
    // const merges = [
    //   'A1:A3',
    //   'B1:B3',
    //   'C1:G1',
    //   'H1:I1',
    //   'J1:K1',
    //   'C2:D2',
    //   'E2:G2',
    //   'H2:H3',
    //   'I2:I3',
    //   'J2:J3',
    //   'K2:K3'
    // ]

    const data = formatJson(tableJson.filterVal, tableJson.tableData)
    excel.export_json_to_excel({
      data, // 数据
      merges, // 合并
      styleList, // 样式
      multiHeader, // 表头
      header: tHeader, // 表头
      filename: filename // 文件名
    })
  })
}
// 数据过滤，时间过滤
function formatJson (filterVal, jsonData) {
  return jsonData.map(v =>
    filterVal.map(j => {
      if (j === 'timestamp') {
        return parseTime(v[j])
      } else {
        return v[j]
      }
    })
  )
}
