// 导入封装的方法
import Vue from 'vue'
import {
  get,
  post,
  postform,
  exportFile,
  fileRequest
} from '../http'
const SmallModule = {
  uploadFile (params) {
    return postform('/attachment/uploadFile', params, { timeout: 80000 })
  }
}
const membersDuty = {
  // 代表值班的导出
  memberGuestExport (data) {
    return exportFile('/officeonlinetopic/export', data)
  },
  // 来信导出
  letterExport (data) {
    return exportFile('/officeonlineletter/export', data)
  },
  // 获取值班代表
  memberList (data) {
    return post(`/officeonlineschedule/info/${data}`)
  },
  // 导入值班excel
  memberListExport (data) {
    return postform('/officeonlinetopic/import', data)
  },

  // 委员值班列表
  officeonlinescheduleList (params) {
    return post('/officeonlineschedule/list?', params)
  },
  // 委员值班列表删除
  officeonlinescheduleDel (params) {
    return post('/officeonlineschedule/dels', params)
  },
  // 委员值班主题列表
  officeonlinetopicList (params) {
    return post('/officeonlinetopic/list?', params)
  },
  // 委员值班来信列表
  officeonlineletterList (params) {
    return post('/officeonlineletter/list?', params)
  },
  // 委员值班来信列表详情
  officeonlineletterInfo (params) {
    return post(`/officeonlineletter/info/${params}`)
  },
  // 委员值班来信审核
  officeonlineletterAudit (params) {
    return post('/officeonlineletter/publish', params)
  },
  // 委员值班来信删除
  officeonlineletterDel (params) {
    return post('/officeonlineletter/dels', params)
  },
  // 设置来信默认公开状态
  officeonlineletterEditSwitch (data) {
    return post('/officeonlineletter/editSwitch', data)
  },
  // 获取来信默认公开状态
  officeonlineletterShowSwitch (data) {
    return post('/officeonlineletter/showSwitch', data)
  }
}
// 智慧小助手
const wisdomLittleHelper = {
  // 开头语列表
  openingWordList (data) {
    return post('/rd/assistant/welcome/list', data)
  },
  // 特殊关键词列表
  specialKeywordList (data) {
    return post('/rd/assistant/question/list', data)
  },
  // 关键词删除
  specialKeywordDels (data) {
    return post('/rd/assistant/question/dels', data)
  },
  // 开头语列表
  openingWordZXList (data) {
    return post('/zx/assistant/welcome/list', data)
  },
  // 特殊关键词列表
  specialKeywordZXList (data) {
    return post('/zx/assistant/question/list', data)
  },
  // 关键词删除
  specialKeywordZXDels (data) {
    return post('/zx/assistant/question/dels', data)
  }
}
const InstallationRates = {
  joinappusertype (data) {
    return post('/wholeuser/joinapp/usertype', data)
  },
  joinapp (data) {
    return post('/wholeuser/joinapp?', data)
  }
}
// 模块管理
const moduleManagement = {
  moduleList (params) {
    return post('/module/list?', params)
  },
  moduleInfo (params) {
    return post(`/module/info/${params}`)
  },
  moduleDel (params) {
    return post('/module/dels', params)
  },
  areaTree (params) {
    return post('/area/tree?', params)
  },
  moduleTree (params) {
    return post('/module/tree', params)
  }
}
// 说说
const membersSaid = {
  /**
     * 获取代表说列表
     */
  committeesayList (params) {
    return post('/committeesay/list', params)
  },
  /**
     * 修改代表说
     */
  committeesayEdit (params) {
    return post('/committeesay/edit', params)
  },
  /**
     * 添加代表说
     */
  committeesayAdd (params) {
    return post('/committeesay/add', params)
  },
  /**
     * 代表说详情
     */
  committeesayInfo (params) {
    return post('/committeesay/info/' + params)
  },
  /**
     * 批量删除代表说
     */
  committeesayDels (params) {
    return post('/committeesay/dels', params)
  },
  /**
     * 批量审核代表说
     */
  committeesayBatchUpdate (params) {
    return post('/committeesay/batchUpdate', params)
  }
}
// 网络议政=意见征集
const NetworkPolitics = {
  // 网络议政管理列表
  networkPoliticsList (params) {
    return post('/survey/list?', params)
  },
  groupMessageStatistics (data) {
    return get('/talkroup/groupMessageStatistics', data)
  },
  // 网络议政管理列表详情
  networkPoliticsInfo (params) {
    return post('/survey/info/' + params)
  },
  // 删除议政
  networkPoliticsDel (params) {
    return post('/survey/dels', params)
  },
  // 组织方发布列表
  organizingList (params) {
    return post('/surveyaddition/list', params)
  },
  // 组织方发布详情
  organizingInfo (params) {
    return post('/surveyaddition/info/' + params)
  },
  // 组织方发布删除
  organizingdels (params) {
    return post('/surveyaddition/dels', params)
  },
  // 网络议政列表带子级
  networkPoliticsSurveyList (params) {
    return post('/survey/findSurveyList?', params)
  },
  // 网络议政列表下载文件
  networkFile (params, text) {
    fileRequest('/attachment/downloadFile?', params, text)
  },
  // 网络议政导出
  outSurveyDetailWord (params) {
    exportFile('/survey/outSurveyDetailWord', params)
  }
}
// 通知公告
const noticeAnnouncement = {
  noticeList (params) { // 通知列表
    return post('/notice/list?', params)
  },
  noticeaddedit (url, params) { // 新增详情
    return post(url, params)
  },
  noticeinfo (params) { //  详情
    return post(`/notice/info/${params}`)
  },
  noticedels (params) { //  删除
    return post('/notice/dels', params)
  },
  readingDetail (params) { //  阅读详情
    return post(`/notice/readingDetail?id=${params}`)
  },
  sendRemindSms (params) { //  短信提醒
    return post('/notice/sendRemindSms', params)
  },
  noticereturnoptionlist (params) { //  选项列表
    return post('/noticereturnoption/list', params)
  },
  noticereturnoptioninfo (params) { //  选项获取详情
    return post(`/noticereturnoption/info/${params}`)
  },

  noticereturnlist (params) { //  回执列表
    return post('/noticereturn/list', params)
  },
  noticereturnoptionadd (url, params) { //  选项新增
    return post(url, params)
  },
  noticereturnoptiondels (params) { //  选项新增
    return post('/noticereturnoption/dels', params)
  },
  noticereturndels (params) { //  回执删除
    return post('/noticereturn/dels', params)
  }
}
// 远程协商
const RemoteNegotiation = {
  // 远程协商列表
  zyMeetingList (params) {
    return post('/zyMeeting/list?', params)
  },
  // 远程协商列表详情
  zyMeetingInfo (params) {
    return post(`/zyMeeting/info/${params}`)
  },
  // 远程协商列表删除
  zyMeetingDel (params) {
    return post('/zyMeeting/dels', params)
  }
}
// 考试管理
const examManagement = {
  // 考试管理列表
  examManagementList (params) {
    return post('/paper/list?', params)
  },
  // 新增考试
  examManagementAdd (params) {
    return post('/paper/add', params)
  },
  // 考试管理删除
  examManagementDel (params) {
    return post('/paper/dels', params)
  },
  // 考试详情
  examManagementInfo (id) {
    return post(`/paper/info/${id}`)
  },
  // 考试发布
  publicPaper (params) {
    return post('/paper/publicPaper', params)
  },
  // 考试取消发布
  rePublicPaper (params) {
    return post('/paper/rePublicPaper', params)
  },
  // 获取考试题目列表
  examManagementTopicList (params) {
    return post('/paperquestionrelation/list', params)
  }
}
// 提问监控
const questioningMonitoring = {
  // 提问列表
  questionlist (type, params) {
    return post(`/${type}/assistant/monitor/question/list`, params)
  },
  // 新增编辑提问
  questionadd (type, url, params) {
    return post(`/${type}/assistant/monitor/question/` + url, params)
  },
  // 删除提问列表
  questiondels (type, params) {
    return post(`/${type}/assistant/monitor/question/dels`, params)
  },
  // 提问详情
  questioninfo (type, params) {
    return post(`/${type}/assistant/monitor/question/info/` + params)
  },
  // 批量设置问题处理状态
  batchUpdateQuestionStatus (type, params) {
    return post(`/${type}/assistant/monitor/question/batchUpdateQuestionStatus`, params)
  }
}
// 敏感词管理
const SensitiveWord = {
  // 敏感词列表
  mgcWordList (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/words/mgcWordList`, params)
  },
  // 敏感词新增
  addMgcWord (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/words/addMgcWord`, params)
  },
  // 敏感词删除
  deleteMgcWord (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/words/deleteMgcWord`, params)
  }
}
// 通讯录
const addressBook = {
  /**
     * 通讯录树
     */
  treeTist (params) {
    return post('/tree/list?', params)
  },
  /**
     * 通讯录用户
     */
  treeTistUser (params) {
    return post('/wholeuser/chaters?', params)
  },
  /**
     * 通讯录新增用户
     */
  userelationNew (params) {
    return post('/userelation/save?', params)
  },
  /**
     * 通讯录删除用户
     */
  userelationDel (params) {
    return post('/wholeuser/removechater?', params)
  },
  /**
     * 通讯录分组详情
     */
  treeTistInfo (params) {
    return post(`/tree/info/${params}`)
  },
  /**
     * 通讯录分组删除
     */
  treeTistDel (params) {
    return post(`/tree/del/${params}`)
  },
  /**
     * 用户群组列表
     */
  groupList (params) {
    return post('/talkroup/list', params)
  },
  /**
     * 分配群用户
     */
  groupUser (params) {
    return post('/talkroup/join?', params)
  },
  /**
     * 解散群
     */
  disband (params) {
    return post(`/talkroup/disband/${params}`)
  },
  /**
     * 交流群详情
     */
  talkroupInfo (params) {
    return post('/talkroup/info/' + params)
  },
  /**
     * 群成员列表
     */
  groupUserList (params) {
    return post('/talkroup/member?', params)
  },
  /**
     * 设置管理员
     */
  setGroupAdmin (params) {
    return post('/groupadministrator/setGroupAdministrator', params)
  },
  /**
     * 取消管理员
     */
  cancelGroupAdmin (params) {
    return post('/groupadministrator/cancelGroupAdministrator', params)
  },
  /**
     * 取消管理员
     */
  transferManager (params) {
    return post('/talkroup/edit', params)
  },
  /**
  /**
     * 个人通讯录人员列表
     */
  zyusercontactList (params) {
    return post('/zyusercontact/list?', params)
  },
  /**
     * 个人通讯录人员详情
     */
  zyusercontactInfo (params) {
    return post('/zyusercontact/info/' + params)
  },
  /**
     * 个人通讯录人员删除
     */
  zyusercontactDel (params) {
    return post('/zyusercontact/dels', params)
  },
  /**
     * 个人通讯录分组列表
     */
  zyusercontacttypeList (params) {
    return post('/zyusercontacttype/list?', params)
  },
  /**
     * 个人通讯录分组详情
     */
  zyusercontacttypeInfo (params) {
    return post('/zyusercontacttype/info/' + params)
  },
  /**
     * 个人通讯录分组删除
     */
  zyusercontacttypeDel (params) {
    return post('/zyusercontacttype/dels', params)
  }
}
// 互动交流文件
const interactiveFile = {
  fileinfoList (params) {
    return post('/fileinfo/list?', params)
  },
  downloadFile (params, text) {
    fileRequest(`/fileinfo/download/${params}`, {}, text)
  },
  fileinfoAdd (params) {
    return postform('/fileinfo/add?', params)
  },
  fileinfoInfo (params) {
    return postform(`/fileinfo/info/${params}`)
  },
  fileinfoDel (params) {
    return postform(`/fileinfo/del/${params}`)
  },
  videoinfoAdd (params) { // 视频新增
    return post('/videoinfo/add?', params)
  },
  videoinfoEdit (params) { // 视频编辑
    return post('/videoinfo/edit?', params)
  },
  videoinfoList (params) { // 视频列表
    return post('/videoinfo/list?', params)
  },
  videoinfoInfo (params) { // 视频详情
    return post('/videoinfo/info/' + params)
  },
  videoinfoDel (params) { // 视频删除
    return postform(`/videoinfo/del/${params}`)
  }
}
// app下载
const appDownloadUrl = {
  appDownloadUrlList (params) {
    return post('/appdownloadurl/list?', params)
  },
  appDownloadUrlDel (params) {
    return post('/appdownloadurl/dels', params)
  },
  appDownloadUrlAdd (params) {
    return post('/appdownloadurl/add', params)
  },
  appDownloadUrlEdit (params) {
    return post('/appdownloadurl/edit', params)
  },
  appDownloadUrlInfo (params) {
    return post('/appdownloadurl/info', params)
  }
}
export default {
  ...SmallModule,
  ...membersDuty,
  ...wisdomLittleHelper,
  ...InstallationRates,
  ...moduleManagement,
  ...membersSaid,
  ...NetworkPolitics,
  ...noticeAnnouncement,
  ...RemoteNegotiation,
  ...examManagement,
  ...questioningMonitoring,
  ...SensitiveWord,
  ...addressBook,
  ...interactiveFile,
  ...appDownloadUrl
}
