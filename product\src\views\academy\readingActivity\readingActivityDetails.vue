<template>
  <div class="readingActivityDetails details">
    <div class="details-title">读书活动详情</div>
    <div class="details-item-box">
      <div class="details-item-title">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.title}}</div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">活动类型</div>
          <div class="details-item-value">{{details.schemeTypeName}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">是否公开</div>
          <div class="details-item-value">{{details.isOpen==1?'公开':'不公开'}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">报名开始时间</div>
          <div class="details-item-value">{{details.applyStartTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">开始签到时间</div>
          <div class="details-item-value">{{details.signStartTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">活动开始时间</div>
          <div class="details-item-value">{{details.startTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">组织部门</div>
          <div class="details-item-value">{{details.officeName}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">是否发布</div>
          <div class="details-item-value">{{details.isPublish==1?'已发布':'待发布'}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">报名截止时间</div>
          <div class="details-item-value">{{details.applyEndTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">签到截止时间</div>
          <div class="details-item-value">{{details.signEndTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">活动结束时间</div>
          <div class="details-item-value">{{details.endTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">活动地点</div>
        <div class="details-item-value">{{details.handlAddress}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">邀请人</div>
        <div class="details-item-value activityUser">
          <el-tag v-for="(item) in details.users"
                  :key="item.userId">{{item.name}}</el-tag>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">关联书籍</div>
        <div class="details-item-value">
          <div class="readingActivityDetailsItem"
               v-for="(item) in details.books"
               :key="item.id">
            <div class="readingActivityDetailsItemImg">
              <img :src="item.coverImgUrl"
                   alt="">
            </div>
            <div class="readingActivityDetailsItemBox">
              <div class="readingActivityDetailsItemName">{{item.bookName}}</div>
              <div class="readingActivityDetailsItemIntroduction">{{item.bookDescription}}</div>
              <div class="readingActivityDetailsItemAuthor">
                <div class="readingActivityDetailsItemAuthorText">{{item.authorName}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'readingActivityDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.readschemeInfo()
  },
  methods: {
    /**
     *详情
    */
    async readschemeInfo () {
      const res = await this.$api.academy.readschemeInfo(this.id)
      var { data } = res
      this.details = data
    }
  }
}
</script>
<style lang="scss">
.readingActivityDetails {
  height: 100%;
  padding: 24px;
  .el-tag {
    margin-right: 12px;
    margin-bottom: 6px;
  }
  .readingActivityDetailsItem {
    display: flex;
    justify-content: space-between;
    width: 632px;
    height: 128px;
    margin: 12px 0;
    cursor: pointer;
    .readingActivityDetailsItemImg {
      height: 128px;
      width: 95px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .readingActivityDetailsItemBox {
      width: 522px;
      height: 100%;
      position: relative;
      .readingActivityDetailsItemName {
        color: #333;
        line-height: 21px;
        font-size: 16px;
        margin-bottom: 7px;
      }
      .readingActivityDetailsItemIntroduction {
        line-height: 24px;
        color: #666;
        letter-spacing: 0.93px;
        height: 72px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 13px;
        white-space: normal !important;
      }
      .readingActivityDetailsItemAuthor {
        position: absolute;
        left: 0;
        bottom: -2px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .readingActivityDetailsItemAuthorText {
          font-size: 13px;
          color: #999;
          letter-spacing: 1px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
  .activityUser {
    text-overflow: clip !important;
    white-space: normal !important;
  }
}
</style>
