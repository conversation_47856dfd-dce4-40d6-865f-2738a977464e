<template>
  <div class="Transition">
    <div class="livingExample">
      <div class="livingExampleText">在进行换届操作之前，请您做好以下准备工作</div>
      <div class="livingExampleText">
        1、请维护好系统内届次信息，确保“当届当次”对应数据是您需要的正确数据；
        <el-link type="primary"
                 @click="quickClick">去维护届次信息</el-link>
      </div>
      <div class="livingExampleText">2、请根据导入模板，将新届次{{memberType=='1'?'委员':'代表'}}信息维护好；
        <el-button type="primary"
                   @click="importemplate">点击下载导入模板</el-button>
      </div>
      <div class="livingExampleText">维护好信息后，将文档上传至系统中，<span>先预览确认！</span>然后再点击换届按钮一键换届。</div>
    </div>
    <div class="TransitionText">请上传已整理好的新届次{{memberType=='1'?'委员':'代表'}}信息文档，先预览，确认无误后在操作换届按钮。</div>
    <div class="TransitionButton">
      <el-button type="primary"
                 @click="preview">换届预览</el-button>
      <el-button type="danger"
                 @click="determine">一键换届</el-button>
    </div>
    <div class="TransitionUpload">
      <el-upload drag
                 action="/"
                 :file-list="fileList"
                 :before-upload="handleFile"
                 :http-request="fileUpload"
                 multiple>
        <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
        <div class="el-upload__tip">仅支持.xls或.xlsx格式</div>
      </el-upload>
    </div>
    <div class="TransitionPreview"
         v-if="details.curMemberCount">
      <div class="TransitionPreviewName">换届预览结果</div>
      <div class="TransitionPreviewBox">
        <div class="TransitionPreviewText">本届{{memberType=='1'?'委员':'代表'}}总数：<span>{{details.curMemberCount}}位</span></div>
        <div class="TransitionPreviewText"
             v-if="details.targetCircleBout">目标届次：<span>{{details.targetCircleBout.simpleName}}</span></div>
      </div>
      <template v-if="details.addMembers">
        <div class="TransitionPreviewText"
             v-if="details.addMembers.length">新增{{memberType=='1'?'委员':'代表'}}数量：<span>{{details.addMembers.length}}位</span></div>
        <div class="userBox"
             v-if="details.addMembers.length">
          <div class="userItem"
               v-for="(item,index) in details.addMembers"
               :key="index+'zx'">{{item}}</div>
        </div>
      </template>
      <template v-if="details.keepMembers">
        <div class="TransitionPreviewText"
             v-if="details.keepMembers.length">连任{{memberType=='1'?'委员':'代表'}}数量：<span>{{details.keepMembers.length}}位</span><span>说明：连任{{memberType=='1'?'委员':'代表'}}在换届操作时候，系统将进行覆盖导入，信息将更新成最新数据。</span></div>
        <div class="userBox"
             v-if="details.keepMembers.length">
          <div class="userItem"
               v-for="(item,index)  in details.keepMembers"
               :key="index+'lr'">{{item}}</div>
        </div>
      </template>
      <template v-if="details.vacantMembers">
        <div class="TransitionPreviewText"
             v-if="details.vacantMembers.length">出缺{{memberType=='1'?'委员':'代表'}}数量：<span>{{details.vacantMembers.length}}位</span></div>
        <div class="userBox"
             v-if="details.vacantMembers.length">
          <div class="userItem"
               v-for="(item,index)  in details.vacantMembers"
               :key="index+'cq'">{{item}}</div>
        </div>
      </template>
      <div class="TransitionPreviewText"
           v-if="details.errorMsg.length">失败信息</div>
      <div class="errorMsgItem"
           v-for="(item,index) in details.errorMsg"
           :key="index+'sb'">{{item}}</div>
    </div>
    <template v-if="show">
      <div class="TransitionText">
        <i class="el-icon-loading"
           v-if="errorShow === null"></i>
        静待系统上传文件吧！这个过程受网络带宽影响较大，故请您耐心等待，不要关闭此页面。
      </div>
      <div class="TransitionTextSuccess"
           v-if="errorShow === false">换届操作成功
      </div>
      <div class="TransitionTextError"
           v-if="errorShow === true">换届操作失败，请检查网络稍后重新操作</div>
    </template>
  </div>
</template>
<script>
export default {
  name: 'Transition',
  data () {
    return {
      blob: null,
      errorShow: null,
      fileList: [],
      details: {},
      show: false
    }
  },
  inject: ['jumpMenu'],
  props: ['memberType'],
  methods: {
    /**
     * 限制上传附件的文件类型
    */
    handleFile (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension = testmsg === 'xlsx'
      const extension1 = testmsg === 'xls'
      if (!extension && !extension1) {
        this.$message({
          message: '上传文件只能是Excel格式!',
          type: 'warning'
        })
      }
      return extension || extension1
    },
    /**
     * 上传附件请求方法
    */
    fileUpload (files) {
      this.fileList = [files.file]
    },
    preview () {
      if (!this.fileList.length) {
        this.$message({
          message: `请先上传新届次${this.memberType === 1 ? '委员' : '代表'}信息文档!`,
          type: 'warning'
        })
        return
      }
      this.details = {}
      this.show = false
      const param = new FormData()
      param.append('memberType', this.memberType)
      param.append('file', this.fileList[0])
      this.$api.memberInformation.changePreview(param).then(res => {
        this.details = res.data
      })
    },
    determine () {
      if (!this.fileList.length) {
        this.$message({
          message: `请先上传新届次${this.memberType === 1 ? '委员' : '代表'}信息文档!`,
          type: 'warning'
        })
        return
      }
      this.details = {}
      this.show = true
      const param = new FormData()
      param.append('memberType', this.memberType)
      param.append('file', this.fileList[0])
      this.$api.memberInformation.changeJc(param).then(res => {
        this.errorShow = false
      }).catch(() => {
        this.errorShow = true
      })
    },
    importemplate () {
      this.$api.memberInformation.importemplate({ memberType: this.memberType, systemType: this.memberType === 1 ? 1 : 2 })
    },
    quickClick () {
      // if (this.memberType === 1) {
      //   this.jumpMenu('/time?memberType=1')
      // } else {
      this.jumpMenu('/time')
      // }
    }
  }
}
</script>
<style lang="scss">
.Transition {
  width: 1060px;
  height: 100%;
  padding: 24px;
  .livingExample {
    width: 100%;
    padding: 24px;
    border: 2px solid $zy-color;
    margin-bottom: 22px;
    background-color: $zy-withColor;
    .livingExampleText {
      line-height: 36px;
      span {
        color: red;
        font-weight: 600;
      }
      .el-link {
        font-size: 16px;
        height: 32px;
        line-height: 32px;
        span {
          color: $zy-color;
        }
      }
      .el-button {
        padding: 0 12px;
        height: 32px;
        line-height: 32px;
        span {
          color: #fff;
        }
      }
    }
  }
  .TransitionText {
    line-height: 36px;
  }
  .TransitionButton {
    padding: 12px 0;
    .el-button {
      padding: 0 32px;
      height: 36px;
      line-height: 36px;
    }
    .el-button + .el-button {
      padding: 0 12px;
    }
  }
  .TransitionUpload {
    padding: 12px 0;
    .el-upload {
      width: 100%;
      .el-upload-dragger {
        height: 82px;
        width: 100%;
        background-color: #e6e5e8;
        .el-upload__text {
          padding-top: 22px;
          font-size: 14px;
          line-height: 22px;
        }
        .el-upload__tip {
          font-size: 12px;
          line-height: 20px;
          color: #6e6e6e;
          margin-top: 0;
        }
      }
    }
  }
  .TransitionTextSuccess,
  .TransitionTextError {
    padding-top: 22px;
  }
  .TransitionPreview {
    padding: 24px;
    background-color: #f2f2f2;
    border: 1px dashed #ccc;
    .TransitionPreviewName {
      font-size: 22px;
      font-weight: bold;
      line-height: 52px;
    }
    .TransitionPreviewText {
      line-height: 38px;
      border-bottom: 1px dashed #ccc;
      span {
        color: red;
        font-size: 18px;
        font-weight: bold;
      }
      span + span {
        color: #999;
        font-weight: normal;
        font-size: 16px;
        margin-left: 22px;
      }
    }
    .TransitionPreviewBox + .TransitionPreviewText {
      border-bottom: 0;
    }
    .TransitionPreviewBox {
      display: flex;
      padding-bottom: 12px;
      border-bottom: 1px dashed #ccc;
      .TransitionPreviewText {
        border-bottom: 0;
      }
      .TransitionPreviewText + .TransitionPreviewText {
        margin-left: 52px;
      }
    }
    .userBox {
      padding: 22px 0;
      .userItem {
        display: inline-block;
        margin-right: 12px;
        background-color: #fff;
        padding: 12px;
        margin-bottom: 12px;
      }
    }
    .errorMsgItem {
      color: red;
      line-height: 28px;
    }
  }
}
</style>
