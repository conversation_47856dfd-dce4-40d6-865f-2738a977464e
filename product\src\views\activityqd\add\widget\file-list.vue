<template>
  <div class="activity-file-list1">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input v-model="keyword"
                  placeholder="请输入关键字"
                  clearable
                  @keyup.enter.native="search"></el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="isAdd=true">新增</el-button>
        <el-button type="primary"
                   @click="handleBatchDelete">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  stripe
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                           prop="sort"
                           min-width="80"></el-table-column>
          <el-table-column label="材料名称"
                           prop="name"
                           min-width="240"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="材料名称"
                           prop="name"
                           min-width="240"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="材料格式"
                           prop="format"
                           width="180"></el-table-column>
          <el-table-column label="创建人"
                           prop="createName"
                           min-width="180"></el-table-column>
          <el-table-column label="发布时间"
                           prop="date"
                           min-width="180"></el-table-column>
          <el-table-column label="是否公开"
                           width="120">
            <template slot-scope="scope">
              {{scope.row.isAppShow === 1 ?'公开':'不公开'}}
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <zy-pop-up v-model="isAdd"
               title="添加材料">
      <add-file @callback="handleCallback"></add-file>
    </zy-pop-up>
  </div>
</template>

<script>
import AddFile from './widget/add-file.vue'
export default {
  components: { AddFile },
  data () {
    return {
      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),
      keyword: '',
      list: [],
      selectionList: [],
      originList: [],
      isAdd: false
    }
  },
  methods: {
    search () {
      if (this.keyword === '') {
        return this.$message.warning('请输入想要搜索的')
      }
      this.list = this.originList.filter(v => v.name.indexOf(this.keyword) !== -1)
    },
    reset () {
      this.keyword = ''
      this.list = []
    },
    handleBatchDelete () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.selectionList.forEach(item => {
        this.deleteList(item.fileId)
        this.deleteOriginList(item.fileId)
      })
    },
    deleteList (fileId) {
      var list = this.list
      this.list = list.filter(item => item.fileId !== fileId)
    },
    deleteOriginList (fileId) {
      var originList = this.originList
      this.originList = originList.filter(item => item.fileId !== fileId)
    },
    handleSelectionChange (val) {
      this.selectionList = val
    },
    handleCallback (val) {
      if (val) {
        val.forEach(item => {
          item.createName = this.user.userName
        })
      }
      this.list.push.apply(this.list, val)
      this.originList = this.list
      this.isAdd = false
      console.log(this.list)
    }
  }
}
</script>
<style lang="scss">
.activity-file-list1 {
  height: 100%;
  padding: 24px 40px;
  padding-right: 0;
  .tableData {
    height: 500px;
  }
}
</style>
