.xyl-tab {
  width: 100%;
  margin: auto;
  background-color: #fff;
  border-bottom: 1px solid #eeeeee;

  .xyl-tab-wrap {
    width: 100%;
    overflow: hidden;

    .xyl-tab-prev,
    .xyl-tab-next {
      position: absolute;
      top: 2px;
      height: 100%;
      width: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      cursor: pointer;
      z-index: 9;
    }

    .xyl-tab-prev {
      left: 0;
    }

    .xyl-tab-next {
      right: 0;
    }

    .xyl-tab-scroll {
      white-space: nowrap;
      position: relative;
      transition: transform 0.3s;
      float: left;
      display: block;
      z-index: 3;

      .xyl-tab-item {
        display: inline-block;
        height: 42px;
        line-height: 42px;
        cursor: pointer;
        padding-left: 22px;
        border-right: 1px solid #eeeeee;
        -moz-user-select: none; /*火狐*/
        -webkit-user-select: none; /*webkit浏览器*/
        -ms-user-select: none; /*IE10*/
        -khtml-user-select: none; /*早期浏览器*/
        user-select: none;
        .tabSlots {
          position: relative;
          display: inline-block;
          transform: translateY(-3px);
          &::after {
            content: '';
            position: absolute;
            top: 36px;
            left: 50%;
            transform: translateX(-50%);
            width: 38px;
            height: 2px;
            background-color: transparent;
          }
        }
        .el-popover__reference-wrapper {
          display: inline-block;
          width: 42px;
        }
        .tab-popover-icon {
          float: right;
          width: 9px;
          height: 32px;
          margin: 0 16px;
          background: url('../../assets/img/tab-icon.png') no-repeat;
          background-size: 9px 20px;
          background-position: center 100%;
          &:focus {
            outline: none;
          }
        }
        .tab-popover-icon-a {
          background: url('../../assets/img/tab-icon-a.png') no-repeat;
          background-size: 9px 20px;
          background-position: center 100%;
        }
      }

      .is-active {
        color: $zy-color;
        font-weight: 700;
        .tabSlots {
          &::after {
            background-color: $zy-color;
          }
        }
      }
    }
  }
}
.xyl-tab-popover {
  padding: 0 !important;
  margin: 0 !important;
  padding-bottom: 1px !important;
  .xyl-tab-extension {
    .xyl-tab-extension-item {
      height: 30px;
      width: 100%;
      padding: 0 8px;
      line-height: 30px;
      color: #999999;
      box-shadow: 0px 1px 2px rgba(15, 56, 90, 0.1);
      border: 1px solid #cccccc;
      cursor: pointer;
    }
    .xyl-tab-extension-item + .xyl-tab-extension-item {
      border-top: 0;
      border-bottom: 1px solid #cccccc;
    }
  }
}
