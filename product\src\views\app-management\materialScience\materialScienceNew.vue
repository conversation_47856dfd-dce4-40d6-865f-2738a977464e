<template>
  <div class="materialScienceNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             :show-message="false"
             label-width="136px"
             class="newFormPaper">
      <div class="title">材料提交</div>
      <el-form-item class="formInput formLevel"
                    label="提交单位"
                    prop="submitUnit">
          <el-select v-model="form.submitUnit"
                   filterable
                   clearable
                   placeholder="请选择提交单位">
          <el-option v-for="item in submitUnitData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="formTitle"
                    label="材料名称"
                    prop="materialName">
        <el-input placeholder="请输入标题"
                  v-model="form.materialName"
                  show-word-limit
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="活动事项"
                    class="formInput formLevel"
                    prop="activityItem">
        <el-select v-model="form.activityItem"
                   filterable
                   clearable
                   placeholder="请选择活动事项">
          <el-option v-for="item in activityItemData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传材料"
                    prop="submitUnit"
                    class="formUpload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :file-list="file"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注"
                    class="formUpload">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入需要备注的内容"
          v-model="form.remarks">
        </el-input>
      </el-form-item>
      <div class="formButton">
        <el-button type="primary"
                   @click="submitForm('form')">提&nbsp;&nbsp;&nbsp;交</el-button>
        <el-button @click="reset">{{id ? '取&nbsp;&nbsp;&nbsp;消' : '重&nbsp;&nbsp;&nbsp;置'}}</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'materialScienceNew',
  data () {
    return {
      form: {
        submitUnit: '',
        materialName: '',
        activityItem: ''
      },
      rules: {
        submitUnit: [
          { required: true, message: '请选择提交单位', trigger: 'blur' }
        ],
        materialName: [
          { required: true, message: '请输入材料名称', trigger: 'blur' }
        ],
        activityItem: [
          { required: true, message: '请选择活动事项', trigger: 'blur' }
        ]
      },
      submitUnitData: [],
      activityItemData: [],
      file: []
    }
  },
  props: ['id', 'type'],
  mounted () {
    this.dictionaryPubkvs()
    if (this.id) {
      this.socialinfoinfo()
    }
  },
  inject: ['refresh'],
  methods: {
    /**
     * 限制上传附件的文件类型
    */
    handleImg (file, fileList) {
    },
    /**
     * 上传附件请求方法
    */
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.mainModule.uploadFile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.file.push(data[0])
      }).catch(() => {
        files.onError()
      })
    },
    /**
     * 删除附件
    */
    beforeRemove (file, fileList) {
      var fileData = this.file
      this.file = fileData.filter(item => item.id !== file.id)
    },
    /**
     * 字典
     */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'submit_unit_type,activity_item_type'
      })
      var { data } = res
      this.submitUnitData = data.submit_unit_type
      this.activityItemData = data.activity_item_type
    },
    /**
     * 获取基础详情
    */
    async socialinfoinfo () {
      const res = await this.$api.appManagement.materialsubmitInfo(this.id)
      var { data } = res
      this.form = data
      data.attachmentList.forEach(item => {
        item.url = item.filePath
        item.name = item.fileName
      })
      this.file = data.attachmentList // 附件ids ，多个用逗号隔开
    },
    submitForm (formName, type) {
      if (!this.file.length) {
        this.$message({
          message: '请输入必填项',
          type: 'warning'
        })
        return
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.generalAdd(type)
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    async generalAdd (type) {
      var attachmentIds = []
      this.file.forEach(item => {
        attachmentIds.push(item.id)
      })
      var data = {
        id: this.id,
        submitUnit: this.form.submitUnit,
        materialName: this.form.materialName,
        activityItem: this.form.activityItem,
        remarks: this.form.remarks,
        attachmentIds: attachmentIds.join(',') // 附件ids ，多个用逗号隔开
      }
      if (type) {
        data.processStatus = type
      }
      var res = ''
      res = await this.$api.systemSettings.generalAdd(this.id ? '/materialsubmit/edit' : '/materialsubmit/add', data)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        this.$emit('callback')
        this.refresh()
      }
    },
    reset () {
      this.$confirm(`${this.id ? '取消' : '重置'}将不会保存当前已编辑的内容, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('callback')
      }).catch(() => {
        this.$message({
          type: 'info',
          message: `已取消${this.id ? '操作' : '重置'}`
        })
      })
    }
  }
}
</script>
<style lang="scss">
.materialScienceNew {
  width: 980px;
  padding: 24px;
  margin: 0 auto;
  .formCommittee{
      border-left: none!important;
  }
  .formLevel{
    width: 100%;
  }
}
</style>
