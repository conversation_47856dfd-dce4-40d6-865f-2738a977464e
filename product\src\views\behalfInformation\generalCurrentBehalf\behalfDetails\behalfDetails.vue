<template>
  <div class="memberDetailsQd">
    <div class="memberDetailsQdTitle">代表信息详情</div>
    <div class="memberDetailsQdForm">
      <div class="memberDetailsQdPortrait">
        <div class="memberUploader-img"
             v-if="details.headImg"><img :src="details.headImg"></div>
        <div v-else
             class="memberUploader-icon">暂无</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">姓名</div>
        <div class="memberDetailsQdcontent">{{details.userName}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">性别</div>
        <div class="memberDetailsQdcontent">{{details.sex}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">账号</div>
        <div class="memberDetailsQdcontent">{{details.account}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">民族</div>
        <div class="memberDetailsQdcontent">{{details.nation}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">手机号</div>
        <div class="memberDetailsQdcontent">{{details.mobile}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">代表证号</div>
        <div class="memberDetailsQdcontent">{{details.userNumber}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">出生年月</div>
        <div class="memberDetailsQdcontent">{{details.birthday|datefmt('YYYY/MM/DD')}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">籍贯</div>
        <div class="memberDetailsQdcontent fill">{{details.nativePlace}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">党派</div>
        <div class="memberDetailsQdcontent">{{details.party}}</div>
      </div>
      <div class="memberDetailsQdFormItem points">
        <div class="memberDetailsQdLabel">提名方式</div>
        <div class="memberDetailsQdcontent">{{details.belectedType}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">职称</div>
        <div class="memberDetailsQdcontent">{{details.professional?details.professional.label:''}}</div>
      </div>
      <div class="memberDetailsQdFormItem points">
        <div class="memberDetailsQdLabel">是否常委会组成成员</div>
        <div class="memberDetailsQdcontent">{{details.isRoutineMember}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">所属结构</div>
        <div class="memberDetailsQdcontent">{{details.representerElement}}</div>
      </div>
      <div class="memberDetailsQdFormItem points">
        <div class="memberDetailsQdLabel">代表团</div>
        <div class="memberDetailsQdcontent">{{details.representerTeam}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">学历</div>
        <div class="memberDetailsQdcontent">{{details.education}}</div>
      </div>
      <div class="memberDetailsQdFormItem points">
        <div class="memberDetailsQdLabel">学位</div>
        <div class="memberDetailsQdcontent">{{details.degree}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">毕业院校</div>
        <div class="memberDetailsQdcontent">{{details.school}}</div>
      </div>
      <div class="memberDetailsQdFormItem points">
        <div class="memberDetailsQdLabel">邮政编码</div>
        <div class="memberDetailsQdcontent">{{details.postcode}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">健康状况</div>
        <div class="memberDetailsQdcontent">{{details.health}}</div>
      </div>
      <div class="memberDetailsQdFormItem points">
        <div class="memberDetailsQdLabel">办公电话</div>
        <div class="memberDetailsQdcontent">{{details.officePhone}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">单位及职务</div>
        <div class="memberDetailsQdcontent all">{{details.position}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">通讯地址</div>
        <div class="memberDetailsQdcontent all">{{details.callAddress}}</div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">个人简历</div>
        <div class="memberDetailsQdcontent all">
          <pre>{{details.introduction}}</pre>
        </div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">社会荣誉</div>
        <div class="memberDetailsQdcontent all">
          <pre>{{details.honorInfo}}</pre>
        </div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">社会贡献</div>
        <div class="memberDetailsQdcontent all">
          <pre>{{details.contribute}}</pre>
        </div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">个人备注</div>
        <div class="memberDetailsQdcontent all">
          <pre>{{details.remarks}}</pre>
        </div>
      </div>
      <div class="memberDetailsQdFormItem">
        <div class="memberDetailsQdLabel">是否启用</div>
        <div class="memberDetailsQdcontent">{{details.isUsing}}</div>
      </div>
      <div class="memberDetailsQdFormItem points">
        <div class="memberDetailsQdLabel">接受系统短信</div>
        <div class="memberDetailsQdcontent">{{details.isReceiveMsg}}</div>
      </div>
      <div class="memberDetailsQdFormItem"
           v-if="$isAppShow()">
        <div class="memberDetailsQdLabel">APP安装情况</div>
        <div class="memberDetailsQdcontent all">{{details.isJoinApp}}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'behalfDetails',
  data () {
    return {
      id: this.$route.query.id,
      toId: this.$route.query.toId,
      type: this.$route.query.type,
      memberType: this.$route.query.memberType,
      details: {}
    }
  },
  mounted () {
    if (this.id) {
      if (this.type) {
        this.historymemberInfo()
      } else {
        this.memberDetails()
      }
    }
  },
  methods: {
    async memberDetails () {
      const res = await this.$api.memberInformation.memberDetails({ id: this.id, memberType: this.memberType })
      var { data } = res
      this.details = data
    },
    async historymemberInfo () {
      const res = await this.$api.memberInformation.historymemberInfo(this.id)
      var { data } = res
      this.details = data
    }
  }
}
</script>
<style lang="scss">
.memberDetailsQd {
  width: 1020px;
  background: #ffffff;
  border-radius: 10px;
  margin: 30px auto;
  padding: 0 52px;
  padding-bottom: 52px;
  background: linear-gradient(179deg, #ffffff 0%, #fdfdfe 100%);
  box-shadow: 0px 3px 15px rgba(0, 0, 0, 0.16);
  .memberDetailsQdTitle {
    font-size: 36px;
    font-family: SimSun;
    font-weight: bold;
    color: #ff0000;
    line-height: 118px;
    border-bottom: 4px solid #f00;
    text-align: center;
    margin-bottom: 58px;
    box-sizing: border-box;
    padding-top: 12px;
  }
  .memberDetailsQdForm {
    box-shadow: 1px 0px 0px 0px #ebebeb, -1px 0px 0px 0px #ebebeb,
      0px 0px 0px 0px #ebebeb, 0px -1px 0px 0px #ebebeb;
    .memberDetailsQdPortrait {
      width: 130px;
      height: 159px;
      background: #ffffff;
      transform: translateY(0.1px);
      float: right;
      box-shadow: 0px 0px 0px 0px #ebebeb, -1px 0px 0px 0px #ebebeb,
        0px 1px 0px 0px #ebebeb, 0px 0px 0px 0px #ebebeb;

      .memberUploader-icon {
        color: #8c939d;
        width: 130px;
        height: 159px;
        background: url("../../../../assets/img/userqd.png") no-repeat;
        background-size: 84px 90px;
        background-position: center;
        font-size: 16px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #999999;
        line-height: 159px;
        text-align: center;
      }

      .memberUploader-img {
        width: 130px;
        height: 159px;
        display: flex;
        justify-content: center;
        overflow: hidden;
        img {
          height: 100%;
          display: inline-block;
        }
      }
    }
    .memberDetailsQdFormItem {
      display: inline-block;
      box-shadow: 0px 1px 0px 0px #ebebeb;
      overflow: hidden;
      background: #f5f7fb;
      .memberDetailsQdLabel {
        width: 160px;
        min-height: 52px;
        display: flex;
        align-items: center;
        justify-content: center;
        // line-height: 52px;
        float: left;
        background: #f5f7fb;
        color: #333333;
        font-family: PingFang SC;
        font-weight: 500;
      }
      .memberDetailsQdcontent {
        width: 233px;
        height: 100%;
        min-height: 52px;
        margin-left: 160px;
        background: #ffffff;
        padding: 10px 12px;
        line-height: 32px;
      }
      .fill {
        width: 363px;
      }
      .all {
        width: 756px;
      }
    }
    .points {
      .memberDetailsQdLabel {
        width: 160px;
      }
      .memberDetailsQdcontent {
        width: 363px;
        margin-left: 160px;
      }
    }
    .special {
      .memberDetailsQdLabel {
        width: 280px;
      }
      .memberDetailsQdcontent {
        width: 140px;
        margin-left: 280px;
      }
    }
  }
}
</style>
