<template>
  <div class="newsConfigColumn">
    <div class="module">
      <el-radio-group v-model="module"
                      @change="handleSelect">
        <el-radio v-for="item in moduleList"
                  :key="item.id"
                  :label="item.id">{{item.name}}</el-radio>
      </el-radio-group>
    </div>
    <div class="tree-box">
      <zy-tree :tree="tree"
               v-model="treeId"
               :props="{ children: 'children', label: 'name'}"></zy-tree>
    </div>
    <div class="module-column-btn">
      <el-button type="primary"
                 size="small"
                 @click="handleSubmit">确定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    id: String,
    bigDataId: String,
    moduleId: String
  },
  data () {
    return {
      module: '1',
      moduleList: [],
      treeId: '',
      tree: []
    }
  },
  created () {
    if (this.$system() === '人大') {
      this.moduleList = [
        { name: '综合资讯', id: '1' },
        { name: '代表培训', id: '3' },
        { name: '知情知政', id: '6' }
      ]
    } else {
      this.moduleList = [
        { name: '综合资讯', id: '1' },
        { name: '内容资料', id: '6' }
      ]
    }
    if (this.id) {
      this.treeId = this.id
    }
    if (this.moduleId) {
      this.module = this.moduleId
    }
    this.getTree()
  },
  methods: {
    getTree () {
      this.$api.appManagement.informationColumnTree({ module: this.module }).then(res => {
        this.tree = res.data
      })
    },
    handleSelect () {
      this.getTree()
    },
    handleSubmit () {
      if (!this.treeId) {
        return this.$message.warning('请选择栏目')
      }
      this.$api.smartHelper.editCraw({
        module: this.module,
        bigDataId: this.bigDataId,
        structureId: this.treeId
      }).then(res => {
        this.$message.success('修改配置成功')
        this.$emit('close')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.newsConfigColumn {
  width: 450px;
  height: 450px;
  .module {
    height: 45px;
    padding: 0 30px;
    display: flex;
    align-items: center;
  }
  .tree-box {
    height: calc(100% - 90px);
    overflow-y: hidden;
  }
  .tree-box::-webkit-scrollbar {
    width: 6px;
  }

  .tree-box::-webkit-scrollbar-track {
    border-radius: 6px;
  }

  .tree-box::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.1);
  }
  .module-column-btn {
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
