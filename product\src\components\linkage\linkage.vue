<template>
  <div class="xyl-linkage"
       ref="linkage">
    <el-popover placement="bottom"
                trigger="manual"
                v-model="visible"
                popper-class="xyl-linkage-popover">
      <template slot="reference">
        <el-input readonly
                  class="is-focus"
                  @focus="focus"
                  @blur="blur"
                  @mouseover.native="mouseover"
                  @mouseleave.native="mouseleave"
                  v-model="input"
                  placeholder="请选择">
          <template slot="suffix">
            <i v-if="show"
               :class="['xyl-linkage-icon','el-icon-arrow-down',visible?'el-icon-arrow-down-a':'']"></i>
            <i v-if="!show"
               @click="empty"
               class="el-icon-circle-close"></i>
          </template>
        </el-input>
      </template>
      <div class="xyl-linkage-box"
           :style="{ minWidth: w + 'px' }"
           @mousedown="mousedown">
        <el-scrollbar class="xyl-linkage-scrollbar">
          <div class="xyl-linkage-list">
            <div class="xyl-linkage-item"
                 v-for="item in data"
                 :key="item.id"
                 @click="oneClick(item)"
                 :class="{selected :id==item.id}">{{item[props.label]}}
              <i class="el-icon-arrow-right"
                 v-if="item[props.children].length"></i>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="xyl-linkage-box"
           v-if="children.length"
           :style="{ minWidth: w + 'px' }"
           @mousedown="mousedown">
        <el-scrollbar class="xyl-linkage-scrollbar">
          <div class="xyl-linkage-list">
            <div v-for="item in children"
                 :key="item.id">
              <div class="xyl-linkage-tree"
                   v-if="item[props.children].length">
                <div class="xyl-linkage-title"
                     :class="{selected : id == item.id}"
                     @click="twoClick(item)">{{item[props.label]}} <i class="el-icon-arrow-down"></i></div>
                <el-collapse-transition>
                  <div v-if="twoShow.includes(item.id)">
                    <div class="xyl-linkage-item"
                         v-for="items in item[props.children]"
                         :class="{selected : id == items.id}"
                         @click="threeClick(items,item)"
                         :key="items.id">{{items.name}}</div>
                  </div>
                </el-collapse-transition>
              </div>
              <div class="xyl-linkage-item"
                   :class="{selected : id == item.id}"
                   @click="twoClick(item)"
                   v-else>{{item[props.label]}}</div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </el-popover>
  </div>
</template>
<script>
export default {
  name: 'XylLinkage',
  props: {
    value: [String, Number, Array, Object],
    data: {
      type: Array,
      default: () => []
    },
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label',
          id: 'id'
        }
      }
    }
  },
  data () {
    return {
      w: 0,
      id: this.value,
      oneId: '',
      oneData: {},
      children: [],
      twoId: '',
      twoData: {},
      twoShow: [],
      threeId: '',
      threeData: {},
      visible: false,
      show: true
    }
  },
  computed: {
    input () {
      var text = ''
      if (JSON.stringify(this.oneData) !== '{}') {
        text += this.oneData[this.props.label]
      }
      if (JSON.stringify(this.twoData) !== '{}') {
        text += ` / ${this.twoData[this.props.label]}`
      }
      if (JSON.stringify(this.threeData) !== '{}') {
        text += ` / ${this.threeData[this.props.label]}`
      }
      return text
    }
  },
  mounted () {
    this.w = this.$refs.linkage.offsetWidth
    this.default()
  },
  watch: {
    value (val) {
      this.id = this.value
      if (val) {
        this.default()
      } else {
        this.empty()
      }
    },
    data () {
      this.default()
    }
  },
  methods: {
    deepCopy (data) {
      var t = this.type(data)
      var o
      var i
      var ni
      if (t === 'array') {
        o = []
      } else if (t === 'object') {
        o = {}
      } else {
        return data
      }
      if (t === 'array') {
        for (i = 0, ni = data.length; i < ni; i++) {
          o.push(this.deepCopy(data[i]))
        }
        return o
      } else if (t === 'object') {
        for (i in data) {
          o[i] = this.deepCopy(data[i])
        }
        return o
      }
    },
    type (obj) {
      var toString = Object.prototype.toString
      var map = {
        '[object Boolean]': 'boolean',
        '[object Number]': 'number',
        '[object String]': 'string',
        '[object Function]': 'function',
        '[object Array]': 'array',
        '[object Date]': 'date',
        '[object RegExp]': 'regExp',
        '[object Undefined]': 'undefined',
        '[object Null]': 'null',
        '[object Object]': 'object'
      }
      return map[toString.call(obj)]
    },
    default () {
      var data = this.selected(this.deepCopy(this.data))
      if (data.length) {
        this.defaultSelected(data)
        this.data.forEach(item => {
          if (item[this.props.id] === data[0][this.props.id]) {
            this.children = item[this.props.children]
          }
        })
      }
    },
    defaultSelected (data) {
      data.forEach(item => {
        if (this.oneId && this.twoId === '' && item[this.props.children].length) {
          if (!this.twoShow.includes(item[this.props.id])) {
            this.twoShow.push(item[this.props.id])
          }
        }
        if (this.oneId && this.twoId && this.threeId === '') {
          this.threeId = item[this.props.id]
          this.threeData = item
        }
        if (this.oneId && item[this.props.id] !== this.oneId && this.twoId === '') {
          this.twoId = item[this.props.id]
          this.twoData = item
        }
        if (this.oneId === '') {
          this.oneId = item[this.props.id]
          this.oneData = item
        }
        if (item[this.props.children].length) {
          this.defaultSelected(item[this.props.children])
        } else {
          this.$emit('input', item[this.props.id])
        }
      })
    },
    selected (data) {
      var children = []
      data.forEach(item => {
        var obj = item
        if (item.id === this.value) {
          obj.children = []
          children.push(obj)
        } else {
          obj.children = this.selected(item[this.props.children])
          if (obj.children.length) {
            children.push(obj)
          }
        }
      })
      return children
    },
    focus () {
      this.visible = !this.visible
    },
    blur () {
      this.visible = !this.visible
    },
    mousedown (e) {
      e.preventDefault()
    },
    oneClick (item) {
      if (this.oneId !== item[this.props.id]) {
        this.twoId = ''
        this.twoData = {}
        this.threeId = ''
        this.threeData = {}
      }
      this.$emit('input', item[this.props.id])
      this.oneId = item[this.props.id]
      this.oneData = item
      this.children = item[this.props.children]
    },
    twoClick (item) {
      if (this.twoId !== item[this.props.id]) {
        this.threeId = ''
        this.threeData = {}
      }
      this.$emit('input', item[this.props.id])
      this.twoId = item[this.props.id]
      this.twoData = item
      if (item[this.props.children]) {
        if (this.twoShow.includes(item[this.props.id])) {
          var twoShow = this.twoShow
          this.twoShow = twoShow.filter(row => row !== item[this.props.id])
        } else {
          this.twoShow.push(item[this.props.id])
        }
      }
    },
    threeClick (item, row) {
      this.$emit('input', item[this.props.id])
      this.twoId = row[this.props.id]
      this.twoData = row
      this.threeId = item[this.props.id]
      this.threeData = item
    },
    empty () {
      this.oneId = ''
      this.oneData = {}
      this.children = []
      this.twoId = ''
      this.twoData = {}
      this.twoShow = []
      this.threeId = ''
      this.threeData = {}
      this.$emit('input', '')
    },
    mouseover () {
      if (this.value) {
        this.show = false
      }
    },
    mouseleave () {
      this.show = true
    }
  }
}
</script>
<style lang="scss">
.xyl-linkage {
  width: 100%;
  height: 40px;
  min-width: 222px;

  .el-input__suffix {
    width: 25px;
    line-height: 40px;
    text-align: center;
  }

  .xyl-linkage-icon {
    transition-duration: 0.4s;
    transform: rotate(0);
  }

  .el-icon-arrow-down-a {
    transition-duration: 0.4s;
    transform: rotate(-180deg);
  }
}
.xyl-linkage-popover {
  padding: 0 !important;
  display: flex;
  .xyl-linkage-box {
    min-width: 222px;
    .xyl-linkage-list {
      padding: 6px 0;
      .xyl-linkage-item {
        min-height: 40px;
        line-height: 40px;
        padding-left: 22px;
        padding-right: 52px;
        position: relative;
        cursor: pointer;
        &:hover {
          background-color: $zy-withColor;
        }
        .el-icon-arrow-right {
          position: absolute;
          top: 50%;
          right: 26px;
          transform: translate(50%, -50%);
        }
      }
      .xyl-linkage-tree {
        min-height: 40px;
        line-height: 40px;
        .xyl-linkage-title {
          min-height: 40px;
          line-height: 40px;
          padding-left: 22px;
          padding-right: 52px;
          position: relative;
          cursor: pointer;
          &:hover {
            background-color: $zy-withColor;
          }
          .el-icon-arrow-down {
            position: absolute;
            top: 50%;
            right: 26px;
            transform: translate(50%, -50%);
          }
        }
        .xyl-linkage-item {
          padding-left: 36px;
          padding-right: 52px;
        }
      }
      .selected {
        color: $zy-color;
        font-weight: bold;
        .el-icon-arrow-right {
          font-weight: bold;
        }
      }
    }
  }
  .xyl-linkage-box + .xyl-linkage-box {
    border-left: 1px solid #e6e6e6;
  }
}
.xyl-linkage-scrollbar {
  width: 100%;
  height: 258px;
  max-height: 258px;
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .is-vertical {
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.8);
    }
  }
}
</style>
