<template>
  <div class="table-box">
    <zy-table>
      <el-table :data="list"
                ref="table"
                slot="zytable">
        <el-table-column type="index"
                         label="序号"
                         width="80"></el-table-column>
        <el-table-column label="姓名"
                         min-width="180"
                         prop="userName"
                         show-overflow-tooltip></el-table-column>
        <el-table-column label="选项"
                         min-width=" 320"
                         prop="optionName"
                         show-overflow-tooltip></el-table-column>
        <el-table-column label="投票时间"
                         min-width=" 300"
                         prop="date"></el-table-column>
      </el-table>
    </zy-table>
  </div>
</template>

<script>
export default {
  props: { id: String },
  data () {
    return {
      list: []
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.$api.appManagement.pollOptionsCountJoinMan(this.id).then(res => {
        this.list = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-box {
  width: 800px;
  height: 450px;
}
</style>
