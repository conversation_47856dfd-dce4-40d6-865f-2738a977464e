<template>
  <div class="newsConfig">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input v-model="keyword"
                  placeholder="请输入关键词"></el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="handleRefreshRule">刷新规则</el-button>
        <!-- <el-button @click="handleBatchEnable">批量启动</el-button>
      <el-button @click="handleBatchBan">批量禁用</el-button> -->
        <!-- <el-button @click="triggerClick">批量触发</el-button> -->
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="list"
                  borders
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="50"></el-table-column>
          <el-table-column label="任务名称"
                           prop="bigDataId"
                           min-width="300"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="域名"
                           prop="webSite"
                           min-width="150"></el-table-column>
          <!-- <el-table-column label="数据更新频率" prop="rate" min-width="180"></el-table-column> -->
          <el-table-column label="任务运行状态"
                           min-width="150">
            <template slot-scope="scope">{{scope.row.enable ===  1? '运行':'停止' }}</template>
          </el-table-column>
          <el-table-column label="最近更新时间"
                           prop="lastModifyTime"
                           min-width="240"></el-table-column>
          <el-table-column label="今日抓取数量"
                           prop="todayNum"
                           min-width="180"></el-table-column>
          <el-table-column label="对应栏目"
                           min-width="150">
            <template slot-scope="scope">
              <el-button type="text"
                         size="default"
                         @click="handleConfigColumn(scope.row)">
                {{scope.row.structureName || '配置栏目'}}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           width="150">
            <template slot-scope="scope">
              <!-- <el-button type="text"
                         @click="handleEnable(scope.row.bigDataId)">启用</el-button>
              <el-button type="text"
                         @click="handleBan(scope.row.bigDataId)">禁用</el-button> -->
              <el-button type="text"
                         @click="triggerId(scope.row)">手动抓取</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="pageNo"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="isColumn"
               title="配置栏目">
      <newsConfigColumn :id="structureId"
                        :bigDataId="bigDataId"
                        :moduleId="moduleId"
                        @close="handleCallback"></newsConfigColumn>
    </zy-pop-up>
  </div>
</template>

<script>
import tableData from '@/mixins/tableData'
import newsConfigColumn from './widget/newsConfigColumn'
export default {
  mixins: [tableData],
  components: { newsConfigColumn },
  data () {
    return {
      keyword: '',
      isColumn: false,
      structureId: null,
      bigDataId: null,
      moduleId: '',
      pageNo: 1,
      pageSize: this.$pageSize(),
      list: [],
      total: 0
    }
  },
  created () {
    this.value = 'structureId'
    this.getList()
  },
  methods: {
    getList () {
      const params = {
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      if (this.keyword !== '') {
        params.keyword = this.keyword
      }
      this.$api.smartHelper.crawlList(params).then(res => {
        this.list = res.data
        this.total = res.total
      })
    },
    whatPage (val) {
      this.getList()
    },
    howManyArticle (val) {
      this.getList()
    },
    search () {
      if (this.keyword === '') {
        return this.$message.warning('请输入想要搜索的关键字')
      }
      this.page = 1
      this.getList()
    },
    reset () {
      this.keyword = ''
      this.getList()
    },
    handleRefreshRule () {
      this.$confirm('此操作将刷新规则, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.reset()
      }).catch(() => {
        return false
      })
    },
    triggerClick () {
      if (this.choose.length === 0) {
        return this.$message.warning('请选择想要启用的项!')
      }
      this.trigger(this.choose.join(','))
    },
    triggerId (row) {
      this.trigger(row.structureId)
    },
    async trigger (id) {
      const res = await this.$api.smartHelper.trigger({
        structureId: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.getList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    handleBatchEnable () {
      if (this.choose.length === 0) {
        return this.$message.warning('请选择想要启用的项!')
      }
      this.handleEnable(this.choose.join(','))
    },
    handleBatchBan () {
      if (this.choose.length === 0) {
        return this.$message.warning('请选择想要禁用的项!')
      }
      this.handleBan(this.choose.join(','))
    },
    handleEnable (ids) {
      this.$confirm('此操作将启用选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleUse(ids, 1)
      }).catch(() => {
        return false
      })
    },
    handleBan (ids) {
      this.$confirm('此操作将禁用用选中的项, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleUse(ids, 0)
      }).catch(() => {
        return false
      })
    },
    handleUse (ids, enable) {
      this.$api.smartHelper.crawlIsEnable({ bigDataIds: ids, enable: enable }).then(res => {
        this.$message.success('修改成功')
        this.getList()
      })
    },
    // 配置栏目
    handleConfigColumn (row) {
      this.structureId = row.structureId
      this.bigDataId = row.bigDataId
      this.moduleId = row.module
      this.isColumn = true
    },
    handleCallback () {
      this.getList()
      this.isColumn = false
    }
  }
}
</script>
<style lang="scss" scoped>
.newsConfig {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
