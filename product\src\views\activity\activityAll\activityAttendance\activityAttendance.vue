<template>
  <div class="activityAttendance details">
    <div class="details-title">考勤详情</div>
    <div class="details-item-box">
      <div class="details-item">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.meetName}}</div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">活动类型</div>
          <div class="details-item-value">{{details.meetType}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">报名截止时间</div>
          <div class="details-item-value">{{details.signEndTime}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">活动开始时间</div>
          <div class="details-item-value">{{details.meetStartTime}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">组织部门</div>
          <div class="details-item-value">{{details.org}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">开始签到时间</div>
          <div class="details-item-value">{{details.meetSignBeginTime}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">活动结束时间</div>
          <div class="details-item-value">{{details.meetEndTime}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">地点</div>
        <div class="details-item-value">{{details.address}}</div>
      </div>
      <!-- <div class="details-item">
        <div class="details-item-label">标签</div>
        <div class="details-item-value">{{details.label}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">邀请人({{details.inviterNum||0}})</div>
        <div class="details-item-value activityUser">{{details.inviterNames}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">报名人({{details.signUpNum||0}})</div>
        <div class="details-item-value activityUser">{{details.signUpNames}}</div>
      </div> -->
      <div class="details-item">
        <div class="details-item-label">签到人({{details.signInNum||0}})</div>
        <div class="details-item-value activityUser">{{details.signInNames}}</div>
      </div>
      <!-- <div class="details-item">
        <div class="details-item-label">未报名({{details.noSignUpNum||0}})</div>
        <div class="details-item-value activityUser">{{details.noSignUpNames}}</div>
      </div> -->
    </div>
  </div>
</template>
<script>
export default {
  name: 'activityAttendance',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.activityGetMeetingListAttendance()
  },
  methods: {
    async activityGetMeetingListAttendance () {
      const res = await this.$api.activity.activityGetMeetingListAttendance(this.id)
      var { data } = res
      this.details = data
    }
  }
}
</script>
<style lang="scss">
.activityAttendance {
  padding: 24px;
  .activityUser {
    text-overflow: clip !important;
    white-space: normal !important;
  }
}
</style>
