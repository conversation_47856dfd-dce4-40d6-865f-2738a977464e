// 书库分类管理
const libraryType = () => import('@/views/academy/library/libraryType')
// 书库管理
const libraryAll = () => import('@/views/academy/library/libraryAll')
// 总书库
const totalLibrary = () => import('@/views/academy/library/totalLibrary')
// 滚动推荐
const ScrollRecommend = () => import('@/views/academy/library/ScrollRecommend')
// 新增书籍
const booksNew = () => import('@/views/academy/library/booksNew')
// 读书公告
const academyAnnouncement = () => import('@/views/academy/academyAnnouncement/academyAnnouncement')
// 推荐阅读
const recommendedReading = () => import('@/views/academy/recommendedReading/recommendedReading')
// 推荐阅读混合排序
const recommendedReadingSort = () => import('@/views/academy/recommendedReading/recommendedReadingSort')
// 精彩读书笔记登选
const readingNotes = () => import('@/views/academy/readingNotes/readingNotes')
// 书籍金玉良言选登
const goodWords = () => import('@/views/academy/goodWords/goodWords')
// 统计分析
const academyStatistical = () => import('@/views/academy/academyStatistical/academyStatistical')
// 个人读书管理
// 我的书架
const myBookcase = () => import('@/views/academy/myReading/myBookcase')
// 读书笔记
const myReadingNotes = () => import('@/views/academy/myReading/myReadingNotes')
// 交流群
const academyGroup = () => import('@/views/academy/academyGroup/academyGroup')
// 交流群消息收藏
const academyGroupMessage = () => import('@/views/academy/academyGroup/academyGroupMessage')
// 读书活动
const readingActivity = () => import('@/views/academy/readingActivity/readingActivity')

const academy = [
  {
    path: '/readingActivity',
    name: 'readingActivity',
    component: readingActivity
  },
  {
    path: '/libraryType',
    name: 'libraryType',
    component: libraryType
  },
  {
    path: '/libraryAll',
    name: 'libraryAll',
    component: libraryAll
  },
  {
    path: '/totalLibrary',
    name: 'totalLibrary',
    component: totalLibrary
  },
  {
    path: '/booksNew',
    name: 'booksNew',
    component: booksNew
  },
  {
    path: '/academyAnnouncement',
    name: 'academyAnnouncement',
    component: academyAnnouncement
  },
  {
    path: '/recommendedReading',
    name: 'recommendedReading',
    component: recommendedReading
  },
  {
    path: '/recommendedReadingSort',
    name: 'recommendedReadingSort',
    component: recommendedReadingSort
  },
  {
    path: '/readingNotes',
    name: 'readingNotes',
    component: readingNotes
  },
  {
    path: '/goodWords',
    name: 'goodWords',
    component: goodWords
  },
  {
    path: '/academyStatistical',
    name: 'academyStatistical',
    component: academyStatistical
  },
  {
    path: '/myBookcase',
    name: 'myBookcase',
    component: myBookcase
  },
  {
    path: '/myReadingNotes',
    name: 'myReadingNotes',
    component: myReadingNotes
  },
  {
    path: '/ScrollRecommend',
    name: 'ScrollRecommend',
    component: ScrollRecommend
  },
  {
    path: '/academyGroup',
    name: 'academyGroup',
    component: academyGroup
  },
  {
    path: '/academyGroupMessage',
    name: 'academyGroupMessage',
    component: academyGroupMessage
  }
]
export default academy
