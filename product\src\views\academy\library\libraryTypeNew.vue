<template>
  <div class="libraryTypeNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="名称"
                    prop="name"
                    class="form-input">
        <el-input placeholder="请输入名称"
                  v-model="form.name"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上级"
                    prop="parentId"
                    class="form-input">
        <zy-select width="296"
                   node-key="id"
                   v-model="form.parentId"
                   :props=" { children: 'children', label: 'name' }"
                   :data="parentData"
                   placeholder="请选择上级"></zy-select>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input">
        <el-input placeholder="请输入排序"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="上传主题图"
                    class="form-img">
        <el-upload class="form-img-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="fileImg.filePath"
               :src="fileImg.filePath"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'libraryTypeNew',
  data () {
    return {
      form: {
        name: '',
        parentId: '',
        sort: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        parentId: [
          { required: true, message: '请选择上级', trigger: 'blur' }
        ]
      },
      fileImg: {},
      parentData: []
    }
  },
  props: ['id', 'parentId'],
  created () {
    this.getSyTypeTree()
    this.form.parentId = this.parentId
    if (this.id) {
      this.syTypeInfo()
    }
  },
  methods: {
    async getSyTypeTree () {
      const res = await this.$api.academy.getSyTypeTree({})
      var { data } = res
      var arr = [{
        attachmentList: [],
        children: [],
        id: '0',
        isShow: 0,
        isTop: 0,
        name: '所有',
        parentId: '0',
        parentName: '',
        sort: 0
      }]
      this.parentData = arr.concat(data)
    },
    async syTypeInfo () {
      const res = await this.$api.academy.syTypeInfo({ id: this.id })
      var { data } = res
      this.form.name = data.name
      this.form.parentId = data.parentId
      this.form.sort = data.sort
      if (data.coverImg) {
        this.fileImg = { id: data.coverImg, filePath: data.coverImgUrl }
      }
    },
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.fileImg = data[0]
      }).catch(() => {
        files.onError()
      })
    },
    /**
   * 提交提案
  */
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/syType/add?'
          if (this.id) {
            url = '/syType/edit?'
          }
          this.$api.general.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            name: this.form.name,
            parentId: this.form.parentId,
            sort: this.form.sort,
            coverImg: this.fileImg.id
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.libraryTypeNew {
  width: 682px;

  .form-img {
    width: 100%;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
