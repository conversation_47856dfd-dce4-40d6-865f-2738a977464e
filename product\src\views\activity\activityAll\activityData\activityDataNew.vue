<template>
  <div class="activityDataNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item class="form-title"
                    prop="title">
        <span slot="label"
              class="label-button">资料名称 <span style="color: red;">(最多25字)</span>
        </span>
        <el-input placeholder="请输入资料名称"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="是否app显示"
                    v-if="$isAppShow()"
                    prop="isAppShow"
                    class="form-input">
        <el-radio-group v-model="form.isAppShow">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上传原图"
                    class="form-img">
        <el-upload class="form-img-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="fileImg.filePath"
               :src="fileImg.filePath"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="上传活动资料"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleFile"
                   :http-request="fileUpload"
                   :file-list="fileData"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-ue">
        <UEditor v-model="form.content"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'activityDataNew',
  data () {
    return {
      form: {
        title: '',
        isAppShow: 1,
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入资料名称', trigger: 'blur' }
        ]
      },
      fileImg: [],
      fileData: []
    }
  },
  props: ['id', 'meetId'],
  mounted () {
    if (this.id) {
      this.materiainfoInfo()
    }
  },
  methods: {
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'materiainfoPhoto')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.fileImg = data[0]
      }).catch(() => {
        files.onError()
      })
    },
    handleFile (file, fileList) {
    },
    fileUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'materiainfoFile')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.fileData.push(data[0])
      }).catch(() => {
        files.onError()
      })
    },
    beforeRemove (file, fileList) {
      var fileData = this.fileData
      this.fileData = fileData.filter(item => item.id !== file.id)
    },
    async materiainfoInfo () {
      const res = await this.$api.activity.materiainfoInfo(this.id)
      var { data } = res
      this.form.title = data.materialName
      this.form.isAppShow = data.isAppShow
      this.form.content = data.content
      if (data.attachId) {
        data.attachId.forEach((item, index) => {
          item.uid = item.id
          item.name = item.fileName
        })
        this.fileData = data.attachId
      }
      if (data.photo) {
        this.fileImg = data.photo[0] || {}
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var attachmentIds = []
          if (this.fileImg.id) {
            attachmentIds.push(this.fileImg.id)
          }
          this.fileData.forEach(item => {
            attachmentIds.push(item.id)
          })
          let url = '/materiainfo/add?'
          if (this.id) {
            url = '/materiainfo/edit?'
          }
          this.$api.activity.activityscheduleAddorEdit(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            materialName: this.form.title,
            content: this.form.content,
            meetId: this.meetId,
            isAppShow: this.form.isAppShow,
            attachmentIds: attachmentIds.join(',')
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.activityDataNew {
  width: 682px;
  .form-img {
    width: 100%;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: $zy-color;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
