<template>
  <div class="generalCurrentBehalf">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="representerElement"
                   clearable
                   placeholder="请选择所属结构">
          <el-option v-for="item in representerElementData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="representerTeam"
                   clearable
                   :disabled="disabled"
                   placeholder="请选择代表团">
          <el-option v-for="item in representerTeamData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="hasVacant"
                   clearable
                   v-if="type=='1'"
                   placeholder="请选择是否出缺">
          <el-option v-for="item in hasVacantData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="vacantType"
                   clearable
                   v-if="type=='present'"
                   placeholder="请选择出缺类型">
          <el-option v-for="item in vacantTypeData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="party"
                   clearable
                   placeholder="请选择党派">
          <el-option v-for="item in partyData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="nation"
                   clearable
                   placeholder="请选择民族">
          <el-option v-for="item in nationData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="sex"
                   clearable
                   placeholder="请选择性别">
          <el-option v-for="item in sexData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="isUsing"
                   clearable
                   placeholder="请选择状态">
          <el-option v-for="item in isUsingData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="startBirthday"
                   clearable
                   placeholder="请选择起始年龄段">
          <el-option v-for="item in Birthday"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="endBirthday"
                   clearable
                   placeholder="请选择结束年龄段">
          <el-option v-for="item in Birthday"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button type="primary"
                   v-permissions="has +'dels'"
                   @click="deleteClick">删除</el-button>
        <el-button type="primary"
                   v-permissions="has +'resetpwd'"
                   @click="resetPassword">重置密码</el-button>
        <!-- <el-button type="primary"
                   v-permissions="has +'changejc'"
                   @click="transition">换届</el-button> -->
        <el-button v-permissions="has +'open'"
                   type="primary"
                   @click="stateClick(1)">启用</el-button>
        <el-button v-permissions="has +'stop'"
                   type="primary"
                   @click="stateClick(0)">禁用</el-button>
        <el-button @click="present"
                   type="primary"
                   v-permissions="has +'vacany'">出缺</el-button>
        <el-button @click="nopresent"
                   type="primary"
                   v-permissions="has +'noVacany'">取消出缺</el-button>
        <el-button type="primary"
                   v-permissions="has +'import'"
                   @click="importShow = !importShow">excel导入</el-button>
        <el-button @click="exportMethods">导出excel</el-button>
        <el-button @click="exportMD"
                   v-permissions="has +'exportMD'">导出代表名单</el-button>
        <el-button @click="exportMC"
                   v-permissions="has +'exportMC'">导出代表名册</el-button>
        <el-button @click="SendMail"
                   v-permissions="has +'SendMail'">导出代表发邮</el-button>
        <el-button @click="exportPhoto"
                   v-permissions="has +'exportPhoto'">导出代表照片</el-button>
        <el-button v-permissions="has +'samenet'"
                   @click="synchronous">同步代表</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="姓名"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="details(scope.row)"
                         type="text">{{scope.row.userName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column :label="'代表证号'"
                           width="160"
                           prop="userNumber"></el-table-column>
          <el-table-column label="性别"
                           width="80"
                           prop="sex"></el-table-column>
          <el-table-column :label="'代表团'"
                           width="160"
                           prop="representerTeam"></el-table-column>
          <el-table-column label="所属结构"
                           width="160"
                           prop="representerElement"></el-table-column>
          <el-table-column label="所属代表工作站"
                           min-width="140"
                           v-if="findUserStationName"
                           prop="stationName"></el-table-column>
          <el-table-column label="出生年月"
                           width="160">
            <template slot-scope="scope">{{scope.row.birthday|datefmt('YYYY-MM')}}</template>
          </el-table-column>
          <el-table-column label="民族"
                           width="120"
                           prop="nation"></el-table-column>
          <el-table-column label="手机号码"
                           width="160"
                           prop="mobile"></el-table-column>
          <el-table-column label="届"
                           width="80"
                           prop="circlesName"></el-table-column>
          <el-table-column label="次"
                           width="80"
                           prop="boutName"></el-table-column>
          <el-table-column label="出缺类型"
                           min-width="190"
                           v-if="type=='present'"
                           prop="vacantType"></el-table-column>
          <el-table-column label="状态"
                           min-width="120"
                           prop="isUsing">
            <template slot-scope="scope">
              {{scope.row.isUsing==1?'正常':'被撤销资格'}}
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           v-if="$hasPermission([has +'edit',has +'record'])"
                           width="200">
            <template slot-scope="scope">
              <el-button @click="perfectClick(scope.row)"
                         type="primary"
                         v-permissions="has +'edit'"
                         plain
                         size="mini">编辑</el-button>
              <el-button @click="recordClick(scope.row)"
                         type="primary"
                         v-permissions="has +'record'"
                         plain
                         size="mini">编辑记录</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <zy-pop-up v-model="presentShow"
               title="出缺">
      <presentBehalf :id="ids"
                     :memberType="memberType"
                     @newCallback="presentCallback"> </presentBehalf>
    </zy-pop-up>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export systemType="2"
                 :params="params"
                 :excelId="excelId"
                 :type="type=='present'?'10':memberType"
                 @callback="callback"></zy-export>
    </zy-pop-up>
    <zy-pop-up v-model="importShow"
               title="导入">
      <xyl-template-import :data="{systemType: '2'}"
                           :params="importParams"
                           templateUrl="/member/importemplate"
                           uploadUrl="/member/import"></xyl-template-import>
    </zy-pop-up>
    <zy-pop-up v-model="recordShow"
               title="编辑记录">
      <editorRecord :userId="id"
                    type="2"></editorRecord>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import exportWord from '@/mixins/exportWord'
import behalf from '../behalf'
import presentBehalf from './presentBehalf/presentBehalf'
import editorRecord from '@/views/system-settings/user-management/editorRecord/editorRecord'
export default {
  name: 'generalCurrentBehalf',
  data () {
    return {
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      ids: '',
      presentShow: false,
      exportShow: false,
      excelId: '',
      params: {},
      importShow: false,
      importParams: [{ name: 'memberType', value: this.memberType }, { name: 'systemType', value: '2' }],
      findUserStationName: false,
      recordShow: false
    }
  },
  mixins: [tableData, behalf, exportWord],
  components: {
    presentBehalf,
    editorRecord
  },
  inject: ['newTab'],
  props: ['memberType', 'has', 'type'],
  mounted () {
    this.readonfig()
    if (this.type === 'present') {
      this.hasVacant = 1
    }
    this.dictionaryPubkvs()
    if (JSON.stringify(this.$route.query) !== '{}') {
      for (var i in this.$route.query) {
        this[i] = this.$route.query[i]
      }
    }
    this.memberList()
  },
  activated () {
    this.readonfig()
    if (JSON.stringify(this.$route.query) !== '{}') {
      for (var i in this.$route.query) {
        this[i] = this.$route.query[i]
      }
    }
    this.memberList()
  },
  methods: {
    search () {
      this.page = 1
      this.memberList()
    },
    async readonfig () {
      const res = await this.$api.general.readonfig({ codes: 'findUserStationName' })
      var { data } = res
      if (data.findUserStationName === '1') {
        this.findUserStationName = true
      }
    },
    async synchronous () {
      const res = await this.$api.memberInformation.same()
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.memberList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    perfectClick (row) {
      this.newTab({ name: '编辑代表信息', menuId: row.id, to: '/behalfNew', params: { id: row.id, memberType: this.memberType } })
    },
    async memberList () {
      const res = await this.$api.memberInformation.memberList({
        pageNo: this.page,
        pageSize: this.pageSize,
        memberType: this.memberType,
        keyword: this.keyword,
        representerElement: this.representerElement,
        representerTeam: this.representerTeam,
        vacantType: this.vacantType,
        hasVacant: this.hasVacant,
        party: this.party,
        nation: this.nation,
        sex: this.sex,
        startBirthday: this.startBirthday,
        endBirthday: this.endBirthday,
        isUsing: this.isUsing
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    details (row) {
      this.newTab({ name: '代表信息详情', menuId: '16620092811', to: '/behalfDetails', params: { id: row.id, memberType: this.memberType } })
    },
    howManyArticle (val) {
      this.memberList()
    },
    whatPage (val) {
      this.memberList()
    },
    nopresent () {
      if (this.choose.length) {
        this.$confirm('此操作将会把选中的代表取消出缺, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.nobatchOut(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async nobatchOut (id) {
      const res = await this.$api.memberInformation.nobatchOut({
        memberType: this.memberType,
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.memberList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    present () {
      if (this.choose.length) {
        this.ids = this.choose.join(',')
        this.presentShow = true
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    presentCallback () {
      this.choose = []
      this.selectObj = []
      this.memberList()
      this.presentShow = false
    },
    transition () {
      this.$confirm('此操作将会把本届代表换届, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.changejc()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重置'
        })
      })
    },
    async changejc () {
      const res = await this.$api.memberInformation.changejc({
        memberType: this.memberType
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.memberList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    resetPassword () {
      if (this.choose.length) {
        this.$confirm('此操作将重置该代表的密码, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.memberResetpwd(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消重置'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async memberResetpwd (id) {
      const res = await this.$api.memberInformation.memberResetpwd({
        memberType: this.memberType,
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.memberList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    stateClick (type) {
      if (this.choose.length) {
        if (type) {
          this.memberStartuse(this.choose.join(','))
        } else {
          this.memberStopuse(this.choose.join(','))
        }
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async memberStartuse (id) {
      const res = await this.$api.memberInformation.memberStartuse({
        memberType: this.memberType,
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.memberList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async memberStopuse (id) {
      const res = await this.$api.memberInformation.memberStopuse({
        memberType: this.memberType,
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.memberList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    exportMD () {
      this.$api.memberInformation.exportnames({ memberType: this.memberType })
    },
    exportMC () {
      this.$api.memberInformation.exportnamelist({ memberType: this.memberType })
    },
    SendMail () {
      if (this.choose.length) {
        this.memberSendMail(this.choose.join(','))
      } else {
        this.memberSendMail()
      }
    },
    async memberSendMail (ids) {
      var datas = {
        pageNo: 1,
        pageSize: this.total,
        memberType: this.memberType,
        keyword: this.keyword,
        representerElement: this.representerElement,
        representerTeam: this.representerTeam,
        vacantType: this.vacantType,
        hasVacant: this.hasVacant,
        party: this.party,
        nation: this.nation,
        sex: this.sex,
        startBirthday: this.startBirthday,
        endBirthday: this.endBirthday,
        isUsing: this.isUsing
      }
      if (ids) {
        datas = {
          pageNo: 1,
          pageSize: 999,
          memberType: this.memberType,
          ids: ids
        }
      }
      const res = await this.$api.memberInformation.memberList(datas)
      var { data } = res
      var arr = []
      var obj = {
        oneName: '',
        onePostCode: '',
        oneCallAddress: '',
        twoName: '',
        twoPostCode: '',
        twoCallAddress: ''
      }
      data.forEach(item => {
        if (obj.oneName === '') {
          obj.oneName = `${item.userName}  代表` || ''
          obj.onePostCode = item.postCode || ''
          obj.oneCallAddress = item.callAddress || ''
        } else if (obj.twoName === '') {
          obj.twoName = `${item.userName}  代表` || ''
          obj.twoPostCode = item.postCode || ''
          obj.twoCallAddress = item.callAddress || ''
          arr.push(obj)
          obj = {
            oneName: '',
            onePostCode: '',
            oneCallAddress: '',
            twoName: '',
            twoPostCode: '',
            twoCallAddress: ''
          }
        }
      })
      if (obj.oneName) {
        arr.push(obj)
      }
      this.exportWord('word/behalfSendMail.docx', '代表发邮模板', { data: arr })
    },
    exportPhoto () {
      if (this.choose.length) {
        this.$confirm('此操作将导出当前选中的代表照片, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$api.memberInformation.downloadHeadImg({ ids: this.choose.join(','), memberType: this.memberType })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消重置'
          })
        })
      } else {
        this.$confirm('当前没有选中的数据将会按照筛选条件导出所有数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.exportPhotoList()
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消重置'
          })
        })
      }
    },
    async exportPhotoList () {
      var datas = {
        pageNo: 1,
        pageSize: this.total,
        memberType: this.memberType,
        keyword: this.keyword,
        representerElement: this.representerElement,
        representerTeam: this.representerTeam,
        vacantType: this.vacantType,
        hasVacant: this.hasVacant,
        party: this.party,
        nation: this.nation,
        sex: this.sex,
        startBirthday: this.startBirthday,
        endBirthday: this.endBirthday,
        isUsing: this.isUsing
      }
      const res = await this.$api.memberInformation.memberList(datas)
      var { data } = res
      const ids = data.map((v) => v.id).join(',')
      this.$api.memberInformation.downloadHeadImg({ ids: ids, memberType: this.memberType })
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除该代表, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.memberDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async memberDel (id) {
      const res = await this.$api.memberInformation.memberDel({
        memberType: this.memberType,
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.memberList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    recordClick (row) {
      this.id = row.id
      this.recordShow = true
    }
  }
}
</script>
<style lang="scss">
.generalCurrentBehalf {
  height: 100%;
  width: 100%;

  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
