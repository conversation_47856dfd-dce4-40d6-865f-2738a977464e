<template>
  <div v-if="isShow"
       class="urban-linkage-manage">
    <el-form-item>
      <el-select v-model="province"
                 clearable
                 placeholder="省级"
                 @change="changeProvince">
        <el-option v-for="item in provinceList"
                   :key="item.id"
                   :label="item.label"
                   :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-select v-model="city"
                 clearable
                 placeholder="市级"
                 @change="changeCity">
        <el-option v-for="item in cityList"
                   :key="item.id"
                   :label="item.label"
                   :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-select v-model="region"
                 clearable
                 placeholder="区级"
                 @change="changeRegion">
        <el-option v-for="item in regionList"
                   :key="item.id"
                   :label="item.label"
                   :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-select v-model="street"
                 clearable
                 placeholder="街道"
                 @change="changeStreet">
        <el-option v-for="item in streetList"
                   :key="item.id"
                   :label="item.label"
                   :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
  </div>
  <div class="urban-linkage"
       v-else>
    <el-form-item prop="province">
      <el-select v-model="province"
                 clearable
                 placeholder="省级"
                 @change="changeProvince">
        <el-option v-for="item in provinceList"
                   :key="item.id"
                   :label="item.label"
                   :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item prop="city">
      <el-select v-model="city"
                 clearable
                 placeholder="市级"
                 @change="changeCity">
        <el-option v-for="item in cityList"
                   :key="item.id"
                   :label="item.label"
                   :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item prop="region">
      <el-select v-model="region"
                 clearable
                 placeholder="区级"
                 @change="changeRegion">
        <el-option v-for="item in regionList"
                   :key="item.id"
                   :label="item.label"
                   :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item prop="street">
      <el-select v-model="street"
                 clearable
                 placeholder="街道"
                 @change="changeStreet">
        <el-option v-for="item in streetList"
                   :key="item.id"
                   :label="item.label"
                   :value="item.id">
        </el-option>
      </el-select>
    </el-form-item>
  </div>
</template>
<script>
export default {
  name: 'urbanLinkage',
  data () {
    return {
      data: [],
      province: '', // 省
      city: '', // 市
      region: '', // 区
      street: '', // 行政区
      provinceList: [],
      cityList: [],
      regionList: [],
      streetList: [],
      disabled: false,
      emptys: '', // 根据这个字段，编辑时，重新选完市，清掉县
      emptysStree: '' // 根据这个字段，编辑时，重新选完区、县,清掉街道
    }
  },
  mounted () {
    this.areaTree()
    // this.provinceList = this.data // 省
  },
  // props: ['ids', 'provinces', 'citys', 'regions', 'streets', 'isShow', 'defaults'],
  props: {
    ids: {
      type: String,
      default: ''
    },
    provinces: {
      type: String,
      default: ''
    },
    citys: {
      type: String,
      default: ''
    },
    regions: {
      type: String,
      default: ''
    },
    streets: {
      type: String,
      default: ''
    },
    isShow: {
      type: Boolean,
      default: false
    },
    defaults: {
      type: Boolean,
      default: false
    },
    proCityfalg: {
      type: Boolean,
      default: true
    },
    end: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    async areaTree () {
      const res = await this.$api.SmallModule.areaTree()
      var { data } = res
      this.data = data
      this.provinceList = data
      this.emptys = this.city
      this.emptysStree = this.region
      if (this.ids) {
        this.changeProvince()
        this.changeCity()
        this.changeRegion()
      }
      if (this.defaults) {
        this.changeProvince()
        this.changeCity()
        this.changeRegion()
      }
    },
    changeProvince () {
      for (var key of this.data) {
        if (key.id === this.province) {
          this.cityList = key.children
          // this.city = key.children[0].name
        }
      }
      this.changes()
    },
    changeCity () {
      this.cityList.forEach((item, index) => {
        if (item.id === this.city) {
          this.regionList = item.children
        }
        if (this.emptys !== this.city) {
          console.log(11)
          this.region = ''
          this.street = ''
        }
      })
      this.changes()
    },
    changeRegion () {
      this.regionList.forEach((item1, index1) => {
        if (item1.id === this.region) {
          this.streetList = item1.children
        }
        if (this.emptysStree !== this.region) {
          this.street = ''
        }
      })
      this.changes()
    },
    changeStreet (val) {
      this.street = val
      this.changes()
    },
    changes () {
      this.$emit('change', this.province, this.city, this.region, this.street)
    }
  },
  watch: {
    ids (val) {
      if (val) {
        this.disabled = true
      }
    },
    provinces (val) {
      this.province = val
    },
    citys (val) {
      this.city = val
    },
    regions (val) {
      this.region = val
    },
    streets (val) {
      this.street = val
    },
    defaults (val) {
      // this.province = JSON.parse(sessionStorage.getItem('user' + this.$logo())).areaId // 默认系统的省
      this.areaTree()
    },
    end () {
      this.areaTree()
    }

  }
}
</script>
<style lang="scss">
.urban-linkage {
  display: inline-block;
  .el-select {
    margin-right: 10px;
  }
  .el-select:last-child {
    margin-right: 0;
  }
  .el-input__inner {
    height: 40px !important;
  }
  .el-form-item {
    width: 22%;
    margin-right: 20px !important;
  }
}
.workstation-supervise-box {
  display: inline-block;
  position: relative;
  .el-form-item {
    margin-bottom: 0;
  }
}
.urban-linkage-manage {
  width: 82%;
  float: left;
}
</style>
