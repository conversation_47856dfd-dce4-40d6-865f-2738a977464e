// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile,
  fileRequest,
  filedownload
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const proposal = {
  proposalfile (params) {
    return postform('/attachment/uploadFile', params, { timeout: 80000 })
  },
  downloadFile (params, text) {
    fileRequest('/attachment/downloadFile?', params, text)
  },
  // 届次编号
  circleBoutInfoList (params) {
    return post('/submission/proposal/circleBoutInfo/list', params)
  },
  circleBoutInfoInfo (params) {
    return post(`/submission/proposal/circleBoutInfo/info/${params}`)
  },
  // 届次年
  yearRelationList (params) {
    return post('/submission/proposal/circleboutyear/list', params)
  },
  yearRelationInfo (params) {
    return post(`/submission/proposal/circleboutyear/info/${params}`)
  },
  yearRelationDel (params) {
    return post('/submission/proposal/circleboutyear/dels?', params)
  },
  // 提案分类
  proposalTypeList (params) {
    return post('/submission/proposal/topic/list?', params)
  },
  proposalTypeInfo (params) {
    return post(`/submission/proposal/topic/info/${params}`)
  },
  // 提案业务用户关系
  businessUser (params) {
    return post('/submission/proposal/businessUser/list', params)
  },
  businessUserdels (params) {
    return post('/submission/proposal/businessUser/dels', params)
  },
  enumData (params) {
    return post('/submission/proposal/businessUser/enumData', params)
  },
  enumDatainfo (params) {
    return post(params)
  },
  businessUserInfo (params) {
    return post(`/submission/proposal/businessUser/info/${params}`)
  },
  // 获取提案分类和主题词
  chooseList (params) {
    return post('/submission/proposal/topic/chooseList', params)
  },
  // 获取当前届次
  currentCircleAndBout (params) {
    return post('/submission/proposal/circleBoutInfo/currentCircleAndBout', params)
  },
  // 流程配置列表
  flowconfigList (params) {
    return post('/flowconfig/proposal/list', params)
  },
  // 流程配置详情
  flowconfigInfo (params) {
    return post(`/flowconfig/proposal/info/${params}`)
  },
  // 流程配置删除
  flowconfigDel (params) {
    return post(`/flowconfig/proposal/del/${params}`)
  },
  // 流程配置获取
  getConfigMap (params) {
    return post('/flowconfig/proposal/getConfigMap', params)
  },
  // 所有提案列表
  proposalList (params) {
    return post('/proposal/list', params)
  },
  // 提案基础详情
  proposalInfo (params) {
    return post(`/proposal/info/${params}`)
  },
  // 提案委删除提案
  powerDelete (params) {
    return post('/proposal/powerDelete', params)
  },
  // 委员删除提案
  memberDelete (params) {
    return post('/proposal/memberDelete', params)
  },
  // 我领衔的提案
  myProposalList (params) {
    return post('/proposal/myProposalList', params)
  },
  // 我联名的提案
  myJoinProposalList (params) {
    return post('/proposal/myJoinProposalList', params)
  },
  // 我的草稿提案
  myDraftsProposalList (params) {
    return post('/proposal/myDraftsProposalList', params)
  },
  // 获取提案枚举值
  getOperationList (params) {
    return post('/proposal/getOperationList', params)
  },
  // 解锁审查提案
  unlockProposal (params) {
    return post('/proposal/unlockProposal', params)
  },
  // 获取提案审查详情
  auditDetail (params) {
    return post('/proposal/auditDetail', params)
  },
  // 待专委会审查提案
  committeeAuditList (params) {
    return post('/proposal/committeeAuditList', params)
  },
  // 待审查提案
  examineList (params) {
    return post('/proposal/examineList', params)
  },
  // 待复审提案
  reviewList (params) {
    return post('/proposal/reviewList', params)
  },
  // 待审定提案
  lastAuditList (params) {
    return post('/proposal/lastAuditList', params)
  },
  // 不予立案提案
  rejectList (params) {
    return post('/proposal/rejectList', params)
  },
  // 转社情民意提案
  socialList (params) {
    return post('/proposal/socialList', params)
  },
  // 撤案提案
  banList (params) {
    return post('/proposal/banList', params)
  },
  // 转来信提案
  referList (params) {
    return post('/proposal/referList', params)
  },
  // 政协交办中列表
  ZXAssignList (params) {
    return post('/proposal/ZXAssignList', params)
  },
  // 政府交办中列表
  ZFAssignList (params) {
    return post('/proposal/ZFAssignList', params)
  },
  // 党委交办中列表
  DWAssignList (params) {
    return post('/proposal/DWAssignList', params)
  },
  // 两院交办中列表
  LYAssignList (params) {
    return post('/proposal/LYAssignList', params)
  },
  // 法院交办中列表
  FYAssignList (params) {
    return post('/proposal/FYAssignList', params)
  },
  // 检察院交办中列表
  JCYAssignList (params) {
    return post('/proposal/JCYAssignList', params)
  },
  // 交办详情
  assignDetail (params) {
    return post('/proposal/assignDetail', params)
  },
  // 提案批量二次交办
  batchSecondAssign (params) {
    return post('/proposal/batchSecondAssign', params)
  },
  // 提案批量交办办理单位
  batchAssignProposal (params) {
    return post('/proposal/batchAssignProposal', params)
  },
  // 提案委待签收
  allPreAssignList (params) {
    return post('/proposal/allPreAssignList', params)
  },
  // 提案委申请调整
  preAssignReviseList (params) {
    return post('/proposal/preAssignReviseList', params)
  },
  // 单位待签收列表
  groupPreAssignList (params) {
    return post('/proposal/groupPreAssignList', params)
  },
  // 单位申请调整列表
  groupPreAssignReviseList (params) {
    return post('/proposal/groupPreAssignReviseList', params)
  },
  // 单位申请调整列表
  groupPreAssignHistoryList (params) {
    return post('/proposal/groupPreAssignHistoryList', params)
  },
  // 提案委审查预交办申请调整提案
  auditReviseTransact (params) {
    return post('/proposal/auditReviseTransact', params)
  },
  // 提案委办理中提案
  allTransactList (params) {
    return post('/proposal/allTransactList', params)
  },
  // 办理中提案详情
  transactProposalDetail (params) {
    return post('/proposal/transactProposalDetail', params)
  },
  // 办理单位办理中提案
  groupTransactList (params) {
    return post('/proposal/groupTransactList', params)
  },
  // 办理单位办理中提案更新内部流程
  updateTransactInnerStatus (params) {
    return post('/proposal/updateTransactInnerStatus', params)
  },
  // 办理单位办理中提案申请调整办理单位
  exchangeTransact (params) {
    return post('/proposal/exchangeTransact', params)
  },
  // 办理单位办理中提案申请延期
  saveFlowDelay (params) {
    return post('/proposal/saveFlowDelay', params)
  },
  // 办理单位办理中提案提案沟通情况列表
  flowContactList (params) {
    return post('/proposal/flowContactList', params)
  },
  // 办理单位办理中提案增加沟通情况提案沟通联系人
  contactPersonList (params) {
    return post('/proposal/contactPersonList', params)
  },
  // 可添加沟通情况的办理信息
  contactTransactList (params) {
    return post('/proposal/contactTransactList', params)
  },
  // 提案委删除办理单位沟通交流
  superDeleteFlowContact (params) {
    return post('/proposal/superDeleteFlowContact', params)
  },
  // 办理单位办理中提案增加沟通情况
  saveFlowContact (params) {
    return post('/proposal/saveFlowContact', params)
  },
  // 办理单位办理中提案保存答复件
  saveFlowAnswer (params) {
    return post('/proposal/saveFlowAnswer', params)
  },
  // 答复件详情(点击查看)
  proposalAnswerDetail (params) {
    return post('/proposal/proposalAnswerDetail', params)
  },
  // 答复件详情(点击添加答复件)
  flowAnswerDetail (params) {
    return post('/proposal/flowAnswerDetail', params)
  },
  // 提案委已答复列表
  allAnsweredProposalList (params) {
    return post('/proposal/allAnsweredProposalList', params)
  },
  // 办理单位已答复列表
  groupAnsweredProposalList (params) {
    return post('/proposal/groupAnsweredProposalList', params)
  },
  // 办理单位申请跟踪办理
  requestTractAnswer (params) {
    return post('/proposal/requestTractAnswer', params)
  },
  // 提案重新办理
  reTransact (params) {
    return post('/proposal/reTransact', params)
  },
  // 提案委单个办结提案
  finishProposal (params) {
    return post('/proposal/finishProposal', params)
  },
  // 提案委批量办结提案
  batchFinishProposal (params) {
    return post('/proposal/batchFinishProposal', params)
  },
  // 提案委已办结提案列表
  allFinishProposalList (params) {
    return post('/proposal/allFinishProposalList', params)
  },
  // 办理单位已办结提案列表
  groupFinishProposalList (params) {
    return post('/proposal/groupFinishProposalList', params)
  },
  // 申请延期办理单位列表
  flowDelayList (params) {
    return post('/proposal/flowDelayList', params)
  },
  // 申请调整办理单位列表
  flowBackList (params) {
    return post('/proposal/flowBackList', params)
  },
  // 延期审查
  flowDelayAudit (params) {
    return post('/proposal/flowDelayAudit', params)
  },
  // 调整审查
  auditExchangeTransact (params) {
    return post('/proposal/auditExchangeTransact', params)
  },
  // 提案委跟踪办理列表
  allTrackProposalList (params) {
    return post('/proposal/allTrackProposalList', params)
  },
  // 办理单位跟踪办理列表
  groupTrackProposalList (params) {
    return post('/proposal/groupTrackProposalList', params)
  },
  // 提案委审查跟踪办理
  auditRequestTractAnswer (params) {
    return post('/proposal/auditRequestTractAnswer', params)
  },
  // 办理中历史申请调整
  groupChangeList (params) {
    return post('/proposal/groupChangeList', params)
  },
  // 委员确认联名关系
  confirmJoinSubmit (params) {
    return post('/proposal/confirmJoinSubmit', params)
  },
  // 满意度测评详情
  flowEvaluateDetail (params) {
    return post('/proposal/flowEvaluateDetail', params)
  },
  // 保存满意度测评
  saveFlowEvaluate (params) {
    return post('/proposal/saveFlowEvaluate', params)
  },
  // 保存提案质量评议(提案质量评议表)
  saveFlowGroupEvaluate (params) {
    return post('/proposal/saveFlowGroupEvaluate', params)
  },
  // 超管满意度测评列表
  flowEvaluateList (params) {
    return post('/proposal/flowEvaluateList', params)
  },
  // 超管新增满意度测评
  superAddFlowEvaluate (params) {
    return post('/proposal/superAddFlowEvaluate', params)
  },
  // 超管编辑满意度测评
  superEditFlowEvaluate (params) {
    return post('/proposal/superEditFlowEvaluate', params)
  },
  // 超管删除满意度测评
  superDeleteFlowEvaluate (params) {
    return post('/proposal/superDeleteFlowEvaluate', params)
  },
  // 超管同意所有未操作联名数据
  superAgreeJoinSubmit (params) {
    return post('/proposal/superAgreeJoinSubmit', params)
  },
  // 统计分析
  proposalCount (params) {
    return post('/proposal/proposalCount', params)
  },
  // 设置/取消 重点提案/公开提案/优秀提案
  batchUpdateProposal (params) {
    return post('/proposal/batchUpdateProposal', params)
  },
  // 管理员/提案委 真实删除联名关系
  realDeleteJoinUser (params) {
    return post('/proposal/realDeleteJoinUser', params)
  },
  // 管理员/提案委 添加联名关系
  addAgreeJoinUser (params) {
    return post('/proposal/addAgreeJoinUser', params)
  },
  // 管理员修改办理单位
  superEditTransactGroup (params) {
    return post('/proposal/superEditTransactGroup', params)
  },
  // 超管获取办理单位列表
  flowTransactList (params) {
    return post('/proposal/flowTransact/list', params)
  },
  // 超管获取办理单位列表详情
  flowTransactInfo (params) {
    return post(`/proposal/flowTransact/info/${params}`)
  },
  // 超管删除办理单位
  flowTransactDel (params) {
    return post(`/proposal/flowTransact/del/${params}`)
  },
  // 超管获取答复件列表
  answerList (params) {
    return post('/proposal/answerList', params)
  },
  // 可添加办理答复件的办理单位
  answerTransactList (params) {
    return post('/proposal/answerTransactList', params)
  },
  // 超管保存和编辑答复件
  superSaveFlowAnswer (params) {
    return post('/proposal/superSaveFlowAnswer', params)
  },
  // 超管删除答复件
  superDeleteFlowAnswer (params) {
    return post('/proposal/superDeleteFlowAnswer', params)
  },
  // 可添加跟踪办理答复件的办理单位
  tractAnswerTransactList (params) {
    return post('/proposal/tractAnswerTransactList', params)
  },
  // 提案word批量导出数据
  wordExport (params) {
    return post('/proposal/wordExport', params)
  },
  // 根据条件查询ids
  findQueryIds (params) {
    return post('/proposal/findQueryIds', params)
  },
  // 导出word的答复件
  exportReplyFile (params) {
    return filedownload('/proposal/exportFlowAnswerByProposal?', params, 'arraybuffer')
  },
  // 办理中催办查看
  batchUrgeView (params) {
    return post('/proposal/batchUrgeView', params)
  },
  // 办理中催办答复
  batchUrgeAnswer (params) {
    return post('/proposal/batchUrgeAnswer', params)
  },
  // 催办满意度测评(已答复建议催代表)
  batchUrgeEvaluate (params) {
    return post('/proposal/batchUrgeEvaluate', params)
  },
  // 提案并案
  mergeProposal (params) {
    return post('/proposal/mergeProposal', params)
  },
  // 提案已并案列表
  mergedProposalList (params) {
    return post('/proposal/mergedProposalList', params)
  },
  // 取消并案
  batchCancelMerge (params) {
    return post('/proposal/batchCancelMerge', params)
  },
  // 提案线索库列表
  clueList (params) {
    return post('/submission/clue/list?', params)
  },
  // 提案线索库详情
  clueInfo (params) {
    return post(`/submission/clue/info/${params}`)
  },
  // 提案线索库删除
  clueDel (params) {
    return post('/submission/clue/dels', params)
  },
  // 提案评价新增
  updateTransactEvaluateInfo (params) {
    return post('/proposal/updateTransactEvaluateInfo', params)
  },
  // 提案评价列表
  findTransactEvaluates (params) {
    return post('/proposal/findTransactEvaluates', params)
  },
  // 导出办理单位状态
  importGroupStatusCount (params) {
    exportFile('/proposal/importGroupStatusCount?', params)
  },
  // 退回政协交办
  batchBackAssign (params) {
    return post('/proposal/batchBackAssign', params)
  },
  // 案号对调
  exchangeCustomNum (params) {
    return post('/proposal/exchangeCustomNum', params)
  },
  // 单位所有提案
  allTransactProposalList (params) {
    return post('/proposal/allTransactProposalList', params)
  },
  // 判断是否集体单位账号
  Collectivenit (params) {
    return post('/proposal/hasIsJtOrg?', params)
  },
  // 查询集体委员虚拟用户与真实用户关联关系列表
  teamMemberRelationList (params) {
    return post('/proposal/teamMemberRelation/list', params)
  },
  // 保存集体委员虚拟用户与真实用户关联关系
  teamMemberRelationSave (params) {
    return post('/proposal/teamMemberRelation/save', params)
  },
  // 获取申请列表
  getApply (params) {
    return post('/secondreply/list', params)
  },
  // 申请修该答复件
  applyModifying (params) {
    return post('/secondreply/add', params)
  },
  // 审核
  applyAudit (params) {
    return post('/secondreply/edit', params)
  },
  lookAuditIdea (params) {
    return post(`/secondreply/look/${params}`)
  }
}
export default proposal
