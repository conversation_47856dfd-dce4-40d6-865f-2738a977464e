<template>
  <div class="zy-exportStudy scrollBar">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="导出字段："
                    prop="field"
                    class="form-title">
        <el-checkbox-group v-model="form.field">
          <el-checkbox v-for="item in field"
                       :label="item.id"
                       :key="item.id">{{item.key}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>

    <div class="progress-box"
         v-if="show"
         @click.stop>
      <div class="progress">
        <el-progress :percentage="percentage"
                     :color="customColor"></el-progress>
      </div>
    </div>
  </div>
</template>
<script>
import exportExcel from '@/mixins/exportExcel'
export default {
  name: 'zyExportStudy',
  data () {
    return {
      name: '',
      percentage: 0,
      customColor: '#94070A',
      form: {
        field: []
      },
      rules: {
        field: [
          { required: true, message: '请选择导出Excel的字段', trigger: 'blur' }
        ]
      },
      field: [],
      data: [],
      show: false,
      dataShow: false,
      exporttypes: false
    }
  },
  props: {
    type: [String, Number, Array, Object],
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    excelId: {
      type: String,
      default: ''
    },
    systemType: {
      type: String,
      default: ''
    }
  },
  mixins: [exportExcel],
  created () {
    this.exportFields()
    this.exportDatas()
  },
  watch: {
    data (val) {
      if (val.length && this.dataShow) {
        this.exportExcelMethods()
      }
    }
  },
  methods: {
    async exportFields () {
      const res = await this.$api.general.exportFields({ exportType: this.type, systemType: this.systemType })
      res.data.fields.forEach((item, index) => {
        item.id = index
        if (item.checked) {
          this.form.field.push(index)
        }
      })
      this.name = res.data.name
      this.field = res.data.fields
    },
    async exportDatas () {
      var params = {}
      if (!this.excelId) {
        params = this.params
      } else {
        params.ids = this.excelId
      }
      params.exportType = this.type
      const res = await this.$api.SmallModule.groupMessageStatistics(params)
      this.exporttypes = true
      this.data = res.data
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.show = true
          this.setInterval = setInterval(() => {
            if (this.percentage >= 90) {
              clearInterval(this.setInterval)
            } else {
              this.percentage = this.percentage + 9
            }
          }, 200)
          if (this.data.length || this.exporttypes) {
            setTimeout(() => {
              this.exportExcelMethods()
            }, 400)
          } else {
            this.dataShow = true
          }
        } else {
          this.$message({
            message: '导出字段不能为空！',
            type: 'warning'
          })
          return false
        }
      })
    },
    exportExcelMethods () {
      this.percentage = 100
      var field = []
      this.form.field.forEach(item => {
        field.push(this.fieldMethods(item))
      })
      this.exportExcel(this.name, field, this.data)
      clearInterval(this.setInterval)
      setTimeout(() => {
        this.show = false
        this.exporttypes = false
        this.percentage = 0
        setTimeout(() => {
          this.$emit('callback')
        }, 222)
      }, 1000)
    },
    fieldMethods (id) {
      var arr = {}
      this.field.forEach(item => {
        if (item.id === id) {
          arr = item
        }
      })
      return arr
    },
    resetForm (formName) {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
@import "./zy-exportStudy.scss";
</style>
