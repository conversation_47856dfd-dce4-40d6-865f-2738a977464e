<template>
  <div class="zy-sliding-box"
       ref="zySlidingBox">
    <div class="zy-sliding-left"
         v-if="show&&offset>0||offset!=0"
         @click.stop="slidingLeft"><i class="el-icon-d-arrow-left"></i></div>
    <div class="zy-sliding-right"
         v-if="show&&offset<biggest"
         @click.stop="slidingRight"><i class="el-icon-d-arrow-right"></i></div>
    <div class="zy-sliding-box-s">
      <div class="zy-sliding-item-list"
           ref="zySlidingItemList">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script>
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
export default {
  name: 'zySlidingBox',
  data () {
    return {
      show: false,
      offset: 0,
      biggest: 0
    }
  },
  props: {
    shift: {
      type: Number,
      default: 168
    }
  },
  mounted () {
    this.$nextTick(() => {
      const that = this
      erd.listenTo(this.$refs.zySlidingBox, (element) => {
        that.$nextTick(() => {
          that.biggestClick()
        })
      })
      erd.listenTo(this.$refs.zySlidingItemList, (element) => {
        that.$nextTick(() => {
          that.biggestClick()
        })
      })
    })
  },
  methods: {
    biggestClick () {
      var tabBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-box-s')
      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')
      if (itemBox.offsetWidth > tabBox.offsetWidth) {
        this.show = true
        this.biggest = itemBox.offsetWidth - tabBox.offsetWidth
      } else {
        this.show = false
      }
    },
    slidingLeft () {
      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')
      var offset = this.offset - this.shift
      if (this.offset - this.shift <= 0) {
        offset = 0
      }
      itemBox.style.transform = `translateX(-${offset}px)`
      itemBox.style.transitionDuration = '.4s'
      this.offset = offset
    },
    slidingRight () {
      var itemBox = this.$refs.zySlidingBox.querySelector('.zy-sliding-item-list')
      var offset = this.offset + this.shift
      if (this.biggest < this.offset + this.shift) {
        offset = this.biggest
      }
      itemBox.style.transform = `translateX(-${offset}px)`
      itemBox.style.transitionDuration = '.4s'
      this.offset = offset
    }
  },
  beforeDestroy () {
    erd.uninstall(this.$refs.zySlidingBox)
    erd.uninstall(this.$refs.zySlidingItemList)
  }
}
</script>
<style lang="scss">
@import "./zy-sliding-box.scss";
</style>
