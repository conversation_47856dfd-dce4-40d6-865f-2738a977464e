<template>
  <div class="poll-options-add">
    <el-form :model="form"
             ref="form"
             :rules="rules"
             label-position="top">
      <el-form-item label="选项"
                    prop="name">
        <el-input v-model="form.name"
                  placeholder="请输入选项"></el-input>
      </el-form-item>
      <el-form-item label="选项说明">
        <el-input v-model="form.instructions"
                  placeholder="请输入选项说明"
                  type="textarea"
                  rows="3"></el-input>
      </el-form-item>
      <el-form-item label="选项附件">
        <upload v-model="form.files"
                module="pollOtions" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary"
                   @click="onSubmit('form')">立即提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import upload from '@/views/office/File-center/send-file-approval/widget/upload.vue'
import _ from 'lodash'
export default {
  components: { upload },
  props: {
    id: String,
    pollId: String
  },
  data () {
    return {
      form: {
        name: '',
        instructions: '',
        files: []
      },
      rules: {
        name: [{ required: true, message: '请输入选项', trigger: 'blur' }]
      }
    }
  },
  created () {
    this.getInfo()
  },
  methods: {
    getInfo () {
      if (!this.id) return
      this.$api.appManagement.pollOptionsInfo(this.id).then(res => {
        const { name, instructions, attachmentList } = res.data
        this.form = {
          name: name,
          instructions: instructions,
          files: attachmentList.filter(v => v.moduleType === 'pollOtions').map(v => {
            return {
              name: v.fileName,
              size: v.fileSize,
              type: v.fileType,
              url: v.filePath,
              id: v.id,
              uid: v.uid
            }
          })
        }
      })
    },
    // 提交
    onSubmit (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const data = _.cloneDeep(this.form)
          data.attachmentIds = this.form.files.map(v => v.id).join(',')
          data.voteId = this.pollId
          delete data.files
          if (this.id) {
            data.id = this.id
            this.$api.appManagement.pollOptionsEdit(data).then(() => {
              this.$message.success('编辑投票选项成功')
              this.$emit('callback')
            })
          } else {
            this.$api.appManagement.pollOptionsAdd(data).then(() => {
              this.$message.success('新增投票选项成功')
              this.$emit('callback')
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.poll-options-add {
  width: 600px;
  padding: 0 15px;
}
</style>
