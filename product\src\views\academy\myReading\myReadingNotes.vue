<template>
  <div class="myReadingNotes">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-date-picker v-model="time"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
      </template>
      <template slot="button">
        <el-button @click="deleteClick"
                   type="primary">删除</el-button>
        <el-button @click="addeditors('0')"
                   type="primary">批量公开</el-button>
        <el-button @click="addeditors('1')"
                   type="primary">批量转私密</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="标记"
                           width="120">
            <template slot-scope="scope">
              <i v-if="scope.row.isPrivacy == 0"
                 class="el-icon-lock"></i>
            </template>
          </el-table-column>
          <el-table-column label="笔记内容"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.noteContent}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="点赞数"
                           width="100"
                           prop="likenum"></el-table-column>
          <el-table-column label="关联书籍"
                           width="190">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="library(scope.row.bookId)">{{scope.row.bookName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="笔记截图"
                           width="160">
            <template slot-scope="scope">
              <div class="table-img">
                <el-image style="width: 100%; height: 100%"
                          :src="scope.row.screenshotUrl"
                          :preview-src-list="[scope.row.screenshotUrl]">
                </el-image>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="笔记创建时间"
                           width="190"
                           prop="createDate"></el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="addeditor(scope.row)"
                         type="primary"
                         plain
                         size="mini">{{scope.row.isPrivacy == 0?'公开':'转私密'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="libraryShow"
                      title="书籍详情">
      <libraryDetails :id="id"> </libraryDetails>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      title="笔记详情">
      <myReadingNotesDetails :id="id"> </myReadingNotesDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import libraryDetails from '../library/libraryDetails'
import myReadingNotesDetails from './myReadingNotesDetails'
export default {
  name: 'myReadingNotes',
  data () {
    return {
      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),
      keyword: '',
      time: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      detailsShow: false,
      libraryShow: false
    }
  },
  mixins: [tableData],
  components: {
    libraryDetails,
    myReadingNotesDetails
  },
  mounted () {
    this.syReadingNotesList()
  },
  methods: {
    search () {
      this.page = 1
      this.syReadingNotesList()
    },
    reset () {
      this.keyword = ''
      this.time = ''
      this.syReadingNotesList()
    },
    library (row) {
      this.id = row
      this.libraryShow = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    async syReadingNotesList () {
      const res = await this.$api.academy.syReadingNotesList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        isMine: true,
        beginDate: this.time ? this.time[0] : '',
        endDate: this.time ? this.time[1] : ''
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
    },
    howManyArticle () {
      this.syReadingNotesList()
    },
    whatPage () {
      this.syReadingNotesList()
    },
    addeditor (row) {
      if (row.isPrivacy === 0) {
        this.delsOpen(row.id)
      } else {
        this.delsPrivacy(row.id)
      }
    },
    addeditors (type) {
      if (this.choose.length) {
        this.$confirm(`此操作将${type === '1' ? '撤销' : '选登'}当前选中的笔记, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if (type === '1') {
            this.delsPrivacy(this.choose.join(','))
          } else {
            this.delsOpen(this.choose.join(','))
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async delsOpen (id) {
      const res = await this.$api.academy.delsOpen({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.syReadingNotesList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async delsPrivacy (id) {
      const res = await this.$api.academy.delsPrivacy({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.syReadingNotesList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的笔记, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.syReadingNotesDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async syReadingNotesDel (id) {
      const res = await this.$api.academy.syReadingNotesDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.syReadingNotesList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.myReadingNotes {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
    .el-icon-lock {
      font-size: 20px;
    }
    .table-img {
      height: 38px;
      overflow: hidden;
      .el-image__inner {
        width: auto;
      }
      .el-icon-circle-close {
        color: #fff;
      }
    }
  }
}
</style>
