import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
const delay = (function () {
  let timer = 0
  return function (callback, ms) {
    clearTimeout(timer)
    timer = setTimeout(callback, ms)
  }
})()
export default {
  name: 'XylSliding',
  props: {
    value: [String, Number, Array, Object],
    distance: {
      type: Number,
      default: 52
    }
  },
  render () {
    const prev = this.prevShow ? (<div class="xyl-sliding-prev" on-click={this.prevClick}><i class="el-icon-caret-left"></i></div>) : null
    const next = this.nextShow ? (<div class="xyl-sliding-next" on-click={this.nextClick}><i class="el-icon-caret-right"></i></div>) : null
    const bar = (<div class="xyl-sliding-active-bar" style={this.barStyle}></div>)
    return (<div class="xyl-sliding" ref='sliding'>
      <div class="xyl-sliding-wrap">
        <div class="xyl-sliding-scroll" ref='scroll' style={this.scrollStyle}>{bar}{this.$slots.default}</div>
        {prev}{next}
      </div>
    </div>)
  },
  computed: {
    panesActive: {
      get () {
        let active = null
        this.panes.forEach(item => {
          if (this.value === item.value) {
            active = item
          }
        })
        return active
      }
    },
    barStyle: {
      get () {
        const style = {}
        const Width = this.panesActive ? this.panesActive.$el.clientWidth : 0
        const offset = this.panesActive ? this.panesActive.$el.offsetLeft : 0
        const transform = `translateX(${offset}px)`
        style.width = `${Width}px`
        style.transform = transform
        style.msTransform = transform
        style.webkitTransform = transform
        return style
      }
    },
    scrollStyle: {
      get () {
        const style = {}
        const transform = `translateX(${this.scrollLeft}px)`
        style.transform = transform
        style.msTransform = transform
        style.webkitTransform = transform
        return style
      }
    }
  },
  data () {
    return {
      panes: [],
      prevShow: false,
      nextShow: false,
      scrollLeft: 0
    }
  },
  mounted () {
    this.$nextTick(() => {
      const that = this
      erd.listenTo(that.$refs.sliding, (element) => {
        that.$nextTick(() => {
          that.scrollLeft = 0
          that.slidingUpdateRef(that.panesActive)
        })
      })
    })
    this.calcPaneInstances()
    this.obtainActive()
  },
  updated () {
    this.calcPaneInstances()
  },
  watch: {
    value (val) {
      this.obtainActive()
    }
  },
  methods: {
    calcPaneInstances (isForceUpdate = false) {
      if (this.$slots.default) {
        const paneSlots = this.$slots.default.filter(vnode => vnode.tag && vnode.componentOptions && vnode.componentOptions.Ctor.options.name === 'XylSlidingItem')
        const panes = paneSlots.map(({ componentInstance }) => componentInstance)
        const panesChanged = !(panes.length === this.panes.length && panes.every((pane, index) => pane === this.panes[index]))
        if (isForceUpdate || panesChanged) {
          this.panes = panes
        }
      } else if (this.panes.length !== 0) {
        this.panes = []
      }
    },
    prevClick () {
      const left = this.$refs.sliding.offsetWidth - this.$refs.scroll.offsetWidth
      this.scrollLeft = this.scrollLeft + this.distance > 0 ? 0 : this.scrollLeft + this.distance
      delay(() => {
        this.prevShow = this.scrollLeft !== 0
        this.nextShow = this.scrollLeft !== left
      }, 520)
    },
    nextClick () {
      const left = this.$refs.sliding.offsetWidth - this.$refs.scroll.offsetWidth
      this.scrollLeft = this.scrollLeft - this.distance < left ? left : this.scrollLeft - this.distance
      delay(() => {
        this.prevShow = this.scrollLeft !== 0
        this.nextShow = this.scrollLeft !== left
      }, 520)
    },
    slidingUpdate (e, value) {
      this.$emit('sliding-click', e, value)
    },
    obtainActive () {
      if (this.panesActive) {
        this.$nextTick(() => {
          this.slidingUpdateRef(this.panesActive)
        })
        this.$emit('sliding-click', this.panesActive, this.value)
        return
      }
      setTimeout(() => {
        this.obtainActive()
      }, 520)
    },
    slidingUpdateRef (e) {
      if (!e) {
        return
      }
      const left = this.$refs.sliding.offsetWidth - this.$refs.scroll.offsetWidth
      const width = this.$refs.sliding.offsetWidth
      const offset = e.$el.offsetLeft
      if (this.$refs.sliding.offsetWidth > this.$refs.scroll.offsetWidth) {
        this.scrollLeft = 0
        delay(() => {
          this.prevShow = false
          this.nextShow = false
        }, 520)
        return
      }
      if (width - (offset + e.$el.offsetWidth + 38) <= this.scrollLeft || width - (offset + e.$el.offsetWidth + 38) - 222 <= this.scrollLeft) {
        var is = width - (offset + e.$el.offsetWidth + 38) - 222
        if (width - (offset + e.$el.offsetWidth + 38) - 222 <= left) {
          is = width - (offset + e.$el.offsetWidth + 38)
        }
        this.scrollLeft = width - (offset + e.$el.offsetWidth + 38) - 222 <= left ? left : is
        delay(() => {
          this.prevShow = this.scrollLeft !== 0
          this.nextShow = this.scrollLeft !== left
        }, 520)
      }
      if (-offset > this.scrollLeft || -offset + 222 > this.scrollLeft) {
        var negative = -offset + 222
        if (-offset + 222 > 0) {
          negative = 0
        }
        this.scrollLeft = -offset > 0 ? 0 : negative
        delay(() => {
          this.prevShow = this.scrollLeft !== 0
          this.nextShow = this.scrollLeft !== left
        }, 520)
      }
    }
  }
}
