import Vue from 'vue'
import smartConfirm from './smartConfirm.vue'
const ConfirmConstructor = Vue.extend(smartConfirm)
let instance
const initInstance = ({ title, message, defineText, cancelText }) => {
  instance = new ConfirmConstructor({
    el: document.createElement('div'),
    data() {
      return {
        title,
        message,
        defineText,
        cancelText
      }
    }
  })
}
const showConfirm = obj => {
  return new Promise((reslove, reject) => { // eslint-disable-line
    initInstance(obj)
    instance.callback = action => {
      if (action === 'confirm') {
        reslove()
      } else if (action === 'cancel') {
        reject() // eslint-disable-line
      }
    }
    document.body.appendChild(instance.$el)
    Vue.nextTick(() => {
      instance.visible = true
    })
  })
}
function registryConfirm () {
  Vue.prototype.$smartConfirm = showConfirm
}
export default registryConfirm
