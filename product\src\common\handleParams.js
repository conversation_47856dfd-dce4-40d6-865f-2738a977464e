export const filterParams = object => {
  for (const i in object) {
    if (object[i] === null || object[i] === undefined || object[i] === '') {
      delete object[i]
    }
  }
  return object
}

export const checkParams = (object, extra) => {
  const arr = extra ? [...extra] : []
  for (const i in object) {
    if (object[i] === null || object[i] === undefined || object[i] === '') {
      arr.push(false)
    } else {
      arr.push(true)
    }
  }
  return arr.some(v => v)
}

export default {
  filterParams,
  checkParams
}
