<template>
  <div class="behalfAudit">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="auditStatus"
                   filterable
                   clearable
                   placeholder="请选择审核状态">
          <el-option v-for="item in auditStatusData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="deleteClick">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="姓名"
                           prop="userName"
                           width="120">
          </el-table-column>
          <el-table-column label="代表证号"
                           width="160"
                           prop="userNumber"></el-table-column>
          <el-table-column label="性别"
                           width="90"
                           prop="sex"></el-table-column>
          <el-table-column label="代表团"
                           width="160"
                           prop="representerTeam"></el-table-column>
          <el-table-column label="所属结构"
                           width="160"
                           prop="representerElement"></el-table-column>
          <el-table-column label="出生年月"
                           width="160">
            <template slot-scope="scope">{{scope.row.birthday|datefmt('YYYY-MM')}}</template>
          </el-table-column>
          <el-table-column label="民族"
                           min-width="120"
                           prop="nation"></el-table-column>
          <el-table-column label="手机号码"
                           min-width="190"
                           prop="mobile"></el-table-column>
          <!-- <el-table-column label="状态"
                           min-width="120"
                           prop="isUsing">
            <template slot-scope="scope">
              {{scope.row.isUsing==1?'正常':'被撤销资格'}}
            </template>
          </el-table-column> -->
          <el-table-column label="提交时间"
                           width="190">
            <template slot-scope="scope">{{scope.row.createTime|datefmt()}}</template>
          </el-table-column>
          <el-table-column label="状态"
                           min-width="120"
                           prop="auditStatus">
            <!-- <template slot-scope="scope">
              {{scope.row.hasAudit?'已审核':'未审核'}}
            </template> -->
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="120">
            <template slot-scope="scope">
              <el-button type="primary"
                         @click="editor(scope.row)"
                         plain
                         size="mini"> {{scope.row.hasAudit?'查看':'审核'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               :title="$position()+'审核'">
      <behalfAuditDetails :id="id"
                          :hasAudit="hasAudit"
                          @callback="callback"> </behalfAuditDetails>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import behalfAuditDetails from './behalfAuditDetails'
export default {
  name: 'behalfAudit',
  data () {
    return {
      keyword: '',
      auditStatus: '',
      auditStatusData: [],
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      hasAudit: null,
      show: false
    }
  },
  mixins: [tableData],
  components: {
    behalfAuditDetails
  },
  mounted () {
    this.auditStatus = this.$route.params.auditStatus
    this.dictionaryPubkvs()
    this.userauditList()
  },
  methods: {
    search () {
      this.page = 1
      this.userauditList()
    },
    reset () {
      this.keyword = ''
      this.auditStatus = ''
      this.userauditList()
    },
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'user_audit_status'
      })
      var { data } = res
      this.auditStatusData = data.user_audit_status
    },
    async userauditList () {
      const res = await this.$api.memberInformation.userauditList({
        systemType: '2',
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        auditStatus: this.auditStatus
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.userauditList()
    },
    whatPage (val) {
      this.userauditList()
    },
    editor (row) {
      this.id = row.id
      this.hasAudit = row.hasAudit
      this.show = true
    },
    callback () {
      this.show = false
      this.userauditList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除该' + this.$position() + '信息审核数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.userauditDels(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async userauditDels (id) {
      const res = await this.$api.memberInformation.userauditDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.userauditList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.behalfAudit {
  width: 100%;
  height: 100%;

  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
