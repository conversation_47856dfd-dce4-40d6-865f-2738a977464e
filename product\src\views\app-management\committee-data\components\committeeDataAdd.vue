<template>
  <div class="committeeDataAdd">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="标题"
                    prop="title"
                    class="form-title">
        <el-input placeholder="请输入标题"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="所属结构"
                    prop="structureId"
                    class="form-input">
        <zy-select width="296"
                   node-key="id"
                   :props="{children: 'children',label: 'name'}"
                   v-model="form.structureId"
                   :data="structureIdData"
                   placeholder="请选择所属结构"></zy-select>
      </el-form-item>
      <el-form-item label="来源"
                    class="form-input">
        <el-input placeholder="请输入来源"
                  v-model="form.source"
                  clearable>
        </el-input>
      </el-form-item>
      <br>
      <el-form-item label="发布时间"
                    prop="publishDate"
                    class="form-input">
        <el-date-picker v-model="form.publishDate"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="排序"
                    class="form-input">
        <el-input-number v-model="form.sort"
                         placeholder="请输入排序"
                         :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="上传附件"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleFile"
                   :http-request="fileUpload"
                   :file-list="file"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="外部链接"
                    v-if="$isAppShow()"
                    class="form-title">
        <el-input placeholder="请输入外部链接"
                  v-model="form.externalLinks"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-ue">
        <UEditor v-model="form.content"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'committeeDataAdd',
  data () {
    return {
      form: {
        title: '',
        structureId: '',
        source: '',
        sort: '',
        publishDate: '',
        externalLinks: '',
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        structureId: [
          { required: true, message: '请选择所属结构', trigger: 'blur' }
        ],
        infoClass: [
          { required: true, message: '请选择资讯类型', trigger: 'blur' }
        ],
        infoType: [
          { required: true, message: '请选择显示类型', trigger: 'blur' }
        ],
        publishDate: [
          { required: true, message: '选择发布时间', trigger: 'blur' }
        ]
      },
      structureIdData: [],
      file: []
    }
  },
  props: ['id', 'module', 'parentId'],
  mounted () {
    this.informationColumnTree()
    if (this.id) {
      this.informationListInfo()
    } else {
      this.form.publishDate = this.$format(new Date(), 'YYYY-MM-DD HH:mm:ss')
      if (this.parentId && this.parentId !== '1') {
        this.form.structureId = this.parentId
      }
    }
  },
  methods: {
    async informationColumnTree () {
      const res = await this.$api.appManagement.informationColumnTree({ module: this.module })
      var { data } = res
      this.structureIdData = data
    },
    async informationListInfo () {
      const res = await this.$api.appManagement.informationListInfo(this.id)
      var { data } = res
      this.form.title = data.title
      this.form.secondTitle = data.secondTitle
      this.form.structureId = data.structureId
      this.form.source = data.source
      this.form.sort = data.sort
      this.form.publishDate = data.publishDate
      this.form.externalLinks = data.externalLinks
      this.form.content = data.content
      if (data.attachmentList) {
        data.attachmentList.forEach((item, index) => {
          item.uid = item.id
          item.name = item.fileName
        })
        this.file = data.attachmentList
      }
    },
    /**
   * 限制上传附件的文件类型
  */
    handleFile (file, fileList) {
    },
    /**
   * 上传附件请求方法
  */
    fileUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.file.push(data[0])
      }).catch(() => {
        files.onError()
      })
    },
    /**
   * 删除附件
  */
    beforeRemove (file, fileList) {
      var fileData = this.file
      this.file = fileData.filter(item => item.id !== file.id)
    },
    /**
   * 提交提案
  */
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var attach = []
          this.file.forEach(item => {
            attach.push(item.id)
          })
          var url = '/zyinfodetail/add'
          if (this.id) {
            url = '/zyinfodetail/edit'
          }
          this.$api.general.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            title: this.form.title,
            secondTitle: this.form.secondTitle,
            structureId: this.form.structureId,
            source: this.form.source,
            sort: this.form.sort,
            publishDate: this.form.publishDate,
            attachmentIds: attach.join(','),
            externalLinks: this.form.externalLinks,
            content: this.form.content,
            module: this.module
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.committeeDataAdd {
  width: 988px;
  .form-img {
    width: 100%;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
}
</style>
