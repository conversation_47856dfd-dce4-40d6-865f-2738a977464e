<template>
  <div class="generalTime">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input placeholder="请输入内容"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   v-permissions="has +'add'"
                   @click="newData">新增</el-button>
        <el-button v-permissions="has +'dels'"
                   @click="deleteClick">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="届次"
                           width="160"
                           prop="code">
          </el-table-column>
          <el-table-column label="全称"
                           prop="fullName">
          </el-table-column>
          <el-table-column label="简称"
                           prop="simpleName">
          </el-table-column>
          <el-table-column label="年份"
                           width="120"
                           prop="boutYear">
          </el-table-column>
          <el-table-column label="状态"
                           width="160"
                           prop="circlesStatus">
          </el-table-column>
          <el-table-column label="操作"
                           v-if="$hasPermission([has +'edit'])"
                           width="160">
            <template slot-scope="scope">
              <el-button @click="editor(scope.row)"
                         type="primary"
                         v-permissions="has +'edit'"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="show"
               :title="id?'编辑届次':'新增届次'">
      <generalTimeNew :id="id"
                      :memberType="memberType"
                      @newCallback="newCallback"></generalTimeNew>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import generalTimeNew from './generalTimeNew'
export default {
  name: 'generalTime',
  data () {
    return {
      id: '',
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      show: false
    }
  },
  mixins: [tableData],
  props: ['memberType', 'has'],
  components: {
    generalTimeNew
  },
  mounted () {
    this.historycirclesList()
  },
  methods: {
    newData () {
      this.id = ''
      this.show = true
    },
    newCallback () {
      this.historycirclesList()
      this.show = false
    },
    search () {
      this.page = 1
      this.historycirclesList()
    },
    reset () {
      this.keyword = ''
      this.historycirclesList()
    },
    async historycirclesList () {
      const res = await this.$api.memberInformation.historycirclesList({
        pageNo: this.page,
        pageSize: this.pageSize,
        memberType: this.memberType,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.historycirclesList()
    },
    whatPage (val) {
      this.historycirclesList()
    },
    editor (row) {
      this.id = row.id
      this.show = true
    },
    deleteClick (row) {
      this.$confirm('此操作将永久删除该届次, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.historycirclesDel(this.choose.join(','))
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    async historycirclesDel (id) {
      const res = await this.$api.memberInformation.historycirclesDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.historycirclesList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.generalTime {
  width: 100%;
  height: 100%;

  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
