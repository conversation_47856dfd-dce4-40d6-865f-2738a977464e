import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
// const delay = (function () {
//   let timer = 0
//   return function (callback, ms) {
//     clearTimeout(timer)
//     timer = setTimeout(callback, ms)
//   }
// })()
export default {
  name: 'XylPopupWindow',
  render () {
    const cover = (<div class="xyl-popup-window-cover" ref="xylPopupWindowCover"></div>)
    const content = (<div class={this.className} ref="xylPopupWindowContent">
      <div class="xyl-popup-window-head" on-mousedown={this.onMovedown} on-mouseup={this.onMoveup}>
        <div class="xyl-popup-window-title">{this.title}</div>
        <div on-click={this.closeClick}><i class="el-icon-close"></i></div>
      </div>
      <el-scrollbar class="xyl-popup-window-body" ref="scrollbar"><div ref="xylPopupWindowBodyBox" class={this.inputClassName}>{this.$slots.default}</div></el-scrollbar>
    </div>)
    return this.isShow ? (<div class="xyl-popup-window" ref="popupWindow">{cover} {content}</div>) : null
  },
  data () {
    return {
      isShow: false,
      width: null,
      height: null,
      positionX: 0,
      positionY: 0,
      number: 0,
      inputClassName: 'inputClass'
    }
  },
  computed: {
    className: {
      get () {
        let active = 'xyl-popup-window-content animate__animated animate__faster'
        if (this.value) {
          active += ' animate__zoomIn'
        } else {
          active += ' animate__zoomOut'
        }
        return active
      }
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    beforeClose: Function,
    mount: {
      type: Boolean,
      default: true
    },
    appendChild: {
      type: String,
      default: 'body'
    }
  },
  mounted () {
    this.$nextTick(() => {
      if (this.mount) {
        const body = this.$route.matched[this.$route.matched.length - 1].instances.default.$el
        if (body.append) {
          body.append(this.$el)
        } else {
          body.appendChild(this.$el)
        }
      } else {
        const body = document.querySelector(this.appendChild)
        if (body.append) {
          body.append(this.$el)
        } else {
          body.appendChild(this.$el)
        }
      }
    })
  },
  updated () {
  },
  methods: {
    location () {
      this.$nextTick(() => {
        const cover = this.$refs.xylPopupWindowCover
        const content = this.$refs.xylPopupWindowContent
        if (this.$refs.xylPopupWindowBodyBox.offsetHeight > parseInt(this.height * 0.85)) {
          content.style.height = (this.height * 0.85) + 'px'
          this.$refs.scrollbar.update()
        } else {
          content.style.height = 'auto'
          this.$refs.scrollbar.update()
        }
        this.positionX = (cover.offsetHeight - content.offsetHeight) / 2
        this.positionY = (cover.offsetWidth - content.offsetWidth) / 2
        content.style.top = (cover.offsetHeight - content.offsetHeight) / 2 + 'px'
        content.style.left = (cover.offsetWidth - content.offsetWidth) / 2 + 'px'
      })
    },
    // 鼠标按下移动
    onMovedown (e) {
      const pdiv = this.$refs.xylPopupWindowCover
      const odiv = this.$refs.xylPopupWindowContent
      const disX = e.clientX - odiv.offsetLeft
      const disY = e.clientY - odiv.offsetTop
      const diffWidth = pdiv.offsetWidth - odiv.offsetWidth
      const diffHeight = pdiv.offsetHeight - odiv.offsetHeight
      if (diffWidth <= 0 || diffHeight <= 0) {
        document.onmousemove = null
        document.onmouseup = null
        return
      }
      document.onmousemove = (e) => {
        let left = e.clientX - disX
        let top = e.clientY - disY
        const minWidth = pdiv.offsetLeft
        const minHeight = pdiv.offsetTop
        const maxWidth = (pdiv.offsetLeft + pdiv.offsetWidth) - odiv.offsetWidth
        const maxHeight = (pdiv.offsetTop + pdiv.offsetHeight) - odiv.offsetHeight
        left = left < minWidth ? minWidth : left
        top = top < minHeight ? minHeight : top
        left = left > maxWidth ? maxWidth : left
        top = top > maxHeight ? maxHeight : top
        this.positionX = top
        this.positionY = left
        odiv.style.left = left + 'px'
        odiv.style.top = top + 'px'
      }
      document.onmouseup = (e) => {
        document.onmousemove = null
        document.onmouseup = null
      }
    },
    onMoveup () {
      document.onmousemove = null
      document.onmouseup = null
    },
    closeClick () {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(() => { this.$emit('input', false) })
      } else {
        this.$emit('input', false)
      }
    }
  },
  watch: {
    value (val) {
      if (val) {
        this.isShow = true
        setTimeout(() => {
          this.inputClassName = ''
        }, 520)
        this.$nextTick(() => {
          const that = this
          erd.listenTo(this.$refs.xylPopupWindowCover, (element) => {
            that.$nextTick(() => {
              that.width = element.offsetWidth
              that.height = element.offsetHeight
            })
          })
          erd.listenTo(this.$refs.xylPopupWindowBodyBox, (element) => {
            that.$nextTick(() => {
              // delay(() => {
              that.location()
              // }, 99)
            })
          })
        })
      } else {
        setTimeout(() => {
          this.isShow = false
        }, 222)
        erd.uninstall(this.$refs.xylPopupWindowCover)
        erd.uninstall(this.$refs.xylPopupWindowBodyBox)
        this.number = 0
        this.inputClassName = 'inputClass'
      }
    }
  }
}
