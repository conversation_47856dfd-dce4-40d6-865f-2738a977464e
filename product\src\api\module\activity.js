// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const activity = {
  // 获取补录活动列表
  activityfillList (params) {
    return post('/activityfill/list?', params)
  },
  // 获取补录活动详情
  activityfillInfo (params) {
    return post(`/activityfill/info/${params}`)
  },
  // 删除补录活动列表
  activityfillDels (params) {
    return post('/activityfill/dels', params)
  },
  // 获取会议活动列表
  activityList (params) {
    return post('/activity/list?', params)
  },
  // 是否app显示
  updateIsAppShow (params) {
    return post('/activity/updateIsAppShow', params)
  },
  // 查询活动日程行程列表
  activityscheduleList (params) {
    return post('/activityschedule/list?', params)
  },
  // 查询活动资料列表
  materiainfoList (params) {
    return post('/materiainfo/list?', params)
  },
  // 获取活动报告列表
  activityreportList (params) {
    return post('/activityreport/list?', params)
  },
  // 单个请假审核（可发短信）
  leaveEditStatusSingle (params) {
    return post('/leave/editStatusSingle?', params)
  },
  // 批量请假
  editStatus (params) {
    return post('/leave/editStatus', params)
  },
  // 查询活动请假列表
  leaveList (params) {
    return post('/leave/list?', params)
  },
  // 我的活动
  activityFindMyActivitys (params) {
    return post('/activity/findMyActivitys?', params)
  },
  // 请假树
  leaveTreeData (params) {
    return post('/leave/treeData?', params)
  },
  // 保存会议活动
  activitySaveActivity (params) {
    return post('/activity/saveActivity?', params)
  },
  // 新增活动报告
  activityreportAddorEdit (url, params) {
    return post(url, params)
  },
  // 保存会议活动请假记录
  leaveAddorEdit (url, params) {
    return post(url, params)
  },
  // 保存会议活动资料
  materiainfoAddorEdit (url, params) {
    return post(url, params)
  },
  // 保存会议活动日程行程
  activityscheduleAddorEdit (url, params) {
    return post(url, params)
  },
  // 获取会议活动详情
  activityInfo (params) {
    return post('/activity/info/' + params)
  },
  // 查询活动请假详情
  leaveInfo (params) {
    return post('/leave/info/' + params)
  },
  // 删除活动报告
  activityreportDel (params) {
    return post('/activityreport/dels?', params)
  },
  // 获取活动报告详情
  activityreportInfo (params) {
    return post('/activityreport/info/' + params)
  },
  // 查询活动日程行程详情
  activityscheduleInfo (params) {
    return post('/activityschedule/info/' + params)
  },
  // 获取考勤数据
  activityGetMeetingListAttendance (params) {
    return post('/activity/getMeetingListAttendance/?id=' + params)
  },
  // 查询活动资料详情
  materiainfoInfo (params) {
    return post('/materiainfo/info/' + params)
  },
  // 删除会议活动
  activityDel (params) {
    return post('/activity/del?', params)
  },
  // 删除会议活动请假记录
  leaveDels (params) {
    return post('/leave/dels?', params)
  },
  // 删除会议活动日程行程
  activityscheduleDel (params) {
    return post('/activityschedule/dels?', params)
  },
  // 删除会议活动资料
  materiainfoDel (params) {
    return post('/materiainfo/dels?', params)
  },
  // 短信发送
  activitySmsSend (params) {
    return post('/activity/smsSend?', params)
  },
  // 批量删除会议活动
  activityDels (params) {
    return post('/activity/dels?', params)
  },
  treelist (params) { // 查询树列表
    return post('/tree/list?', params)
  },
  // activityMeetingcode (params) { // 导出签到二维码
  //   return post(`/activity/meetingcode?`, params)
  // }
  activityMeetingcode (params) { // 导出签到二维码
    window.open('/activity/meetingcode?id=' + params)
  },
  // 附件下载
  attachmentDownloadFile (params) { // 导出签到二维码
    window.open('http://test.dc.cszysoft.com:19418/attachment/downloadFile?id=' + params)
  },
  // 查看我的请假信息
  activityGetMyLeaveInfo (params) {
    return post('/activity/getMyLeaveInfo?id=' + params)
  }
}

export default activity
