<template>
  <div class="ActivitiesThroughDetails details">
    <div class="details-item-box">
      <div class="details-item">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.name}}</div>
      </div>
      <div class="labelClass">
        <div class="details-item">
          <div class="details-item-label">活动大类</div>
          <div class="details-item-value">{{details.bigTypeName}}</div>
        </div>

        <div class="details-item">
          <div class="details-item-label borderLeft">{{type?'活动日期':'发布时间'}}</div>
          <div class="details-item-value">{{details.dataTime}}</div>
        </div>

      </div>
      <!-- <div class="details-item">
          <div class="details-item-label">活动小类</div>
          <div class="details-item-value">{{details.smallTypeName}}</div>
        </div> -->
      <div class="details-item">
        <div class="details-item-label">活动主体</div>
        <div class="details-item-value">{{details.pubOrganizer}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">发布人</div>
        <div class="details-item-value">{{details.createName}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">{{`参加${$position()}`}}</div>
        <div class="details-item-value activityUser">
          <span v-for="(item,index) in details.chooseUserVo"
                :key="item.userId">{{index !=0?'，':''}}{{item.name}}</span>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">附件</div>
        <div class="details-item-value">
          <div class="details-item-file"
               v-for="(item, index) in details.attachmentList"
               :key="index"
               @click="fileClick(item)">{{item.fileName}}</div>
        </div>
      </div>
      <div class="details-content"
           v-html="details.content"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ActivitiesThroughDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id', 'type'],
  mounted () {
    this.activityfillInfo()
  },
  methods: {
    /**
     *详情
    */
    async activityfillInfo () {
      const res = await this.$api.activity.activityfillInfo(this.id)
      var { data } = res
      this.details = data
    },
    fileClick (row) {
      this.$api.proposal.downloadFile({ id: row.id }, row.fileName)
    }
  }
}
</script>
<style lang="scss">
.ActivitiesThroughDetails {
  height: 100%;
  padding: 24px;
  .activityUser {
    text-overflow: clip !important;
    white-space: normal !important;
  }
  .details-content {
    width: 100%;
    padding: 40px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
  .labelClass {
    display: flex;
    width: 100%;
  }
  .borderLeft {
    border-left: 1px solid #e6e6e6;
  }
}
</style>
