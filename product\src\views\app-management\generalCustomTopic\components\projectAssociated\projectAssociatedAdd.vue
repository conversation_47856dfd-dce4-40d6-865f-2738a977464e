<template>
  <div class="projectAssociatedAdd">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="排序"
                    class="form-title"
                    prop="sort">
        <el-input-number v-model="form.sort"
                         placeholder="请输入排序"
                         :min="1"></el-input-number>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'projectAssociatedAdd',
  data () {
    return {
      form: {
        sort: ''
      },
      rules: {
        sort: [
          { required: true, message: '请输入排序', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id'],
  methods: {
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/zySpecialsubjectRelateinfo/updateSort'
          this.$api.systemSettings.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            sort: this.form.sort
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.projectAssociatedAdd {
  width: 682px;
}
</style>
