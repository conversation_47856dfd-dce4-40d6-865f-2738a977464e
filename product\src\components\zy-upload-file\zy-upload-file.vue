<template>
  <div class="zy-upload-file">
    <el-upload
      :class="`zy-upload-file-${type}`"
      :drag="type == 'drag'"
      action="/"
      :before-remove="fileRemove"
      :before-upload="fileLimit"
      :http-request="fileUpload"
      :show-file-list="false"
      ref="elUploadRef"
      multiple
    >
      <template v-if="type == 'drag'">
        <div class="el-upload__text">
          将附件拖拽至此区域，或<em>点击上传</em>
        </div>
        <div class="el-upload__tip">{{ this.placeholder }}</div>
      </template>
      <div class="zy-upload-img-list" v-if="type == 'img'">
        <div
          class="zy-upload-img-items"
          v-for="item in file"
          :key="item.id || item.uid"
          @click.stop
        >
          <el-image :src="item.filePath" :preview-src-list="srcList">
          </el-image>
          <div class="zy-upload-img-percentage" v-if="item.show">
            <el-progress
              type="circle"
              :percentage="item.percentage"
            ></el-progress>
          </div>
          <div class="zy-upload-img-mask" v-if="!item.show">
            <span>
              <i class="el-icon-zoom-in"></i>
            </span>
          </div>
          <div class="zy-upload-img-operation" v-if="!item.show">
            <span @click.stop="fileClick(item)">
              <i class="el-icon-download"></i>
            </span>
            <span @click.stop="fileRemove(item)">
              <i class="el-icon-delete"></i>
            </span>
          </div>
        </div>
        <div class="zy-upload-img-item"><i class="el-icon-plus"></i></div>
      </div>
    </el-upload>
    <div class="zy-upload-file-list" v-if="type == 'drag'">
      <div class="zy-upload-file-item" v-for="item in file" :key="item.id">
        <div class="zy-upload-file-name">
          <i class="el-icon-document"></i>{{ item.fileName }}
        </div>
        <div class="zy-upload-file-number zy-file-succeed" v-if="!item.show">
          <i class="el-icon-circle-check"></i>
        </div>
        <div
          class="zy-upload-file-number zy-file-del"
          @click="fileRemove(item)"
          v-if="!item.show"
        >
          <i class="el-icon-close"></i>
        </div>
        <div class="zy-upload-file-number" v-if="item.show">
          {{ item.percentage }}%
        </div>
        <div class="zy-upload-file-progress" v-if="item.show">
          <el-progress
            :percentage="item.percentage"
            :show-text="false"
            :stroke-width="2"
          ></el-progress>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'zyUploadFile',
  data () {
    return {
      file: [],
      srcList: [],
      selectObj: []
    }
  },
  props: {
    // 控件样式类型  drag   img
    type: {
      type: String,
      default: 'drag'
    },
    // 最大上传文件数
    max: {
      type: Number,
      default: 0
    },
    // 开启文件类型限制
    limit: {
      type: Array,
      default: () => []
    },
    // 模块值
    module: {
      type: String,
      default: 'ta'
    },
    placeholder: {
      type: String,
      default: ''
    },
    // 开启删除确认
    delConfirm: {
      type: Boolean,
      default: false
    },
    fileCallback: Function,
    // 回显文件
    data: {
      type: Array,
      default: () => []
    },
    // 路径和名字配置
    props: {
      type: Object,
      default: () => {
        return {
          fileName: 'fileName',
          filePath: 'filePath'
        }
      }
    }
  },
  watch: {
    data (val) {
      if (val.length) {
        this.echo()
      }
    }
  },
  mounted () {
    if (this.data.length) {
      this.echo()
    }
  },
  methods: {
    echo () {
      this.data.forEach(item => {
        if (!Object.prototype.hasOwnProperty.call(this.selectObj, item.id)) {
          this.selectObj[item.id] = item.id
          this.file.push({ id: item.id, fileName: item[this.props.fileName], filePath: item[this.props.filePath], show: false })
          if (this.type === 'img') {
            this.srcList.push(item[this.props.filePath])
          }
        }
      })
    },
    /**
     * 限制上传附件的文件类型
    */
    fileLimit (file, fileList) {
      if (this.limit.length) {
        var show = false
        var text = ''
        var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
        this.limit.forEach(item => {
          text += `${item}，`
          if (testmsg === item) {
            show = true
          }
        })
        if (!show) {
          this.$message({
            message: `上传文件只能是${text}格式!`,
            type: 'warning'
          })
        }
        return show
      }
    },
    /**
     * 上传附件请求方法
    */
    fileUpload (files) {
      console.log(files)
      if (this.max && this.max <= this.file.length) {
        this.$message({
          message: `最多只能上传${this.max}个文件`,
          type: 'warning'
        })
        return false
      }
      this.file.push({ uid: files.file.uid, fileName: files.file.name, filePath: URL.createObjectURL(files.file), show: true })
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', this.module)
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      if (typeof this.fileCallback === 'function') {
        this.fileCallback(files, this.percentage, this.uploadSucceed, this.uploadErr)
      } else {
        this.uploadFile(param, files.file.uid)
      }
    },
    async uploadFile (param, id) {
      try {
        const res = await this.$api.general.uploadFile(param, this.percentage, id)
        var { data } = res
        this.uploadSucceed(data[0], id)
      } catch (err) {
        this.uploadErr(id)
      }
    },
    uploadSucceed (data, id) {
      var file = this.file
      file.forEach(item => {
        if (item.uid === id) {
          item.show = false
          item.id = data.id
          item.fileType = data.fileType
          if (this.type === 'img') {
            this.srcList.push(item[this.props.filePath])
          }
          this.selectObj[data.id] = data.id
        }
      })
      this.file = file
    },
    uploadErr (id) {
      var fileData = this.file
      this.file = fileData.filter(item => item.uid !== id)
    },
    /**
     * 删除附件
    */
    fileRemove (file) {
      if (this.delConfirm) {
        this.$confirm('此操作将删除该附件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.fileDel(file)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          })
        })
      } else {
        this.fileDel(file)
      }
    },
    fileDel (file) {
      var fileData = this.file
      this.file = fileData.filter(item => item.id !== file.id)
      if (this.type === 'img') {
        this.srcList = []
        this.file.forEach(item => {
          this.srcList.push(item[this.props.filePath])
        })
      }
      delete this.selectObj[file.id] // eslint-disable-line
      this.$emit('clearFile')
    },
    fileClick (data) {
      this.$api.general.downloadFile({ id: data.id }, data.fileName)
    },
    percentage (e, id) {
      var file = this.file
      file.forEach(item => {
        if (item.uid === id) {
          item.percentage = (e.loaded / e.total * 100 | 0)
        }
      })
      if (this.file.file) {
        this.$delete(this.file, 'file')
      } else {
        this.$set(this.file, 'file', file)
      }
    },
    obtainId () {
      var ids = []
      this.file.forEach(item => {
        if (item.id) {
          ids.push(item.id)
        }
      })
      return ids
    }
  }
}
</script>
<style lang="scss">
.zy-upload-file {
  width: 100%;
  .zy-upload-file-drag {
    .el-upload {
      width: 100%;
    }
    .el-upload-dragger {
      width: 100%;
      height: 120px;
      .el-upload__text {
        margin-top: 22px;
      }
      .el-upload__tip {
        line-height: 32px;
      }
    }
  }
  .zy-upload-file-list {
    .zy-upload-file-item {
      height: 28px;
      cursor: pointer;
      position: relative;
      margin-bottom: 12px;
      .zy-upload-file-name {
        width: calc(100% - 100px);
        height: 28px;
        line-height: 28px;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-left: 6px;
        color: #606266;
        .el-icon-document {
          margin-right: 9px;
        }
      }
      .zy-upload-file-number {
        position: absolute;
        right: 0;
        bottom: 0;
        height: 100%;
        line-height: 28px;
        padding: 0 12px;
        color: #606266;
        .el-icon-circle-check {
          color: #67c23a;
        }
      }
      .zy-upload-file-progress {
        position: absolute;
        bottom: -3px;
        left: 0;
        width: 100%;
      }
      .zy-file-del {
        display: none;
      }
      &:hover {
        background-color: #f5f7fa;
        .zy-upload-file-name {
          color: $zy-color;
        }
        .zy-file-succeed {
          display: none;
        }
        .zy-file-del {
          display: inline;
        }
      }
    }
  }
  .zy-upload-file-img {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
      height: 120px;
    }
    .zy-upload-img-list {
      display: flex;
      flex-wrap: wrap;
      .zy-upload-img-item {
        height: 160px;
        width: 160px;
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          border-color: $zy-color;
        }
        .el-icon-plus {
          font-size: 28px;
          color: #8c939d;
        }
      }
      .zy-upload-img-items {
        height: 160px;
        width: 160px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        margin-bottom: 12px;
        overflow: hidden;
        position: relative;
        .el-image {
          width: 100%;
          height: 100%;
        }
        .zy-upload-img-percentage {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba($color: #000000, $alpha: 0.6);
          .el-progress__text {
            color: #fff;
          }
        }
        .zy-upload-img-mask {
          display: none;
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          line-height: 158px;
          text-align: left;
          background-color: rgba($color: #000000, $alpha: 0.6);
          pointer-events: none;
          span {
            color: #fff;
            font-size: 26px;
            width: 33.333%;
            display: inline-block;
            text-align: center;
          }
        }
        .zy-upload-img-operation {
          display: none;
          position: absolute;
          top: 0;
          right: 0;
          height: 100%;
          width: 66.666%;
          line-height: 158px;
          span {
            color: #fff;
            font-size: 26px;
            width: 50%;
            display: inline-block;
          }
        }
        &:hover {
          .zy-upload-img-mask {
            display: inline;
          }
          .zy-upload-img-operation {
            display: inline;
          }
        }
      }
    }
  }
}
</style>
