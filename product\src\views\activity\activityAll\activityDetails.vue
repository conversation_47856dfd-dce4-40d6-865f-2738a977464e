<template>
  <div class="activityDetails details">
    <div class="details-title">活动信息详情</div>
    <div class="details-item-box">
      <div class="details-item-title">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.meetName}}</div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">活动类型</div>
          <div class="details-item-value">{{details.meetTypeName}}</div>
        </div>
        <!-- <div class="details-item">
          <div class="details-item-label">报送单位</div>
          <div class="details-item-value">{{details.meetingotherTypenameUnit}}</div>
        </div> -->
        <div class="details-item">
          <div class="details-item-label">报名截止时间</div>
          <div class="details-item-value">{{details.signEndTime}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">活动开始时间</div>
          <div class="details-item-value">{{details.meetStartTime}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">组织部门</div>
          <div class="details-item-value">{{details.organizer}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">开始签到时间</div>
          <div class="details-item-value">{{details.meetSignBeginTime}}</div>
        </div>
        <div class="details-item">
          <div class="details-item-label">活动结束时间</div>
          <div class="details-item-value">{{details.meetEndTime}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">活动主体</div>
        <div class="details-item-value">{{details.pubOrganizer}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">地点</div>
        <div class="details-item-value">{{details.address}}</div>
      </div>
      <!-- <div class="details-item">
        <div class="details-item-label">标签</div>
        <div class="details-item-value">{{details.labelName}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">邀请人</div>
        <div class="details-item-value activityUser">{{details.inviterNames}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">报名人</div>
        <div class="details-item-value activityUser">{{details.signUpNames}}</div>
      </div> -->
      <div class="details-item">
        <div class="details-item-label">签到人</div>
        <div class="details-item-value activityUser">{{details.signInNames}}</div>
      </div>
      <div class="details-item-column"
           v-if="type=='1'">
        <div class="details-item">
          <div class="details-item-label">签到口令</div>
          <div class="details-item-value activityUser">{{details.signInCommand}}</div>
        </div>
        <div class="details-item-img activityimg">
          <div class="details-item-label">签到二维码</div>
          <div class="details-item-value">
            <vue-qr class="activityQr"
                    :margin="0"
                    :text="activityQr"></vue-qr>
            <!-- <el-image style="width: 152px; height: 152px"
                      :src="url"
                      :preview-src-list="srcList">
            </el-image> -->
          </div>
        </div>
      </div>
      <div class="details-item-column"
           v-if="type=='1'">
        <div class="details-item">
          <div class="details-item-label">活动资料</div>
          <div class="details-item-value activitybutton">
            <el-button type="primary"
                       @click="dataClick"
                       size="small">新增</el-button>
            <el-button type="primary"
                       size="small"
                       @click="viewData"
                       plain>{{type=='1'?'活动资料管理':'活动资料查看'}}</el-button>
          </div>
        </div>
        <div class="details-item">
          <div class="details-item-label">日程行程</div>
          <div class="details-item-value activitybutton">
            <el-button type="primary"
                       @click="TripClick"
                       size="small">新增</el-button>
            <el-button type="primary"
                       size="small"
                       @click="viewTrip"
                       plain>{{type=='1'?'日程行程管理':'日程行程查看'}}</el-button>
          </div>
        </div>
        <div class="details-item">
          <div class="details-item-label">请假申请</div>
          <div class="details-item-value activitybutton">
            <el-button type="primary"
                       @click="askLeaveClick"
                       size="small">新增</el-button>
            <el-button type="primary"
                       size="small"
                       @click="viewAskLeave"
                       plain>{{type=='1'?'请假申请管理':'我的请假申请'}}</el-button>
          </div>
        </div>
        <div class="details-item">
          <div class="details-item-label">活动报告</div>
          <div class="details-item-value activitybutton">
            <el-button type="primary"
                       @click="reportClick"
                       size="small">新增</el-button>
            <el-button type="primary"
                       size="small"
                       @click="viewReport"
                       plain>{{type=='1'?'活动报告管理':'活动报告查看'}}</el-button>
          </div>
        </div>
        <div class="details-item">
          <div class="details-item-label">考勤查看</div>
          <div class="details-item-value activitybutton">
            <el-button type="primary"
                       size="small"
                       @click="attendance"
                       plain>详情</el-button>
          </div>
        </div>
      </div>
      <div class="details-content"
           v-html="details.content"></div>
    </div>
    <xyl-popup-window v-model="attendanceShow"
                      title="考勤详情">
      <activityAttendance :id="id"></activityAttendance>
    </xyl-popup-window>
    <xyl-popup-window v-model="dataShow"
                      title="新增资料">
      <activityDataNew :meetId="id"
                       @newCallback="newCallback"></activityDataNew>
    </xyl-popup-window>
    <xyl-popup-window v-model="TripShow"
                      title="新增行程">
      <activityTripNew :meetId="id"
                       @newCallback="newCallback"></activityTripNew>
    </xyl-popup-window>
    <xyl-popup-window v-model="askLeaveShow"
                      title="新增请假">
      <activityAskLeaveNew :meetId="id"
                           @newCallback="newCallback"></activityAskLeaveNew>
    </xyl-popup-window>
    <xyl-popup-window v-model="reportShow"
                      title="新增报告">
      <activityReportNew :meetId="id"
                         @newCallback="newCallback"></activityReportNew>
    </xyl-popup-window>
  </div>
</template>
<script>
import vueQr from 'vue-qr'
import activityAttendance from './activityAttendance/activityAttendance'
import activityDataNew from './activityData/activityDataNew'
import activityTripNew from './activityTrip/activityTripNew'
import activityAskLeaveNew from './activityAskLeave/activityAskLeaveNew'
import activityReportNew from './activityReport/activityReportNew'
export default {
  name: 'activityDetails',
  data () {
    return {
      details: {},
      activityQr: '',
      url: '',
      srcList: [],
      attendanceShow: false,
      dataShow: false,
      TripShow: false,
      askLeaveShow: false,
      reportShow: false
    }
  },
  props: ['id', 'type'],
  inject: ['newTab'],
  components: {
    vueQr,
    activityAttendance,
    activityDataNew,
    activityTripNew,
    activityAskLeaveNew,
    activityReportNew
  },
  mounted () {
    this.activityInfo()
  },
  methods: {
    /**
     *详情
    */
    async activityInfo () {
      const res = await this.$api.activity.activityInfo(this.id)
      var { data } = res
      this.details = data
      this.activityQr = `${this.$logo()}|activitySignIn|${this.id}`
      this.dictionaryPubkvs()
    },
    /**
     *字典
    */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'attending_type'
      })
      var { data } = res
      this.url = data.attending_type[0].id + this.id
      this.srcList[0] = data.attending_type[0].id + this.id
    },
    // 考勤详情
    attendance () {
      this.attendanceShow = true
    },
    dataClick () {
      this.dataShow = true
    },
    viewData () {
      this.$confirm('此操作将关闭当前弹窗并打开' + '"' + this.details.meetName + '""' + '的活动资料列表, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.newTab({ name: '活动资料', menuId: '199', to: '/activityData', params: { meetId: this.details.id, type: 0 } })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    TripClick () {
      this.TripShow = true
    },
    viewTrip () {
      this.$confirm('此操作将关闭当前弹窗并打开' + '"' + this.details.meetName + '""' + '的活动行程列表, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.newTab({ name: '活动行程', menuId: '200', to: '/activityTrip', params: { meetId: this.details.id, type: 0 } })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    askLeaveClick () {
      this.askLeaveShow = true
    },
    viewAskLeave () {
      this.$confirm('此操作将关闭当前弹窗并打开' + '"' + this.details.meetName + '""' + '的请假申请列表, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.newTab({ name: '请假申请', menuId: '3', to: '/activityAskLeave', params: { meetId: this.details.id, type: 0 } })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    reportClick () {
      this.reportShow = true
    },
    viewReport () {
      this.$confirm('此操作将关闭当前弹窗并打开' + '"' + this.details.meetName + '""' + '的活动报告列表, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.newTab({ name: '活动报告', menuId: '222', to: '/activityReport', params: { meetId: this.details.id, type: 0 } })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    newCallback () {
      this.dataShow = false
      this.TripShow = false
      this.askLeaveShow = false
      this.reportShow = false
    }
  }
}
</script>
<style lang="scss">
.activityDetails {
  height: 100%;
  padding: 24px;
  .activityUser {
    text-overflow: clip !important;
    white-space: normal !important;
  }
  .activitybutton {
    padding: 5px 9px !important;
  }
  .activityimg {
    height: 172px !important;
    .activityQr {
      height: 150px !important;
      width: 150px !important;
    }
  }
  .details-content {
    width: 100%;
    padding: 40px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
