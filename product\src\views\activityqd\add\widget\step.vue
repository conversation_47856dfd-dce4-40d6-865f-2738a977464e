<template>
  <div class="step">
    <div class="step-item"
         :class="{'active':active === item.value,'success':active>item.value}"
         v-for="(item,index) in process"
         :key="index">
      <div class="step-item-content">
        <span class="circle"
              v-if="active<=item.value">{{index+1}}</span>
        <i class="el-icon-circle-check"
           v-if="active>item.value"></i>
        {{item.label}}
      </div>
      <div class="step-icon-box"
           v-if="index+1<process.length">
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    active: {
      type: Number,
      default: 1
    }
  },
  data () {
    return {
      process: [
        { label: '基本信息', value: 1 },
        { label: '参与人员', value: 2 },
        { label: '上传资料', value: 3 }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.step {
  margin: 0 auto;
  background-color: #f5f7fa;
  height: 52px;
  border-radius: 4px;
  display: flex;
  padding: 0 8%;
  .step-item {
    display: flex;
    align-items: center;
    flex-basis: 50%;
    flex-shrink: 1;
    .step-item-content {
      color: #ccc;
      font-size: 18px;
      display: flex;
      align-items: center;
      span {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        text-align: center;
        line-height: 22px;
        border: 2px solid #ccc;
        margin-right: 8px;
      }
    }
    .step-icon-box {
      font-size: 26px;
      color: #cccccc;
      display: flex;
      align-items: stretch;
      flex-grow: 1;
      justify-content: center;
    }
  }
  .step-item:nth-last-child(1) {
    max-width: 33.33%;
    flex-basis: auto !important;
    flex-shrink: 0;
    flex-grow: 0;
  }
  .step-item.active {
    .step-item-content {
      color: #333;
      span {
        border-color: #333;
      }
    }
  }
  .step-item.success {
    .step-item-content {
      color: $zy-color;
      > i {
        font-size: 26px;
        margin-right: 8px;
      }
    }
  }
}
</style>
