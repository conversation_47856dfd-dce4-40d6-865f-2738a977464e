// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile,
  fileRequest
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const sinceManagement = {
  // 获取我的信息
  dutyreportGetMyInfo (params) {
    return post('/dutyreport/getMyInfo?', params)
  },
  // 获取履职报告详情
  dutyreportInfo (params) {
    return post('/dutyreport/info/' + params)
  },
  // 删除履职报告
  dutyreportDels (params) {
    return post('/dutyreport/dels?', params)
  },
  dutyusersituationgetDutyNum (params) { // 履职情况列表
    return post('/dutyusersituation/getDutyNum?', params)
  },
  dutyusersituationlist (params) { // 履职情况详情
    return post('/dutyusersituation/list?', params)
  },
  userdutyyearlist (params) { // 代表履职补录列表
    return post('/userdutyyear/list?', params)
  },
  addDutyUserSituations (url, params) { // 批量确认确认履职情况
    return post(`/dutyusersituation/${url}?`, params, { 'Content-Type': 'application/json;charset=UTF-8' })
  },
  editStateByIds (params) { // 接收
    return post('/userdutyyear/editStateByIds?', params)
  },
  userdutyyeardels (params) { // 删除
    return post('/userdutyyear/dels?', params)
  },
  getIsAddDutyYear (params) { // 检查是否新增
    return post('/dutyusersituation/getIsAddDutyYear?', params)
  },
  dictionarypubkvs (params) { // 年份字典
    return post('/dictionary/pubkvs?', params)
  },
  dutyoptionslist (params) { // 获取履职项列表
    return post('/dutyoptions/list?', params)
  },
  dutyoptionsinfo (params) { // 获取履职项详情
    return post(`/dutyoptions/info/${params}`)
  },
  dutyoptionsurl (url, params) { // 新增 修改
    return post(`/dutyoptions/${url}`, params)
  },
  dutyoptionsdels (params) { // 删除
    return post('/dutyoptions/dels', params)
  },
  dutyitemlist (params) { // 获取履职项配置列表
    return post('/dutyitem/list', params)
  },
  dutyiteminfo (params) { // 获取履职项配置详情
    return post(`/dutyitem/info/${params}`)
  },
  dutyitemurl (url, params) { // 新增 修改
    return post(`/dutyitem/${url}`, params)
  },
  dutyitemdels (params) { // 删除
    return post('/dutyitem/dels', params)
  },
  outDutyDetailWord (params, name) { // 导出word（网页）履职补录
    fileRequest('/dutyusersituation/outDutyDetailWord', params, name)
  },
  dutyconfiggenerateDuty (params) { // 履职列表
    return post('/dutyconfig/generateDuty', params)
  },
  dutyupdatescoreadd (params) { // 浮动分详情
    return post('/dutyupdatescore/add', params)
  },
  getListHeader (params) { // 履职列表
    return post('/dutyconfig/getListHeader', params)
  },
  getDutyConfigInfo (params) { // 履职项配置列表
    return post('/dutyconfig/getDutyConfigInfo', params)
  },
  dutyconfigedit (params) { // 履职项配置列表
    return post('/dutyconfig/edit', params)
  },
  dutyyearEdit (params) { // 履职项配置列表
    return post('/dutyyear/edit', params)
  },
  generateDutyDetail (params) { // 履履职详情
    return post('/dutyconfig/generateDutyDetail', params)
  },
  getDetailHeader (params) { // 获取履职详情表头
    return post('/dutyconfig/getDetailHeader', params)
  },
  getgenerateDuty (params) { // 生成履职档案
    return post('/duty/generateDuty', params)
  },
  getgenerateCommitteeDuty (params) { // 生成常委会组成人员履职档案
    return post('/duty/generateCommitteeDuty', params)
  },
  fillgenerallist (params) { // 履职填报列表
    return post('/fillgeneral/list', params)
  },
  saveFillGeneral (params) { // 履职填报列表
    return post('/fillgeneral/saveFillGeneral', params)
  },
  fillgeneraldels (params) { // 履职填报列表
    return post('/fillgeneral/dels', params)
  },
  fillgeneralinfo (params) { // 履职填报列表
    return post(`/fillgeneral/info/${params}`)
  },
  // 树接口
  treelist (params) {
    return post('/tree/list?', params)
  },
  dutyGenerateDuty (params) { // 履职情况统计
    return post('/duty/generateDuty?', params)
  },
  dutyGetDutyDetail (params) { // 履职情况统计
    return post('/duty/getDutyDetail?', params)
  },
  findActivityAuditFills (params) { // 履职档案审核列表
    return post('/activityfill/findActivityAuditFills?', params)
  },
  auditActivityFill (params) { // 履职档案审核列表
    return post('activityfill/auditActivityFill', params)
  },
  dutyupdatescoreList (params) { // 履职浮动分列表
    return post('dutyupdatescore/list', params)
  },
  dutyupdatescoreDel (params) { // 履职浮动分删除
    return get(`/dutyupdatescore/del/${params}`)
  }
}

export default sinceManagement
