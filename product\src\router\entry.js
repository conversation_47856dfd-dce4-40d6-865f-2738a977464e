
import Vue from 'vue'
import Router from 'vue-router'
import router, { routes, resetRouter } from './index'
import api from '../api'
import { addRoute } from './addRoute'
import { changeThemeColor } from '@/theme'

var theme = ''
var logoImg = ''
var CuteHand = ''
var isAppShow = ''
var isMoreProject = ''
// 加载页面获取配置
async function refresh (to, next, index) {
  const res = await api.general.nologin({
    codes: 'logo,rongCloudIdPrefix,mainText,router,isMoreProject,isAppShow,downloadQr,listPageRowNumber,pageSizes,CuteHand,appGrayscale,workstationName'
  })
  var { data } = res
  if (data) {
    if (data.appGrayscale === '1') {
      window.document.documentElement.setAttribute('grey', '1')
    }
    if (JSON.stringify(data) !== '{}') {
      theme = data.rongCloudIdPrefix
      sessionStorage.setItem('theme', theme)
      if (data.CuteHand) {
        CuteHand = data.CuteHand === 'true'
      } else {
        CuteHand = false
      }
      if (data.isAppShow) {
        isAppShow = data.isAppShow === 'true'
      } else {
        isAppShow = false
      }
      if (data.isMoreProject) {
        isMoreProject = data.isMoreProject === 'true'
      } else {
        isMoreProject = false
      }
      if (data.logo) {
        logoImg = data.logo.slice(1)
      }
      generate(data.router, data.mainText, data.downloadQr, data.listPageRowNumber, data.pageSizes, index, data.workstationName)
    }
  }
  if (to.name !== 'login') {
    next({ name: 'login' })
  } else {
    const route = sessionStorage.getItem('route') || ''
    if (route) {
      const fullPath = JSON.parse(route)
      next({ path: fullPath })
    } else {
      next()
    }
  }
}
// 切换系统获取配置
async function refreshs (token, projects, callback, index) {
  const res = await api.general.nologin({
    codes: 'logo,rongCloudIdPrefix,mainText,router,isMoreProject,isAppShow,downloadQr,listPageRowNumber,pageSizes,CuteHand,appGrayscale,workstationName'
  })
  var { data } = res
  theme = ''
  if (data) {
    if (data.appGrayscale === '1') {
      window.document.documentElement.setAttribute('grey', '1')
    }
    if (JSON.stringify(data) !== '{}') {
      theme = data.rongCloudIdPrefix
      if (data.CuteHand) {
        CuteHand = data.CuteHand === 'true'
      } else {
        CuteHand = false
      }
      if (data.isAppShow) {
        isAppShow = data.isAppShow === 'true'
      } else {
        isAppShow = false
      }
      if (data.isMoreProject) {
        isMoreProject = data.isMoreProject === 'true'
      } else {
        isMoreProject = false
      }
      if (data.logo) {
        logoImg = data.logo.slice(1)
      }
      sessionStorage.setItem('token' + theme, JSON.stringify(token))
      sessionStorage.setItem('projects' + theme, JSON.stringify(projects))
      sessionStorage.setItem('theme', theme)
      generate(data.router, data.mainText, data.downloadQr, data.listPageRowNumber, data.pageSizes, index, data.workstationName)
    }
  }
  callback()
}
// 根据配置生成系统路由和系统文字
function generate (router, mainText, downloadQr, pageSize = '', pageSizes = '', index, workstationName) {
  sessionStorage.setItem('downloadQr' + theme, JSON.stringify(downloadQr))
  sessionStorage.setItem('pageSize' + theme, JSON.stringify(pageSize))
  sessionStorage.setItem('pageSizes' + theme, JSON.stringify(pageSizes))
  sessionStorage.setItem('workstationName' + theme, JSON.stringify(workstationName))
  if (router) {
    sessionStorage.setItem('router' + theme, router)
    if (index === 1) {
      addRoute()
    }
  } else {
    sessionStorage.setItem('router' + theme, JSON.stringify({ login: 'login', home: 'home' }))
    if (index === 1) {
      addRoute()
    }
  }
  if (mainText) {
    var array = JSON.parse(mainText)
    sessionStorage.setItem('generalName' + theme, JSON.stringify(array.generalName))
    sessionStorage.setItem('system' + theme, JSON.stringify(array.system))
    sessionStorage.setItem('position' + theme, JSON.stringify(array.position))
    document.title = array.generalName.replace(/<.*?>/g, '')
    if (array.system === '人大') { // eslint-disable-line
      changeThemeColor('#BC1D1D')
    } else if (array.system === '政协') { // 智慧政协
      changeThemeColor('#3657C0')
    }
  } else {
    sessionStorage.setItem('generalName' + theme, '智慧人大信息管理系统')
    sessionStorage.setItem('system' + theme, '人大')
    sessionStorage.setItem('position' + theme, '代表')
    document.title = '智慧人大信息管理系统'
    changeThemeColor()
  }
}
// 解决路由切换报错
// const originalPush = Router.prototype.push
// Router.prototype.push = function push (location) {
//   return originalPush.call(this, location).catch(err => err)
// }
const originalPush = Router.prototype.push
Router.prototype.push = function push (location, onResolve, onReject) {
  if (onResolve || onReject) { return originalPush.call(this, location, onResolve, onReject) }
  return originalPush.call(this, location).catch(err => err)
}
// 菜单过滤
function filterMenu (menuList) {
  return menuList.filter(item => {
    return item.isShow
  }).map(item => {
    item = Object.assign({}, item)
    if (item.children) {
      item.children = filterMenu(item.children)
    }
    return item
  })
}
// 切换系统登录
async function changearea () {
  const res = await api.general.changearea({})
  var { data: { token, user, menus, areas } } = res
  var menu = filterMenu(menus)
  sessionStorage.setItem('menus' + theme, JSON.stringify(menu))
  sessionStorage.setItem('token' + theme, JSON.stringify(token))
  sessionStorage.setItem('user' + theme, JSON.stringify(user))
  sessionStorage.setItem('areas' + theme, JSON.stringify(areas))
  sessionStorage.setItem('areaId' + theme, JSON.stringify(user.areaId))
  areas.forEach(item => {
    if (item.id === user.areaId) {
      sessionStorage.setItem('areaName' + theme, JSON.stringify(item.value))
    }
  })
  if (menu.length === 1) {
    sessionStorage.setItem('name', JSON.stringify(menu[0].name))
    sessionStorage.setItem('menuChild', JSON.stringify(menu[0].children))
    router.push({ path: '/general' })
  } else {
    router.push({ path: '/home' })
  }
  readBigDataConfig()
}
// 切换系统
function switchClick (type) {
  const token = JSON.parse(sessionStorage.getItem('token' + theme)) || ''
  const projects = JSON.parse(sessionStorage.getItem('projects' + theme)) || ''
  sessionStorage.clear()
  sessionStorage.setItem('switchpage' + theme, JSON.stringify(type))
  resetRouter()
  refreshs(token, projects, changearea, 1)
}
//  获取大数据配置
async function readBigDataConfig () {
  const res = await api.general.readonfig({ codes: 'BigDataUser,BigDataUrl,BigDataLiveShowUrl,fileStoreUrl,fileVisitUrl,workstationUrl,BigDataCorrecting' })
  if (res.data.BigDataUrl.indexOf('http') === -1) {
    const url = window.location.origin + res.data.BigDataUrl
    sessionStorage.setItem('BigDataUrl' + theme, JSON.stringify(url))
  } else {
    sessionStorage.setItem('BigDataUrl' + theme, JSON.stringify(res.data.BigDataUrl))
  }
  sessionStorage.setItem('BigDataUser' + theme, JSON.stringify(res.data.BigDataUser))
  sessionStorage.setItem('BigDataLiveShowUrl' + theme, JSON.stringify(res.data.BigDataLiveShowUrl))
  sessionStorage.setItem('fileStoreUrl' + theme, JSON.stringify(res.data.fileStoreUrl))
  sessionStorage.setItem('fileVisitUrl' + theme, JSON.stringify(res.data.fileVisitUrl))
  sessionStorage.setItem('workstationUrl' + theme, JSON.stringify(res.data.workstationUrl))
  sessionStorage.setItem('BigDataCorrecting' + theme, JSON.stringify(res.data.BigDataCorrecting))
}
// 主题
Vue.prototype.$logo = () => theme
// 主题
console.log(CuteHand)
Vue.prototype.$CuteHand = () => CuteHand
// 主题
Vue.prototype.$isAppShow = () => isAppShow
// 主题
Vue.prototype.$isMoreProject = () => isMoreProject
// 切换系统
Vue.prototype.$switchClick = switchClick
// 获取大数据配置
Vue.prototype.$readonfig = readBigDataConfig
// 区分是人大还是政协
Vue.prototype.$system = () => {
  return JSON.parse(sessionStorage.getItem('system' + theme)) || ''
}
// 区分是委员还是代表
Vue.prototype.$position = () => {
  return JSON.parse(sessionStorage.getItem('position' + theme)) || ''
}
// 系统名称
Vue.prototype.$generalName = () => {
  return JSON.parse(sessionStorage.getItem('generalName' + theme)).replace(/<.*?>/g, '') || ''
}
// 系统分页条数配置
Vue.prototype.$pageSize = () => {
  const pageSize = JSON.parse(sessionStorage.getItem('pageSize' + theme)) || '10'
  return Number(pageSize)
}
// 系统分页条数配置
Vue.prototype.$pageSizes = () => {
  const pageSizes = JSON.parse(sessionStorage.getItem('pageSizes' + theme)) || '10, 20, 50, 100, 200, 500'
  var pageSize = []
  pageSizes.split(',').forEach(item => {
    pageSize.push(Number(item))
  })
  return pageSize
}
Vue.prototype.$logoImg = () => {
  return logoImg
}
Vue.prototype.$validForbid = (value) => {
  value = value.replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/g, '').replace(/\s/g, '')
  return value
}
// 阻止浏览器的返回
window.addEventListener('popstate', function () {
  history.pushState(null, null, document.URL)
})
// 重置路由
var index = 0
function resetRouters () {
  index = 0
  router.options.routes = routes
  resetRouter()
}
// 重置路由全局方法
Vue.prototype.$resetRouter = resetRouters
router.beforeEach((to, from, next) => {
  if (to.name === 'app') {
    next()
    return
  }
  if (api.general.overdue()) {
    const user = sessionStorage.getItem('token' + theme)
    if (to.query.route) {
      sessionStorage.setItem('route', JSON.stringify(to.fullPath))
    }
    if (user) {
      if (to.name === 'login') {
        const route = sessionStorage.getItem('route') || ''
        if (route) {
          const fullPath = JSON.parse(route)
          next({ path: fullPath })
        } else {
          const menus = sessionStorage.getItem('menus' + theme)
          if (menus) {
            const menu = sessionStorage.getItem('menuChild')
            if (menu) {
              next({ name: 'general' })
            } else {
              next({ name: 'home' })
            }
          }
        }
      } else {
        next()
      }
    } else {
      index += 1
      refresh(to, next, index)
    }
  } else {
    if (to.name !== 'overdue') {
      next({ name: 'overdue' })
    } else {
      next()
    }
  }
})
