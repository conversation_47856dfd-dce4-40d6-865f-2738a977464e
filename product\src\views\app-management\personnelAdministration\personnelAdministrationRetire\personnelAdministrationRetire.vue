<template>
  <div class="personnelAdministrationRetire">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="duty"
                   clearable
                   placeholder="请选择原职务">
          <el-option v-for="(item,index) in dutyData"
                     :key="index"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="politicCountenance"
                   clearable
                   placeholder="请选择政治面貌">
          <el-option v-for="(item,index) in politicCountenanceData"
                     :key="index"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="rank"
                   clearable
                   placeholder="请选择职级">
          <el-option v-for="(item,index) in rankData"
                     :key="index"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="gender"
                   clearable
                   placeholder="请选择性别">
          <el-option v-for="(item,index) in genderData"
                     :key="index"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-date-picker v-model="retireDate"
                        align="right"
                        type="month"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        placeholder="请选择退休时间">
        </el-date-picker>
        <el-date-picker v-model="joinPartyDate"
                        align="right"
                        type="month"
                        format="yyyy-MM"
                        value-format="yyyy-MM"
                        placeholder="请选择入党时间">
        </el-date-picker>
        <el-select v-model="nation"
                   clearable
                   placeholder="请选择民族">
          <el-option v-for="(item,index) in nationData"
                     :key="index"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="exportClick">导出Excel</el-button>
        <el-button type="primary"
                   @click="deleteClick"  v-permissions="'auth:personnelAdministrationRetire:del'">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                           type="index"
                           width="60"></el-table-column>
          <el-table-column label="姓名"
                           min-width="220"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.name}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="性别"
                           min-width="120"
                           prop="gender"></el-table-column>
          <el-table-column label="出生年月"
                           min-width="160"
                           prop="birthday">
          </el-table-column>
          <el-table-column label="民族"
                           min-width="120"
                           prop="nation"></el-table-column>
          <el-table-column label="政治面貌"
                           width="180"
                           prop="politicCountenance">
          </el-table-column>
          <el-table-column label="入党时间"
                           width="180"
                           prop="joinPartyDate">
          </el-table-column>
          <el-table-column label="退休时间"
                           width="180"
                           prop="retireDate">
          </el-table-column>
          <el-table-column label="原职务"
                           width="180"
                           prop="duty">
          </el-table-column>
          <el-table-column label="级别"
                           width="180"
                           prop="rank">
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="100">
            <template slot-scope="scope">
              <el-button @click="editor(scope.row)"
                         type="primary"
                         plain
                         v-permissions="'auth:personnelAdministrationRetire:edit'"
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :type="1214"
                 :excelId="choose.join(',')"
                 @callback="exportCallback"></zy-export>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
export default {
  name: 'personnelAdministrationRetire',
  data () {
    return {
      keyword: '',
      years: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      exportShow: false,
      gender: '',
      nation: '',
      birthday: '',
      politicCountenance: '',
      duty: '',
      rank: '',
      joinPartyDate: '',
      retireDate: '',
      genderData: [],
      nationData: [],
      dutyData: [],
      rankData: [],
      politicCountenanceData: []
    }
  },
  mixins: [tableData],
  components: {
  },
  inject: ['newTab', 'refresh'],
  mounted () {
    this.years = this.$format(new Date(), 'YYYY')
    this.PublicOpinionList()
    this.dictionaryPubkvs()
  },
  activated () {
    this.PublicOpinionList()
  },
  methods: {
    reset () {
      this.keyword = ''
      this.gender = ''
      this.nation = ''
      this.birthday = ''
      this.politicCountenance = ''
      this.duty = ''
      this.rank = ''
      this.joinPartyDate = ''
      this.retireDate = ''
      this.PublicOpinionList()
    },
    /**
     * 字典
     */
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'sex,nation_type,politic_countenance_type,retiree_rank_type,duty_type'
      })
      var { data } = res
      this.genderData = data.sex
      this.nationData = data.nation_type
      this.politicCountenanceData = data.politic_countenance_type
      this.dutyData = data.duty_type
      this.rankData = data.retiree_rank_type
    },
    details (row, type) {
      this.refresh()
      this.newTab({ name: '退休人员信息管理详情', menuId: row.id, to: '/personnelAdministrationDetails', params: { id: row.id, type: type, name: '退休' } })
    },
    editor (row) {
      this.refresh()
      this.newTab({ name: '退休人员信息编辑', menuId: row.id, to: '/personnelAdministrationRetireAdd', params: { id: row.id, nameType: 1 } })
    },
    async PublicOpinionList () {
      this.timeIndex = this.timeIndex + 1
      var datas = {
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        gender: this.gender,
        nation: this.nation,
        birthday: this.birthday,
        politicCountenance: this.politicCountenance,
        duty: this.duty,
        rank: this.rank,
        joinPartyDate: this.joinPartyDate,
        retireDate: this.retireDate
      }
      const res = await this.$api.appManagement.retireeinfoList(datas)
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.socialinfodels(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async socialinfodels (id) {
      const res = await this.$api.appManagement.retireeinfoDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.PublicOpinionList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    // 导出
    exportClick () {
      if (this.choose.length === 0) {
        return this.$message.warning('请至少选中一条数据进行导出！')
      }
      this.exportShow = true
    },
    exportCallback () {
      this.choose = []
      this.selectObj = []
      this.PublicOpinionList()
      this.exportShow = false
    },
    howManyArticle (val) {
      this.PublicOpinionList()
    },
    whatPage (val) {
      this.PublicOpinionList()
    },
    search () {
      this.page = 1
      this.PublicOpinionList()
    }
  }
}
</script>
<style lang="scss">
.personnelAdministrationRetire {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
