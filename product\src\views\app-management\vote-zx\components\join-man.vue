<template>
  <div class="join-man">
    <div class="search-box">
      <el-input size="mini"
                v-model="keyword"
                placeholder="请输入关键字"
                @keydown.native.enter="search"></el-input>
      <el-button size="mini"
                 type="primary"
                 @click="search">查询</el-button>
      <el-button size="mini"
                 @click="reset">重置</el-button>
    </div>
    <div class="btn-box">
      <el-button type="primary"
                 size="mini"
                 @click="handleExport">导出</el-button>
    </div>
    <div class="tableData scrollBar">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fisVisibleixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                           type="index"
                           width="80"></el-table-column>
          <el-table-column label="姓名"
                           min-width="120"
                           prop="userName"
                           show-overflow-tooltip></el-table-column>
          <el-table-column label="填写时间"
                           min-width="200"
                           prop="updateDate"></el-table-column>
          <el-table-column label="投票详情"
                           min-width="100">
            <template slot-scope="scope">
              <span class="t-color"
                    @click="OpenDetail(scope.row)">查看</span>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="page-box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :excelId="excelId"
                 :params="params"
                 :type="108"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  props: {
    id: String
  },
  inject: ['newTab'],
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      selectionList: [],
      params: null,
      exportShow: false,
      excelId: null
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 列表
    getList () {
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        id: this.id
      }
      if (this.keyword !== '') {
        data.keyword = this.keyword
      }
      data.module = 'questionnaire'

      this.$api.appManagement.joinVoteUserList(data).then(res => {
        const { data, errcode, total } = res
        if (errcode === 200) {
          this.total = total
          this.tableData = data
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 查看详情
    OpenDetail (val) {
      console.log(val)
      this.newTab({ name: '投票详情', menuId: val.id, to: '/questionPurview', params: { id: this.id, user: { userId: val.userId, userName: val.userName } } })
    },
    // 导出
    handleExport () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选中想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.params = {
        paperId: this.$route.query.id
      }
      this.exportShow = true
    }
  }
}
</script>

<style lang="scss">
@import "./dialog-list.scss";
</style>
