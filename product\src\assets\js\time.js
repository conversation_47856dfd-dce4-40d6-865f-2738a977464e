export default {
  // 日期格式化
  /**
  * 将 Date 转化为指定格式的String
  * 月(M)、日(D)、12小时(h)、24小时(H)、分(m)、秒(s)、周(E)、季度(q)、早午晚(T) 可以用 1-2 个占位符
  * 年(Y)可以用 1-4 个占位符，毫秒(S)只能用 1 个占位符(是 1-3 位的数字)
  * eg:
  * format(new Date(),"YYYY-MM-DD hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
  * format(new Date(),"YYYY-MM-DD E HH:mm:ss") ==> 2009-03-10 二 20:09:04
  * format(new Date(),"YYYY-MM-DD EE hh:mm:ss") ==> 2009-03-10 周二 08:09:04
  * format(new Date(),"YYYY-MM-DD EEE hh:mm:ss") ==> 2009-03-10 星期二 08:09:04
  * format(new Date(),"YYYY-M-D h:m:s.S") ==> 2006-7-2 8:9:4.18
  * format(new Date(),"YYYY/MM/DD (EEE) T hh:mm") ==> 2017/09/06 (星期三) 下午 05:01
  *
  */
  DateUtil: {
    date (val) {
      if (!val) return false
      if (!val.getTime) {
        const arr = val.split(/[- : T /]/)
        return new Date(arr[0], arr[1] - 1, arr[2], arr[3], arr[4], arr[5])
      } else {
        return val
      }
    },
    format (date, fmt) {
      date = this.date(date)
      const o = {
        'M+': date.getMonth() + 1, // 月份
        'D+': date.getDate(), // 日
        'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
        'H+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
        S: date.getMilliseconds() // 毫秒
      }
      const week = {
        0: '\u65e5',
        1: '\u4e00',
        2: '\u4e8c',
        3: '\u4e09',
        4: '\u56db',
        5: '\u4e94',
        6: '\u516d'
      }
      const time = {
        0: '\u51cc\u6668', // 上午
        1: '\u4e0a\u5348', // 下午
        2: '\u4e0b\u5348', // 晚上
        3: '\u665a\u4e0a' // 凌晨
      }
      if (/(Y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
      }
      if (/(E+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, ((RegExp.$1.length > 1) ? (RegExp.$1.length > 2 ? '/u661f/u671f' : '/u5468') : '') + week[date.getDay() + ''])
      }
      if (/(T)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, time[parseInt(date.getHours() / 6)])
      }
      for (const k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
        }
      }
      return unescape(fmt.replace(/\/u/g, '%u'))
    }
  }
}
