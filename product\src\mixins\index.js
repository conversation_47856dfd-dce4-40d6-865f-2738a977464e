// import { resetRouter } from '../router/index'
export default {
  methods: {
    exit () {
      this.$confirm('此操作将退出当前系统, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.logout()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消退出'
        })
      })
    },
    async logout () {
      const res = await this.$api.general.logout({})
      var { errcode } = res
      if (errcode === 200) {
        this.$resetRouter()
        sessionStorage.clear()
        this.$router.push({ name: 'login' })
        // resetRouter()
        // console.log(this.$router)
        this.$message({
          type: 'success',
          message: '退出成功!'
        })
      }
    }
  }
}
