<template>
  <div class="ActivitiesThrough">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="remarks"
                   clearable
                   placeholder="请选择活动主体">
          <el-option v-for="item in remarksData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.value">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button @click="newData"
                   type="primary"
                   v-permissions="'auth:ActivitiesThrough:add'">新增</el-button>
        <el-button @click="deleteClick"
                   v-permissions="'auth:ActivitiesThrough:del'">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="序号"
                           width="80"
                           type="index"></el-table-column>
          <el-table-column label="标题"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.name}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="活动大类"
                           width="160"
                           prop="bigType"></el-table-column>
          <el-table-column label="活动主体"
                           width="90"
                           prop="remarks"></el-table-column>
          <!-- <el-table-column label="活动小类"
                           width="160"
                           prop="smallType"></el-table-column> -->
          <el-table-column label="活动日期"
                           width="190">
            <template slot-scope="scope">{{scope.row.dataTime|datefmt('YYYY-MM-DD')}}</template>
          </el-table-column>
          <el-table-column label="参与人数"
                           width="100"
                           prop="num"></el-table-column>
          <el-table-column label="创建人"
                           width="120"
                           prop="createName"></el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="editor(scope.row)"
                         type="primary"
                         plain
                         v-permissions="'auth:ActivitiesThrough:edit'"
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="show"
                      :title="id?'编辑':'新增'">
      <AddActivitiesThrough :id="id"
                            type="1"
                            @callback="addCallback"></AddActivitiesThrough>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      title="活动详情">
      <ActivitiesThroughDetails :id="id"></ActivitiesThroughDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import AddActivitiesThrough from './AddActivitiesThrough'
import ActivitiesThroughDetails from './ActivitiesThroughDetails'
export default {
  // 活动补录
  name: 'ActivitiesThrough',
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false,
      detailsShow: false,
      remarks: '',
      remarksData: [
        { id: '001', value: '专委会活动' },
        { id: '002', value: '界别活动' },
        { id: '003', value: '委员工作站活动' }]
    }
  },
  mixins: [tableData],
  components: {
    AddActivitiesThrough,
    ActivitiesThroughDetails
  },
  mounted () {
    this.activityfillList()
  },
  methods: {
    // 搜索
    search () {
      this.page = 1
      this.activityfillList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.activityfillList()
    },
    // 列表接口请求
    async activityfillList () {
      const res = await this.$api.activity.activityfillList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        remarks: this.remarks
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    // 新增
    newData () {
      this.id = ''
      this.show = true
    },
    // 详情
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    // 编辑
    editor (row) {
      this.id = row.id
      this.show = true
    },
    addCallback () {
      this.activityfillList()
      this.show = false
    },
    // 分页
    howManyArticle (val) {
      this.activityfillList()
    },
    // 分页
    whatPage (val) {
      this.activityfillList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的活动补录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.activityfillDels(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async activityfillDels (id) {
      const res = await this.$api.activity.activityfillDels({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.activityfillList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.ActivitiesThrough {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
