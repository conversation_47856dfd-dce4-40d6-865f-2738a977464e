<template>
  <div class="generalTimeNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="届"
                    class="form-input"
                    prop="circlesId">
        <el-select v-model="form.circlesId"
                   filterable
                   clearable
                   placeholder="请选择届">
          <el-option v-for="item in circlesId"
                     :key="item.value"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="次"
                    class="form-input"
                    prop="boutId">
        <el-select v-model="form.boutId"
                   filterable
                   clearable
                   placeholder="请选择次">
          <el-option v-for="item in boutId"
                     :key="item.value"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="年份"
                    class="form-input"
                    prop="boutYear">
        <el-date-picker v-model="form.boutYear"
                        type="year"
                        value-format="yyyy"
                        placeholder="选择年份">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="状态"
                    class="form-input"
                    prop="circlesStatus">
        <el-select v-model="form.circlesStatus"
                   filterable
                   clearable
                   placeholder="请选择状态">
          <el-option v-for="item in circlesStatus"
                     :key="item.value"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="全称"
                    class="form-title"
                    prop="fullName">
        <el-input placeholder="请输入全称"
                  v-model="form.fullName"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="简称"
                    class="form-input"
                    prop="simpleName">
        <el-input placeholder="请输入简称"
                  v-model="form.simpleName"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="届次编号"
                    class="form-input"
                    prop="code">
        <el-input placeholder="请输入届次编号"
                  v-model="form.code"
                  clearable>
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'generalTimeNew',
  data () {
    return {
      form: {
        circlesId: '',
        boutId: '',
        code: '',
        fullName: '',
        simpleName: '',
        boutYear: '',
        circlesStatus: ''
      },
      rules: {
        circlesId: [
          { required: true, message: '请选择届', trigger: 'blur' }
        ],
        boutId: [
          { required: true, message: '请选择次', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入届次编号', trigger: 'blur' }
        ],
        fullName: [
          { required: true, message: '请输入全称', trigger: 'blur' }
        ],
        simpleName: [
          { required: true, message: '请输入简称', trigger: 'blur' }
        ],
        boutYear: [
          { required: true, message: '请选择年份', trigger: 'blur' }
        ],
        circlesStatus: [
          { required: true, message: '请选择状态', trigger: 'blur' }
        ]
      },
      circlesId: [],
      boutId: [],
      circlesStatus: []
    }
  },
  props: ['id', 'memberType'],
  mounted () {
    this.dictionaryPubkvs()
    if (this.id) {
      this.historycirclesInfo()
    }
  },
  methods: {
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'circles_type,bout_type,circles_bout_status'
      })
      var { data } = res
      this.circlesId = data.circles_type
      this.boutId = data.bout_type
      this.circlesStatus = data.circles_bout_status
    },
    async historycirclesInfo () {
      const res = await this.$api.memberInformation.historycirclesInfo(this.id)
      var { data } = res
      this.form.circlesId = data.circlesId
      this.form.boutId = data.boutId
      this.form.code = data.code
      this.form.fullName = data.fullName
      this.form.simpleName = data.simpleName
      this.form.boutYear = data.boutYear
      this.form.circlesStatus = data.circlesStatus
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/historycircles/add?'
          if (this.id) {
            url = '/historycircles/edit?'
          }
          this.$api.systemSettings.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            circlesId: this.form.circlesId,
            boutId: this.form.boutId,
            code: this.form.code,
            fullName: this.form.fullName,
            simpleName: this.form.simpleName,
            boutYear: this.form.boutYear,
            circlesStatus: this.form.circlesStatus,
            memberType: this.memberType
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.generalTimeNew {
  width: 682px;
}
</style>
