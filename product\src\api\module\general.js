// 导入封装的方法
import Vue from 'vue'
import {
  get,
  post,
  postform,
  exportFile,
  filedownload,
  loginUc,
  baseURL,
  yunpan,
  // overdue,
  postformProgress
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
const general = {
  // 登陆
  overdue () {
    // var oDate1 = new Date(overdue)
    // var oDate2 = new Date()
    // return oDate1.getTime() > oDate2.getTime()
    return true
  },
  // 登陆
  export (params) {
    exportFile('/socialanalyse/exportArticleClass', params)
  },
  dsjuser () {
    return JSON.parse(sessionStorage.getItem('BigDataUser' + Vue.prototype.$logo()))
  },
  baseURL () {
    return baseURL
  },
  yunpan () {
    return yunpan
  },
  generalAdd (url, params) {
    return post(url, params)
  },
  // 模板导入
  generalFile (url, params) {
    return filedownload(url, params)
  },
  // 下载模板
  generalImportemplate (url, params) {
    exportFile(url, params)
  },
  loginUc (params) {
    return post(`${loginUc}/login?`, params)
  },
  login (params) {
    return post('/user/login', params)
  },
  loginimg (params) {
    return post('/loginimg/show', params)
  },
  logout (params) {
    return post('/user/logout', params)
  },
  findRolesByUserId (params) {
    return post('/role/findRolesByUserId', params)
  },
  sendmessage (params) { //  发送短信
    return post('/shortcode/send', params)
  },
  editpwd (params) { //  修改密码
    return post('/wholeuser/editpwd/byaccount', params)
  },
  enable (params) {
    return post('/shortcode/enable', params)
  },
  changearea (params) {
    return post('/changearea', params)
  },
  meetControlPeople (params) {
    return post(`lzt/meetcontrol/findControl?meetId=${params}`)
  },
  pointrees (params) {
    return post(`/pointrees/${params}`)
  },
  poinexistsids (params) {
    return post(`/poinexistsids/${params}`)
  },
  meetpointrees (params) {
    return post(`/meetMeeting/pointTrees/${params}`)
  },
  pointreeUsers (params) {
    return post('/pointree/users', params)
  },
  pointreeGroupUsers (params) {
    return post('/meetMeeting/treeNodeUsers', params)
  },
  file (params) {
    return postform('/file/uploadimg4name', params)
  },
  fileImg (params) {
    return postform('/file/uploadimg', params)
  },
  apptoken (params) {
    // return postform('/gain/apptoken?', params)
    return postform(`${loginUc}/gain/apptoken?`, params)
  },
  getToken (params) {
    return post('http://1.192.128.76:18083/front/getToken', params)
  },
  getokearams (params) {
    return post('/valley/getokearams', params)
  },
  websiteLogin (params) {
    return post('/valley/login?', params)
  },
  searchData (params) {
    return post('/pushmsg/search', params)
  },
  searchPush (params) {
    return post('/socket/push', params)
  },
  exportFields (params) {
    return post('/export/fields?', params)
  },
  exportDatas (params) {
    return post('/export/datas', params)
  },
  testvoice (params) {
    return postform('/demo/testvoice', params)
  },
  // 履职
  // 履职档案列表
  findDutys (params) {
    return postform('/duty/findDutys?', params)
  },
  // 履职档案列表详情
  getDutyDetail (params) {
    return postform('/duty/getDutyDetail?', params)
  },
  // 我的履职档案详情
  getDutyDetailByUserId (params) {
    return postform('/duty/getDutyDetailByUserId', params)
  },
  // 生成履职档案
  generateDuty (params) {
    return postform('/duty/generateDuty?', params)
  },
  // 大数据登录
  noauthLogin (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/noauth/login`, params)
  },
  // 推荐建议提案分类
  classify (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/smart/classify`, params)
  },
  // 建议相似度
  similarity (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/smart/proposal/similarity`, params)
  },
  // 建议联名人推荐
  joinUser (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/smart/joinUser`, params)
  },
  // 内容质量分析接口
  qualityAnalysis (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/smart/qualityAnalysis`, params)
  },
  // 建议办理单位推荐
  handle (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/smart/handle`, params)
  },
  // 错别字
  corrector (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/smart/corrector`, params)
  },
  // 新增浏览
  browSeAdd (params) {
    return post('/browse/save', params)
  },
  // 获取公开的配置管理
  nologin (params) {
    return post('/readonfig/nologin?', params)
  },
  // 获取不公开的配置管理
  readonfig (params) {
    return post('/readonfig', params)
  },
  // 履职服务首页统计数据
  dutyHomeCount (params) {
    return post('/count/homepage', params)
  },
  // 获取通用的树
  getTreeData (params) {
    return post('/tree/list', params)
  },
  // 首页活动
  activityList (params) {
    return post('/activity/list?', params)
  },
  // 首页通知
  noticeList (params) {
    return post('/notice/list?', params)
  },
  // 首页资讯
  zyinfodetailList (params) {
    return post('/zyinfodetail/list?', params)
  },
  // 首页代表信息待审核
  userauditList (params) {
    return post('/useraudit/list', params)
  },
  // 首页建议的统计
  suggestHomeCount (params) {
    return post('/suggest/suggestHomeCount', params)
  },
  // 考试待发布
  paperList (params) {
    return post('/paper/list?', params)
  },
  // 首页双联信件待回复
  interknitmailList (params) {
    return post('/interknitmail/list?', params)
  },
  // 首页提案相关状态
  proposalCount (params) {
    return post('/proposal/proposalCount?', params)
  },
  // 首页社情民意待查阅
  socialinfoList (params) {
    return post('/socialinfo/list?', params)
  },
  // 首页远程协商进行中
  zyMeetingList (params) {
    return post('/zyMeeting/list?', params)
  },
  // 获取当前届次
  crrentcircles (params) {
    return post('/member/crrentcircles?', params)
  },

  // 评论列表
  commentList (params) {
    return post('/comment/list?', params)
  },
  // 评论审核
  commentAudit (params) {
    return post('/comment/audit', params)
  },
  // 评论删除
  commentDel (params) {
    return post('/comment/dels', params)
  },
  // 导出评论回复
  commentExport (params) {
    return exportFile('/comment/export', params)
  },
  // 建议相似度
  mergesuggest (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/proposal/mergesuggest`, params)
  },
  // 可并案的相似的提案
  similarToMerge (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return post(`${dsjUrl}/smart/similarToMerge`, params)
  },
  // 热词统计
  aggKeywords (params) {
    var dsjUrl = JSON.parse(sessionStorage.getItem('BigDataUrl' + Vue.prototype.$logo()))
    return get(`${dsjUrl}/inner/aggKeywords`, params)
  },
  // 最新提案
  proposalList (params) {
    return post('/proposal/list', params)
  },
  // 最新建议
  suggestList  (params) {
    return post('/suggest/list', params)
  },
  // 最新建议
  suggestCount  (params) {
    return post('/suggest/suggestCount', params)
  },
  // 批量导入头像
  uploadimgs  (params) {
    return filedownload('/wholeuser/uploadimgs', params)
  },
  // 批量导入头像
  findAllRoleAttach  (params) {
    return post('/role/attach/findAllRoleAttach', params)
  },
  uploadFile (params, callback, id) {
    return postformProgress('/attachment/uploadFile', params, 880000, callback, id)
  }
}
export default general
