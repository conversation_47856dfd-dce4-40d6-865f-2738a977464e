
export function getweek () {
  return `星期${'日一二三四五六'.charAt(new Date().getDay())}`
}

export function getmonth () {
  const mm = new Date().getMonth() + 1
  let month = ''
  switch (mm) {
    case 1:
      month = '一月'
      break
    case 2:
      month = '二月'
      break
    case 3:
      month = '三月'
      break
    case 4:
      month = '四月'
      break
    case 5:
      month = '五月'
      break
    case 6:
      month = '六月'
      break
    case 7:
      month = '七月'
      break
    case 8:
      month = '八月'
      break
    case 9:
      month = '九月'
      break
    case 10:
      month = '十月'
      break
    case 11:
      month = '十一月'
      break
    case 12:
      month = '十二月'
      break
  }
  return month
}
export function getday () {
  return new Date().getDate()
}
export function compare (property) {
  return function (obj1, obj2) {
    var value1 = obj1[property]
    var value2 = obj2[property]
    return value1 - value2 // 升序
  }
}
