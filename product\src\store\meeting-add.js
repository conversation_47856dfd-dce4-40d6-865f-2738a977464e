/*
 * @Author: wuxxing
 * @Date: 2021-04-09 11:23:58
 * @LastEditTime: 2021-05-10 10:58:25
 * @Description: 模块需要TODO:非全局
 */
const meetingAdd = {
  namespaced: true,
  state: {
    baseInfo: {
      id: '', // 会议id -- 新增可不传
      parentId: '', // 子级会议需要
      tempName: '', // 模板名称
      name: '',
      startTime: '',
      endTime: '',
      planTime: '',
      meetPlaceId: '',
      meetTypeId: '',
      officeId: '',
      agenda: '',
      content: ''
    }, // 基本信息
    presentMan: [], // 出席人员
    attendMan: [], // 列席人员
    meetFiles: [], // 会议材料
    type: '' // 区分操作
  },
  // 异步的改变数据
  actions: {
    // 获取通讯录个人
    GET_baseInfo({ commit }, val) {
      commit('SET_baseInfo', val)
    },
    // 获取通讯录个人
    GET_pickMan({ commit }, val) {
      commit('SET_pickMan', val)
    }
  },
  // 唯一能修改数据变化的地方
  mutations: {
    // 修改会议基本信息数据
    SET_baseInfo(state, data) {
      state.baseInfo = data
    },
    // 修改出席人员数据
    SET_presentMan(state, data) {
      state.presentMan = data
    },
    // 修改列席人员数据
    SET_attendMan(state, data) {
      state.attendMan = data
    },
    // 修改会议材料数据
    SET_meetFiles(state, data) {
      state.meetFiles = data
    },
    // 修改type
    SET_type(state, data) {
      state.type = data
    },
    // 初始化
    initData(state) {
      state.baseInfo = {
        id: '',
        parentId: '',
        tempName: '',
        name: '',
        startTime: '',
        endTime: '',
        planTime: '',
        meetPlaceId: '',
        meetTypeId: '',
        officeId: '',
        agenda: '',
        content: '',
        fileIds: '' // 材料id
      }
      state.presentMan = []
      state.attendMan = []
      state.meetFiles = []
      state.type = ''
    }
  },
  // 监听数据变化
  getters: {
    baseInfo: state => state.baseInfo,
    presentMen: state => state.presentMan,
    attendMen: state => state.attendMan,
    meetFiles: state => state.meetFiles,
    type: state => state.type
  }
}
export default meetingAdd
