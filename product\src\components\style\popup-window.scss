.xyl-popup-window {
  .xyl-popup-window-cover {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .xyl-popup-window-content {
    position: fixed;
    height: 85%;
    max-height: 85%;
    background-color: #fff;
    z-index: 1000;
    overflow: hidden;
    .xyl-popup-window-head {
      width: 100%;
      height: 52px;
      background: $zy-color;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      cursor: move;
      .xyl-popup-window-title {
        color: #fff;
      }
      .el-icon-close {
        color: #fff;
        cursor: pointer;
        font-size: 18px;
      }
    }
    .xyl-popup-window-body {
      width: 100%;
      height: calc(100% - 52px);
      .el-scrollbar__wrap {
        overflow-x: hidden;
        margin-bottom: 0 !important;
      }
      .is-vertical {
        .el-scrollbar__thumb {
          background-color: rgba(144, 147, 153, 0.8);
        }
      }
      .inputClass {
        .el-select {
          width: 221px;
          .el-input {
            width: 221px !important;
          }
        }
        .newForm {
          .el-select {
            width: 295px;
            .el-input {
              width: 295px !important;
            }
          }
        }
        .newFormPaper {
          .el-select {
            width: 99% !important;
          }
        }
      }
    }
  }
}
