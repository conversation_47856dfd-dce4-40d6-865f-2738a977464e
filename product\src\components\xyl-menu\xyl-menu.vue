<template>
  <el-scrollbar class="xyl-menu">
    <el-menu :default-active="key"
             :default-openeds="parent"
             :background-color="backgroundColor"
             :text-color="textColor"
             :active-text-color="activeTextColor"
             @select="select">
      <div v-for="(item) in menu"
           :key="item[props.id]">
        <el-submenu :index="item[props.id]"
                    v-if="isShowChildren(item)">
          <template #title>
            <!-- <div class="xyl-menu-icon">
              <img :src="item[props.icon]"
                   alt="">
            </div> -->
            <span class="menu-color">{{item[props.label]}}</span>
          </template>
          <div v-for="(items) in item[props.children]"
               class="xyl-menu-popover-div-box"
               :key="items[props.id]">
            <xyl-menu-children :data="items[props.children]"
                               :parent="item[props.id]"
                               v-if="isShowChildren(items)"
                               :props="props">{{items[props.label]}}</xyl-menu-children>
            <el-menu-item :index="items[props.id]"
                          v-else>
              <span slot="title">{{items[props.label]}}</span>
            </el-menu-item>
          </div>
        </el-submenu>
        <el-menu-item :index="item[props.id]"
                      v-else>
          <!-- <div class="xyl-menu-icon">
            <img :src="item[props.icon]"
                 alt="">
          </div> -->
          <span slot="title"
                class="menu-color">{{item[props.label]}}</span>
        </el-menu-item>
      </div>
    </el-menu>
  </el-scrollbar>
</template>
<script>
import xylMenuChildren from './xyl-menu-children'
export default {
  name: 'xylMenu',
  data () {
    return {
      parent: [],
      key: ''
    }
  },
  provide () {
    return {
      menu: this
    }
  },
  props: {
    value: [String, Number, Array, Object],
    menu: {
      type: Array,
      default: () => []
    },
    textColor: {
      type: String,
      default: ''
    },
    backgroundColor: {
      type: String,
      default: ''
    },
    activeTextColor: {
      type: String,
      default: ''
    },
    // 树结构配置
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label',
          id: 'id',
          to: 'to',
          icon: 'iconUrl',
          isShow: 'isShow',
          showValue: true
        }
      }
    }
  },
  components: {
    xylMenuChildren
  },
  watch: {
    value (val) {
      if (val) {
        this.key = val
        this.selectData(this.menu, val)
      }
    }
  },
  mounted () {
    this.key = this.value
    this.selectData(this.menu, this.value)
  },
  methods: {
    select (key) {
      this.key = key
      this.$emit('input', key)
      // this.selectData(this.menu, key)
    },
    selectData (data, id) {
      data.forEach(item => {
        if (item[this.props.children].length === 0) {
          if (item[this.props.id] === id) {
            this.$emit('select', item)
          }
        } else {
          this.selectData(item[this.props.children], id)
        }
      })
    },
    isShowChildren (menu) {
      let isShow = false
      if (menu[this.props.children].length) {
        isShow = menu[this.props.children].some(item => item[this.props.isShow] === this.props.showValue)
      }
      return isShow
    }
  }
}
</script>
<style lang="scss">
@import "./xyl-menu.scss";
</style>
