<template>
  <div class="goodWordsDetails details">
    <div class="details-title">金玉良言详情</div>
    <div class="details-item-box">
      <div class="details-item-title">
        <div class="details-item-label">笔记内容</div>
        <div class="details-item-value">{{details.content}}</div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">笔记作者</div>
          <div class="details-item-value">{{details.createBy}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">创建时间</div>
          <div class="details-item-value">{{details.uploadTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">关联书籍</div>
        <div class="details-item-value">
          <div class="goodWordsNewItem"
               v-if="book">
            <div class="goodWordsNewItemImg">
              <img :src="book.coverImgUrl"
                   alt="">
            </div>
            <div class="goodWordsNewItemBox">
              <div class="goodWordsNewItemName">{{book.bookName}}</div>
              <div class="goodWordsNewItemIntroduction">{{book.bookDescription}}</div>
              <div class="goodWordsNewItemAuthor">
                <div class="goodWordsNewItemAuthorText">{{book.authorName}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="details-content"
           v-html="details.noteContent"></div> -->
    </div>
  </div>
</template>
<script>
export default {
  name: 'goodWordsDetails',
  data () {
    return {
      details: {},
      book: null
    }
  },
  props: ['id'],
  components: {
  },
  mounted () {
    this.goldworInfo()
  },
  methods: {
    async goldworInfo () {
      const res = await this.$api.academy.goldworInfo({ id: this.id })
      var { data } = res
      this.details = data
      if (data.book) {
        this.book = data.book
      }
    }
  }
}
</script>
<style lang="scss">
.goodWordsDetails {
  height: 100%;
  padding: 24px;

  .goodWordsNewItem {
    display: flex;
    justify-content: space-between;
    width: 632px;
    height: 128px;
    margin: 12px 0;
    cursor: pointer;
    .goodWordsNewItemImg {
      height: 128px;
      width: 95px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .goodWordsNewItemBox {
      width: 522px;
      height: 100%;
      position: relative;
      .goodWordsNewItemName {
        color: #333;
        line-height: 21px;
        font-size: 16px;
        margin-bottom: 7px;
      }
      .goodWordsNewItemIntroduction {
        line-height: 24px;
        color: #666;
        letter-spacing: 0.93px;
        height: 72px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 13px;
        white-space: normal !important;
      }
      .goodWordsNewItemAuthor {
        position: absolute;
        left: 0;
        bottom: -2px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .goodWordsNewItemAuthorText {
          font-size: 13px;
          color: #999;
          letter-spacing: 1px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
  .details-content {
    width: 100%;
    padding: 40px;
    line-height: 26px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
