<template>
  <div class="overallAnalysis">
    <div class="analysis-wrap-left">
      <div class="overall analysis-item">
        <div class="title">
          <span class="color-block"></span>
          资讯
        </div>
        <div class="overall-wrap">
          <div class="overall-item">
            <div class="left">
              <p>原创资讯</p>
              <p>{{countOverall.localCount}}</p>
            </div>
            <div class="right">
              <img src="@/assets/images/yuanchuang.png"
                   alt="/" />
            </div>
          </div>
          <div class="overall-item">
            <div class="left">
              <p>抓取资讯</p>
              <p>{{countOverall.tookCount}}</p>
            </div>
            <div class="right">
              <img src="@/assets/images/dongtaixinxizhuaqu.png"
                   alt="/" />
            </div>
          </div>
          <div class="overall-item">
            <div class="left">
              <p>总和</p>
              <p>{{countOverall.count}}</p>
            </div>
            <div class="right">
              <img src="@/assets/images/zongtu.png"
                   alt="/" />
            </div>
          </div>
        </div>
      </div>
      <div class="analysis-word">
        <div class="focus-point analysis-item">
          <div class="title">
            <span class="color-block"></span>
            关注点
          </div>
          <div id="bubble"
               style="width:100%; height:370px;"> </div>
        </div>
        <div class="hot-word analysis-item">
          <div class="title">
            <span class="color-block"></span>
            热词
          </div>
          <ve-wordcloud :data="chartData.data"
                        :settings="chartData.setting"
                        :extend="chartData.extend"></ve-wordcloud>
        </div>
      </div>
      <div class="read-time analysis-item">
        <div class="title">
          <span class="color-block"></span>
          阅览时间段统计
        </div>
        <ve-line :data="readTime.data"
                 :extend="readTime.extend"
                 :settings="readTime.setting">
        </ve-line>
      </div>
    </div>
    <div class="analysis-wrap-right">
      <div class="time-box analysis-item">
        <el-date-picker v-model="month"
                        type="monthrange"
                        range-separator="至"
                        value-format="yyyy-MM"
                        start-placeholder="开始月份"
                        end-placeholder="结束月份">
        </el-date-picker>
        <el-button type="primary"
                   @click="search"></el-button>
      </div>
      <div class="welcome analysis-item">
        <div class="title">
          <span class="color-block"></span>
          资讯类型欢迎度
        </div>
        <ve-histogram :data="welcome.data"
                      :extend="welcome.extend"
                      :settings="welcome.setting"
                      height="400px"></ve-histogram>
      </div>
      <div class="Activity analysis-item">
        <div class="title">
          <span class="color-block"></span>
          平台用户活跃度
        </div>
        <ve-ring :data="activity.data"
                 :extend="activity.extend"
                 :settings="activity.setting"></ve-ring>
      </div>
    </div>
  </div>
</template>

<script>
import veWordcloud from 'v-charts/lib/wordcloud'
import { initBubbleChart } from './bubble.js'
import VeLine from 'v-charts/lib/line.common'
import VeHistogram from 'v-charts/lib/histogram.common'
import VeRing from 'v-charts/lib/ring.common'
import _ from 'lodash'
import echarts from 'echarts'
import 'echarts/lib/component/title'
export default {
  components: { veWordcloud, VeLine, VeHistogram, VeRing },
  data () {
    return {
      year: '',
      chartData: {
        data: {
          columns: ['key', 'count'],
          rows: []
        },
        setting: {
          color: [
            '#0AC934',
            '#0AC952',
            '#0AC970',
            '#0AC98E',
            '#0AC9AC',
            '#0AC9CA',
            '#0AC9E8'],
          sizeMin: 20,
          sizeMax: 40,
          shape: 'diamond'
        },
        extend: {
          'series.0.rotationRange': [-60, 60]
        }
      },
      resize: null,
      countOverall: {},
      bubbleChart: null,
      readTime: {
        data: {
          columns: ['key', 'count'],
          rows: [
            { key: '1', count: 45 },
            { key: '2', count: 23 },
            { key: '3', count: 89 },
            { key: '4', count: 56 }
          ]
        },
        setting: {
          label: {
            show: true,
            position: 'right',
            color: '#ccc'
          }
        },
        extend: {
          grid: {
            right: '40px',
            left: '40px'
          },
          legend: {
            show: false
          },
          title: {
            show: false
          },
          xAxis: {
            show: true,
            boundaryGap: false,
            axisLine: {
              color: '#EEE'
            },
            splitLine: {
              show: false
            }
          },
          tooltip: {
            formatter: v => {
              return `小时:${v[0].name}<br/>
              阅览:${v[0].value}件<br/>`
            }
          },
          yAxis: {
            axisLine: {
              show: false
            }
          },
          'series.0.itemStyle': {
            normal: {
              lineStyle: { color: '#39D8F0', width: 2 },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: 'rgba(57,216,240,0.3)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(57,216,240,0.6)'
                  }
                ])
              }
            }
          }
        }
      },
      month: [],
      welcome: {
        data: {
          columns: ['key', 'count'],
          rows: []
        },
        setting: {
          legendName: {
            count: '欢迎度'
          },
          label: {
            show: true,
            position: 'top',
            color: '#ccc'
          }
        },
        extend: {
          grid: {
            left: '20px',
            right: '20px',
            containLabel: true
          },
          legend: {
            show: true,
            right: '10px',
            top: '15px',
            orient: 'vertical'
          },
          xAxis: {
            show: true,
            splitLine: {
              show: false
            }
          },
          tooltip: {
            formatter: v => {
              return `类型:${v[0].name}<br/>
              欢迎度:${v[0].value}`
            }
          },
          yAxis: {
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            }
          },
          barWidth: '15px',
          'series.0.itemStyle': {
            normal: {
              color: '#3DE7A8',
              barBorderRadius: [5, 5, 0, 0]
            }
          }
        }
      },
      activity: {
        data: {
          columns: ['key', 'count'],
          rows: []
        },
        extend: {
          legend: {
            show: true,
            left: '10px',
            bottom: '50px',
            orient: 'vertical'
          },
          title: {
            text: '',
            x: 'center',
            top: '48%',
            textStyle: {
              fontSize: 32
            }
          }

        },
        setting: {
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          itemStyle: {
            color: seriesIndex => {
              if (seriesIndex.dataIndex === 1) {
                return {
                  type: 'linear',
                  x: 0,
                  y: 1,
                  x2: 0,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#4a8eea' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#75b9f9' // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#77c5f7' // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#78d0f5' // 100% 处的颜色
                    }
                  ],
                  global: false // 缺省为 false
                }
              } else {
                return '#dbdbe3'
              }
            }
          }
        }
      }
    }
  },
  created () {
    this.getData()
  },
  mounted () {
    this.resize = window.onresize = _.debounce(() => {
      this.bubbleChart.resize()
    }, 1000)
  },
  methods: {
    getData () {
      this.$api.smartHelper.newsCountOverall().then(res => {
        this.countOverall = res.data
      })
      this.$api.smartHelper.newsCountHotWord().then(res => {
        this.chartData.data.rows = res.data
      })
      this.$api.smartHelper.newsCountReadNum().then(res => {
        this.readTime.data.rows = Object.keys(res.data).map(v => {
          return {
            key: v,
            count: res.data[v]
          }
        }).map(v => {
          v.key = v.key.split('info')[1]
          return v
        })
      })
      this.$api.smartHelper.newsCountAggTags().then(res => {
        this.bubbleChart = initBubbleChart(res.data, ['key', 'count'], 'bubble', '#fff')
      })
      this.$api.smartHelper.newsCountLikeRate().then(res => {
        this.welcome.data.rows = res.data
      })
      this.$api.smartHelper.newsCountActivity().then(res => {
        const { count, activeCount } = res.data
        this.activity.data.rows = [{ key: '不活跃总量', count: count - activeCount }, { key: '活跃用户量', count: activeCount }]
        this.activity.extend.title.text = `${((activeCount / count) * 100).toFixed(2)}%`
      })
    },
    search () { }
  },
  destroyed () {
    this.resize = null
  }
}
</script>

<style lang="scss" scoped>
.overallAnalysis {
  display: flex;
  background-color: #f9f9f9;
  .analysis-item {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    background-color: #fff;
    border-radius: 8px;
  }
  .analysis-wrap-left {
    width: calc(70% - 10px);
    margin-right: 20px;
    margin-top: 20px;
    .overall {
      .overall-wrap {
        height: 200px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        .overall-item {
          height: 160px;
          width: calc(100% / 3 - 50px);
          border-radius: 8px;
          display: flex;
          .left {
            width: 70%;
            color: #fff;
            padding: 30px 20px;
            box-sizing: border-box;
            > p:nth-child(1) {
              font-size: 28px;
            }
            > p:nth-child(2) {
              font-size: 36px;
              text-align: center;
              line-height: 90px;
            }
          }
          .right {
            width: 30%;
            display: flex;
            align-items: center;
            justify-content: center;
            > img {
              width: 48px;
            }
          }
        }
        .overall-item:nth-child(1) {
          background: linear-gradient(
            to right,
            #55ceff 0%,
            #55ceff 70%,
            #a1deff 70%,
            #a1deff 100%
          );
        }
        .overall-item:nth-child(2) {
          background: linear-gradient(
            to right,
            #01e5de 0%,
            #01e5de 70%,
            #73e8ee 70%,
            #73e8ee 100%
          );
        }
        .overall-item:nth-child(3) {
          background: linear-gradient(
            to right,
            #6549fe 0%,
            #6549fe 70%,
            #aa9afe 70%,
            #aa9afe 100%
          );
        }
      }
    }
    .analysis-word {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      .hot-word {
        width: calc(50% - 12px);
      }
      .focus-point {
        width: calc(50% - 12px);
      }
    }
    .read-time {
      margin-top: 20px;
    }
  }
  .analysis-wrap-right {
    width: calc(30% - 10px);
    .time-box {
      width: 100%;
      height: 54px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 24px;
      padding: 0 8px;
    }
    .welcome {
      width: 100%;
      margin-top: 24px;
    }
    .Activity {
      width: 100%;
      margin-top: 24px;
    }
  }
  .title {
    height: 36px;
    border-bottom: 1px solid #dcdfe6;
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0 10px;
    .color-block {
      width: 4px;
      height: 24px;
      border-radius: 2px;
      background-color: #016def;
      display: inline-block;
      margin-right: 10px;
    }
  }
}
</style>
