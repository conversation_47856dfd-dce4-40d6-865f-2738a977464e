// 导入封装的方法
import {
  post,
  postform,
  exportFile
} from '../http'

const membersDuty = {
  // 代表值班的导出
  memberGuestExport (data) {
    return exportFile('/officeonlinetopic/export', data)
  },
  // 来信导出
  letterExport (data) {
    return exportFile('/officeonlineletter/export', data)
  },
  // 获取值班代表
  memberList (data) {
    return post(`/officeonlineschedule/info/${data}`)
  },
  // 导入值班excel
  memberListExport (data) {
    return postform('/officeonlinetopic/import', data)
  },

  // 委员值班列表
  officeonlinescheduleList (params) {
    return post('/officeonlineschedule/list?', params)
  },
  // 委员值班列表删除
  officeonlinescheduleDel (params) {
    return post('/officeonlineschedule/dels', params)
  },
  // 委员值班主题列表
  officeonlinetopicList (params) {
    return post('/officeonlinetopic/list?', params)
  },
  // 委员值班来信列表
  officeonlineletterList (params) {
    return post('/officeonlineletter/list?', params)
  },
  // 委员值班来信列表详情
  officeonlineletterInfo (params) {
    return post(`/officeonlineletter/info/${params}`)
  },
  // 委员值班来信审核
  officeonlineletterAudit (params) {
    return post('/officeonlineletter/publish', params)
  },
  // 委员值班来信删除
  officeonlineletterDel (params) {
    return post('/officeonlineletter/dels', params)
  },
  // 设置来信默认公开状态
  officeonlineletterEditSwitch (data) {
    return post('/officeonlineletter/editSwitch', data)
  },
  // 获取来信默认公开状态
  officeonlineletterShowSwitch (data) {
    return post('/officeonlineletter/showSwitch', data)
  }
}

export default membersDuty
