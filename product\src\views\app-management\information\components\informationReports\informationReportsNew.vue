<template>
  <div class="informationReportsNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="标题"
                    class="form-title"
                    prop="title">
        <el-input placeholder="请输入标题"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="时间"
                    class="form-input"
                    prop="publishDate">
        <el-date-picker v-model="form.publishDate"
                        type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        placeholder="选择发布时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="序号"
                    class="form-input"
                    prop="sort">
        <el-input-number v-model="form.sort"
                         placeholder="请输入序号"
                         :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="内容"
                    prop="content"
                    class="form-ue">
        <UEditor v-model="form.content"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'informationReportsNew',
  data () {
    return {
      form: {
        title: '',
        publishDate: '',
        sort: '',
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        publishDate: [
          { required: true, message: '选择发布时间', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id', 'mosaicId'],
  mounted () {
    if (this.mosaicId) {
      this.reportInfo()
    } else {
      this.form.publishDate = this.$format(new Date(), 'YYYY-MM-DD HH:mm:ss')
    }
  },
  methods: {
    async reportInfo () {
      const res = await this.$api.appManagement.reportInfo(this.mosaicId)
      var { data: { title, publishDate, content, sort } } = res
      this.form.title = title
      this.form.sort = sort
      this.form.publishDate = publishDate
      this.form.content = content
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/zyinforeportpic/scrolling/report/add'
          if (this.mosaicId) {
            url = '/zyinforeportpic/scrolling/report/edit'
          }
          this.$api.systemSettings.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.mosaicId,
            detailId: this.id,
            title: this.form.title,
            publishDate: this.form.publishDate,
            sort: this.form.sort,
            content: this.form.content
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.informationReportsNew {
  width: 682px;

  .form-icon {
    width: 100%;

    .form-icon-uploader {
      width: 148px;
      height: 148px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 148px;
        height: 148px;
        line-height: 148px;
        text-align: center;
      }

      .user-img {
        width: 148px;
        height: 148px;
        display: block;
      }
    }
  }
}
</style>
