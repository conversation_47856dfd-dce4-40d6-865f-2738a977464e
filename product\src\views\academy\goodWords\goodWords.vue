<template>
  <div class="goodWords">
    <xyl-search-button @search="search"
                       @reset="reset"
                       :searchNumber="2">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="isIssue"
                   filterable
                   clearable
                   placeholder="请选择是否选登">
          <el-option v-for="item in isIssueData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-date-picker v-model="time"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
      </template>
      <template slot="button">
        <el-button @click="newData"
                   type="primary">新增</el-button>
        <el-button @click="deleteClick"
                   type="primary">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="选登标记"
                           width="120"
                           prop="sort">
            <template slot-scope="scope">
              <div class="Anchoose"
                   v-if="scope.row.isIssue == 1">
                <i class="el-icon-medal-1"></i>
                <div>登选中</div>
              </div>
              <div class="undo"
                   v-else>
                <i class="el-icon-medal"></i>
                <div>未登选</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="内容"
                           min-width="260"
                           show-overflow-tooltip>
            <template slot-scope="scope">
              <el-button type="text"
                         @click="details(scope.row)">{{scope.row.content}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="关联书籍"
                           width="190">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="library(scope.row.bookId)">{{scope.row.bookName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="上传时间"
                           width="190">
            <template slot-scope="scope">{{scope.row.uploadTime|datefmt('YYYY-MM-DD HH:mm:ss')}}</template>
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="220">
            <template slot-scope="scope">
              <el-button @click="addeditor(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
              <el-button @click="addeditors(scope.row)"
                         type="primary"
                         plain
                         size="mini">{{scope.row.isIssue=='1'?'撤下':'选登'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="show"
                      :title="id?'修改书籍金玉良言':'新增书籍金玉良言'">
      <goodWordsNew :id="id"
                    @newCallback="newCallback"></goodWordsNew>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      title="书籍金玉良言详情">
      <goodWordsDetails :id="id"></goodWordsDetails>
    </xyl-popup-window>
    <xyl-popup-window v-model="libraryShow"
                      title="书籍详情">
      <libraryDetails :id="id"></libraryDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import libraryDetails from '../library/libraryDetails'
import goodWordsNew from './goodWordsNew'
import goodWordsDetails from './goodWordsDetails'
export default {
  name: 'goodWords',
  data () {
    return {
      keyword: '',
      isIssue: '',
      isIssueData: [
        { id: '1', value: '是' },
        { id: '0', value: '否' }
      ],
      time: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false,
      detailsShow: false,
      libraryShow: false
    }
  },
  mixins: [tableData],
  components: {
    libraryDetails,
    goodWordsNew,
    goodWordsDetails
  },
  mounted () {
    this.goldwordList()
  },
  methods: {
    search () {
      this.page = 1
      this.goldwordList()
    },
    reset () {
      this.keyword = ''
      this.isIssue = ''
      this.time = ''
      this.goldwordList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    library (row) {
      this.id = row
      this.libraryShow = true
    },
    addeditor (row) {
      this.id = row.id
      this.show = true
    },
    newCallback () {
      this.goldwordList()
      this.show = false
    },
    async goldwordList () {
      const res = await this.$api.academy.goldwordList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword,
        isIssue: this.isIssue,
        beginDate: this.time ? this.time[0] : '',
        endDate: this.time ? this.time[1] : ''
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle () {
      this.goldwordList()
    },
    whatPage () {
      this.goldwordList()
    },
    addeditors (row) {
      if (row.isIssue === 1) {
        this.goldwordUnDigest(row.id)
      } else {
        this.goldwordDigest(row.id)
      }
    },
    async goldwordDigest (id) {
      const res = await this.$api.academy.goldwordDigest({
        id: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.goldwordList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async goldwordUnDigest (id) {
      const res = await this.$api.academy.goldwordUnDigest({
        id: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.goldwordList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的书籍金玉良言, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.goldwordDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async goldwordDel (id) {
      const res = await this.$api.academy.goldwordDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.goldwordList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.goodWords {
  height: 100%;
  width: 100%;
  .tableData {
    height: calc(100% - 116px);
    .Anchoose {
      width: 52px;
      text-align: center;
      i,
      div {
        color: $zy-color;
      }
    }
    .undo {
      width: 52px;
      text-align: center;
      i,
      div {
        color: #666;
      }
    }
  }
}
</style>
