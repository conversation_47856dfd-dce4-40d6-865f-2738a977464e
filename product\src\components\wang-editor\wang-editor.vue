<template>
  <div :id="editorId"
       class="wang-editor"></div>
</template>

<script>
// 引入富文本编辑器
import WangEditor from 'wangeditor'
import axios from 'axios'
export default {
  name: 'wang-editor',
  props: {
    value: {
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      editor: '',
      editorId: ''
    }
  },
  watch: {
    value (newval) {
      if (this.editor) {
        if (newval !== this.editor.txt.html()) {
          this.editor.txt.html(newval)
        }
      }
    }
  },
  model: {
    prop: 'value', // 这个字段，是指父组件设置 v-model 时，将变量值传给子组件的 msg
    event: 'input'// 这个字段，是指父组件监听 parent-event 事件
  },
  methods: {
    // // 生成一个随机不重复id,可以通过时间和随机数生成
    randomId () {
      const baseId = 'wang_editor'
      const now = new Date().getTime()
      return `${baseId}_${now}`
    },
    // 初始化编辑器
    initEditor () {
      const _this = this
      _this.editorId = _this.randomId()// 生成一个id
      this.$nextTick(() => {
        // 获取实例,wangEditor是被注册在window的
        const editor = new WangEditor('#' + _this.editorId)
        _this.editor = editor// 将实例保存待调用其他api
        _this.setConfig()
        editor.create()// 开始创建编辑器；
        _this.editor.txt.html(this.value)
        // 设置是否可编辑
        if (this.disabled !== 'undefined') {
          this.editor.$textElem.attr('contenteditable', !this.disabled)
        }
      })
    },
    // 创建富文本编辑器
    setConfig () {
      var _this = this
      // 开始创建
      const setting = {
        uploadImgShowBase64: false, // 是否允许上传base64位图片
        pasteFilterStyle: true, // 是否过滤粘贴的样式
        zIndex: 100, // 设置层叠位置
        // 菜单列表
        menus: [
          'head', // 标题
          'bold', // 粗体
          'fontSize', // 字号
          'fontName', // 字体
          'italic', // 斜体
          'indent', // 缩进
          'lineHeight', // 行高
          'underline', // 下划线
          'strikeThrough', // 删除线
          'foreColor', // 文字颜色
          'backColor', // 背景颜色
          'link', // 插入链接
          'list', // 列表
          'justify', // 对齐方式
          'quote', // 引用
          'emoticon', // 表情
          'image', // 插入图片
          'table', // 表格
          'video', // 插入视频
          // 'code', // 插入代码
          'undo', // 撤销
          'redo' // 恢复
        ],
        showLinkImg: true, // 是否显示“网络图片”tab
        onchange: function (html) {
          _this.$emit('input', html)
        },
        onlineVideoCallback: v => {
          if (v.endsWith('.mp4')) {
            _this.editor.cmd.do(
              'insertHTML', `<video src="${v}" controls="controls" style="max-width:100%"></video>`
            )
          }
        },
        customUploadImg: (resultFiles, insertImgFn) => {
          const formData = new FormData()
          formData.append('upfile', resultFiles[0])
          axios.post(`${_this.$api.general.baseURL()}/ueditor/exec?action=uploadimage`, formData).then(res => {
            insertImgFn(res.data.url)
          })
        }
      }
      // 配置给编辑器
      _this.editor.config = Object.assign(_this.editor.config, setting)
    }
  },
  created () {
    // 创建editor实例
    this.initEditor()
  }
}

</script>
<style lang="scss" >
.wang-editor {
  width: 100%;
  .w-e-text-container {
    .w-e-text {
      img {
        width: 80%;
        height: auto;
        text-align: center;
      }
    }
  }
}
</style>
