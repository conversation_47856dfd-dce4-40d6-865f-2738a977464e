<template>
  <div class="join-man static">
    <div class="search-box">
      <el-input size="mini"
                v-model="keyword"
                placeholder="请输入关键字"></el-input>
      <el-button size="mini"
                 type="primary"
                 @click="search">查询</el-button>
      <el-button size="mini"
                 @click="reset">重置</el-button>
    </div>
    <div class="btn-box">
      <el-button size="mini"
                 type="primary"
                 @click="handleExport">导出</el-button>
    </div>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="80"></el-table-column>
          <el-table-column label="姓名"
                           prop="userName"
                           min-width="150"></el-table-column>
          <el-table-column label="选项"
                           min-width="150"
                           show-overflow-tooltip>
            <template>
              <div v-if="topic === 'judge'">
                <p v-if="option == '0'"><i class="el-icon-check check-color"></i></p>
                <p v-if="option == '1'"><i class="el-icon-close close-color"></i></p>
              </div>
              <div v-if="topic === 'multi' || topic ==='single'">
                <p>{{option}}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="填写时间"
                           prop="createDate"
                           min-width="250"></el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="page-box">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export :excelId="excelId"
                 :params="params"
                 :type="111"
                 @callback="exportShow = false"></zy-export>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  props: {
    id: String,
    question: Object,
    option: String
  },
  data () {
    return {
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      topic: '',
      selectionList: [],
      params: null,
      exportShow: false,
      excelId: null
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    // 列表
    getList () {
      this.topic = this.question.topic
      const data = {
        pageNo: this.page,
        pageSize: this.pageSize,
        paperId: this.id,
        questionId: this.question.id,
        answer: this.option
      }
      if (this.topic === 'multi' || this.topic === 'single') {
        data.answer = `||${this.option}||`
      }
      this.$api.appManagement.voteOptionManNum(data).then(res => {
        if (res.errcode === 200) {
          this.tableData = res.data
        }
      })
    },
    // 查询
    search () {
      this.page = 1
      this.getList()
    },
    // 重置
    reset () {
      this.keyword = ''
      this.getList()
    },
    // 换分页条数
    handleSizeChange (val) {
      this.pageSize = val
      this.getList()
    },
    // 换页码
    handleCurrentChange (val) {
      this.page = val
      this.getList()
    },
    // 多选
    handleSelectionChange (val) {
      this.selectionList = val
    },
    // 导出
    handleExport () {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要导出的项')
      }
      this.excelId = this.selectionList.map(item => item.id).join(',')
      this.params = {
        paperId: this.$route.query.id
      }
      this.exportShow = true
    }
  }
}
</script>

<style lang="scss">
@import "./dialog-list.scss";
</style>
