.screening-box {
  height: 64px;
  min-height: 64px;
  display: flex;
  flex-wrap: wrap;
  padding-top: 11px;
  border-bottom: 1px solid #e6e6e6;
  box-sizing: border-box;
  overflow: hidden;

  .el-input {
    width: 222px;
  }

  .el-input {
    margin-left: 24px;
    margin-bottom: 12px;
  }

  .el-select {
    margin-left: 24px;
    margin-bottom: 12px;

    .el-input {
      margin-left: 0;
      margin-bottom: 0;
    }
  }

  .zy-select {
    margin-left: 24px;
    margin-bottom: 12px;

    .el-input {
      margin-left: 0;
      margin-bottom: 0;
    }
  }

  .el-date-editor {
    margin-left: 24px;
    margin-bottom: 12px;
  }

  .zy-tree-select {
    width: 222px;
    margin-left: 24px;
    margin-bottom: 12px;

    .el-input {
      margin-left: 0;
      margin-bottom: 0;
    }
  }

  .screening-checkbox {
    width: 222px;
    height: 40px;
    margin-bottom: 12px;
    margin-left: 24px;
    display: flex;
    align-items: center;
  }

  .screening-button {
    width: 246px;

    .el-button {
      height: 40px;
      margin-left: 24px;
      padding: 0 16px;
    }

    .el-button+.el-button {
      margin-left: 16px;
    }

    .el-button--text {
      margin-left: 9px;
      font-size: 14px;
      padding: 0;

      .el-icon-arrow-down {
        transition-duration: .4s;
        transform: rotate(0);
      }

      .el-icon-arrow-down-a {
        transition-duration: .4s;
        transform: rotate(-180deg);

      }
    }
  }
}