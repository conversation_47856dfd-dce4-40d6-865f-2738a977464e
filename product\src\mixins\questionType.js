export default {
  data () {
    return {
      type: '',
      typeList: [],
      topic: '',
      topicList: [
        { value: 'single', label: '单选' },
        { value: 'multi', label: '多选' },
        { value: 'judge', label: '判断' }
        // { value: 'text', label: '文本' }
      ]
    }
  },
  created () {
    this.getTypeList()
  },
  methods: {
    // 获取所属分类
    getTypeList () {
      // this.$api.systemSettings.dictionaryPubkvs({ types: 'paper_type' }).then(res => {
      //   if (res.errcode === 200) {
      //     this.typeList = res.data.paper_type
      //   }
      // })
      this.$api.systemSettings.treeList({ treeType: 13 }).then(res => {
        if (res.errcode === 200) {
          this.typeList = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
