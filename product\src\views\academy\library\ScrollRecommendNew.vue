<template>
  <div class="ScrollRecommendNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="推荐词（推荐词最多不超过30字）"
                    prop="recommendedWord"
                    class="form-title">
        <el-input placeholder="请输入推荐词"
                  v-model="form.recommendedWord"
                  show-word-limit
                  maxlength="30"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item class="form-input"
                    label="序号">
        <el-input placeholder="请输入序号"
                  type="number"
                  v-model="form.sort"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="封面图"
                    prop="imgId"
                    class="form-img">
        <el-upload class="form-img-uploader"
                   action="/"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :show-file-list="false">
          <img v-if="form.fileImg"
               :src="form.fileImg"
               class="user-img">
          <i v-else
             class="el-icon-plus user-uploader-icon"></i>
        </el-upload>
      </el-form-item>
      <el-form-item label="关联书籍"
                    class="form-title">
        <el-button type="primary"
                   @click="BooksClick"
                   class="ScrollRecommendNewButton">选择书籍</el-button>
        <div class="ScrollRecommendNewItemBox">
          <div class="ScrollRecommendNewItem"
               v-for="(item, index) in book"
               :key="index">
            <div class="ScrollRecommendNewItemImg">
              <img :src="item.coverImgUrl"
                   alt="">
            </div>
            <div class="ScrollRecommendNewItemsBox">
              <div class="ScrollRecommendNewItemName">{{item.bookName}}</div>
              <div class="ScrollRecommendNewItemIntroduction">{{item.bookDescription}}</div>
              <div class="ScrollRecommendNewItemAuthor">
                <div class="ScrollRecommendNewItemAuthorText">{{item.authorName}}</div>
                <div class="libraryAllItemButton">
                  <el-button type="text"
                             @click.stop="bookDel(item)">删除</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
    <xyl-popup-window v-model="show"
                      title="选择书籍">
      <RelatedBooks :data="book"
                    :max="1"
                    @callback="callback"></RelatedBooks>
    </xyl-popup-window>
  </div>
</template>
<script>
import RelatedBooks from '../RelatedBooks/RelatedBooks'
export default {
  name: 'ScrollRecommendNew',
  data () {
    return {
      form: {
        recommendedWord: '',
        sort: '',
        imgId: '',
        fileImg: ''
      },
      rules: {
        recommendedWord: [
          { required: true, message: '请输入推荐词', trigger: 'blur' }
        ],
        imgId: [
          { required: true, message: '请上传封面图', trigger: 'blur' }
        ]
      },
      fileImg: {},
      show: false,
      book: []
    }
  },
  props: ['id'],
  components: {
    RelatedBooks
  },
  mounted () {
    if (this.id) {
      this.syRollBookInfo()
    }
  },
  methods: {
    handleImg (file, fileList) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension3 = testmsg === 'png'
      const extension4 = testmsg === 'jpg'
      if (!extension3 && !extension4) {
        this.$message({
          message: '上传文件只能是图片格式!',
          type: 'warning'
        })
      }
      return extension3 || extension4
    },
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'recommendpic')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        this.form.imgId = data[0].id
        this.form.fileImg = data[0].filePath
      }).catch(() => {
        files.onError()
      })
    },
    async syRollBookInfo () {
      const res = await this.$api.academy.syRollBookInfo({ id: this.id })
      var { data } = res
      this.form.recommendedWord = data.recommendedWord
      this.form.sort = data.sort
      if (data.coverImg) {
        this.form.imgId = data.coverImg
        this.form.fileImg = data.coverImgUrl
      }
      if (data.book) {
        this.book = [data.book]
      }
    },
    BooksClick () {
      this.show = true
    },
    callback (data, type) {
      if (data.length) {
        this.book = data
      }
      this.show = false
    },
    bookDel (item) {
      var books = this.book
      this.book = books.filter(book => book.id !== item.id)
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.book.length === 0) {
            this.$message({
              message: '请选择关联书籍！',
              type: 'warning'
            })
            return
          }
          var book = []
          this.book.forEach(item => {
            book.push(item.id)
          })
          let url = '/syRollBook/add'
          if (this.id) {
            url = '/syRollBook/edit'
          }
          this.$api.general.generalAdd(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            recommendedWord: this.form.recommendedWord,
            sort: this.form.sort,
            bookId: book.join(','),
            coverImg: this.form.imgId
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.ScrollRecommendNew {
  width: 682px;
  .form-img {
    width: 100%;

    .form-img-uploader {
      width: 128px;
      height: 128px;
      border: 1px dashed #ccc;

      &:hover {
        border-color: #199bc5;
      }

      .user-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 128px;
        height: 128px;
        line-height: 128px;
        text-align: center;
      }

      .user-img {
        width: 128px;
        height: 128px;
        display: block;
      }
    }
  }
  .ScrollRecommendNewButton {
    height: 36px;
    padding: 0 16px;
  }
  .ScrollRecommendNewItemBox {
    display: flex;
    flex-wrap: wrap;
    padding-top: 12px;
    .ScrollRecommendNewItem {
      display: flex;
      justify-content: space-between;
      margin-right: 24px;
      margin-bottom: 24px;
      width: 362px;
      height: 128px;
      cursor: pointer;
      .ScrollRecommendNewItemImg {
        height: 128px;
        width: 95px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .ScrollRecommendNewItemsBox {
        width: 252px;
        height: 100%;
        position: relative;
        .ScrollRecommendNewItemName {
          color: #333;
          line-height: 21px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-size: 16px;
          margin-bottom: 7px;
        }
        .ScrollRecommendNewItemIntroduction {
          line-height: 24px;
          color: #666;
          letter-spacing: 0.93px;
          height: 72px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          font-size: 13px;
        }
        .ScrollRecommendNewItemAuthor {
          position: absolute;
          left: 0;
          bottom: -2px;
          width: 100%;
          height: 26px;
          line-height: 26px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .ScrollRecommendNewItemAuthorText {
            font-size: 13px;
            color: #999;
            letter-spacing: 1px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .libraryAllItemButton {
            .el-button {
              font-size: 13px;
              padding: 0;
              span {
                display: inline-block;
                line-height: 16px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
