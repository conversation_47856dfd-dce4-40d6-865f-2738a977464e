<template>
  <div class="activityAskLeaveNew">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm">
      <el-form-item label="请假人"
                    prop="userId"
                    class="form-input">
        <el-select v-model="form.userId"
                   filterable
                   clearable
                   placeholder="请选择请假人">
          <el-option v-for="item in userData"
                     :key="item.userId"
                     :label="item.userName"
                     :value="item.userId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="请假理由"
                    prop="reasonChoose"
                    class="form-input">
        <el-select v-model="form.reasonChoose"
                   filterable
                   clearable
                   placeholder="请选择请假理由">
          <el-option v-for="item in reasonChoose"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传请假凭证"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleFile"
                   :http-request="fileUpload"
                   :file-list="fileData"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="内容"
                    class="form-ue">
        <el-input type="textarea"
                  :autosize="{ minRows: 9, maxRows: 12}"
                  placeholder="请输入内容"
                  v-model="form.content">
        </el-input>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">提交</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'activityAskLeaveNew',
  data () {
    return {
      form: {
        userId: '',
        reasonChoose: '',
        content: ''
      },
      rules: {
        userId: [
          { required: true, message: '请选择请假人', trigger: 'blur' }
        ],
        reasonChoose: [
          { required: true, message: '请选择请假理由', trigger: 'blur' }
        ]
      },
      fileData: [],
      userData: [],
      reasonChoose: []
    }
  },
  props: ['id', 'meetId'],
  mounted () {
    this.leaveTreeData()
    this.dictionaryPubkvs()
    if (this.id) {
      this.leaveInfo()
    }
  },
  methods: {
    handleFile (file, fileList) {
    },
    fileUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'leaveFile')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.fileData.push(data[0])
      }).catch(() => {
        files.onError()
      })
    },
    beforeRemove (file, fileList) {
      var fileData = this.fileData
      this.fileData = fileData.filter(item => item.id !== file.id)
    },
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'act_leave_type'
      })
      var { data } = res
      this.reasonChoose = data.act_leave_type
    },
    // 请假树
    async leaveTreeData () {
      const res = await this.$api.activity.leaveTreeData({
        meetId: this.meetId
      })
      var { data } = res
      this.userData = data
    },
    async leaveInfo () {
      const res = await this.$api.activity.leaveInfo(this.id)
      var { data } = res
      this.form.userId = data.userId
      this.form.reasonChoose = data.reasonChoose
      this.form.content = data.reason
      if (data.attachmentList) {
        data.attachmentList.forEach((item, index) => {
          item.uid = item.id
          item.name = item.fileName
        })
        this.fileData = data.attachmentList
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var userName = ''
          this.userData.forEach(item => {
            if (item.userId === this.form.userId) {
              userName = item.userName
            }
          })
          var attachmentIds = []
          this.fileData.forEach(item => {
            attachmentIds.push(item.id)
          })
          this.form.attachmentIds = attachmentIds.join(',')
          this.form.meetId = this.meetId
          let url = '/leave/add?'
          if (this.id) {
            url = '/leave/edit?'
          }
          this.$api.activity.activityscheduleAddorEdit(url, {
            // empty: '1', // 置空过滤标识
            id: this.id,
            userId: this.form.userId,
            userNames: userName,
            reasonChoose: this.form.reasonChoose,
            reason: this.form.content,
            meetId: this.meetId,
            attachmentIds: attachmentIds.join(',')
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('newCallback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('newCallback')
    }
  }
}
</script>
<style lang="scss">
.activityAskLeaveNew {
  width: 682px;
}
</style>
