<template>
  <div class="poll-add">
    <el-form :model="form"
             ref="addform"
             class="newForm"
             :rules="rule"
             inline
             label-position="top">
      <el-form-item label="主题"
                    prop="name"
                    class="form-title">
        <el-input v-model="form.name"
                  placeholder="请输入主题"></el-input>
      </el-form-item>
      <el-form-item label="类型"
                    prop="type"
                    class="form-title">
        <el-select v-model="form.type"
                   placeholder="请选择类型">
          <el-option v-for="item in typeList"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间-结束时间"
                    prop="time"
                    class="form-title">
        <el-date-picker v-model="form.time"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        value-format="yyyy-MM-dd HH:mm:ss">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="说明"
                    class="form-title">
        <el-input type="textarea"
                  v-model="form.instructions"></el-input>
      </el-form-item>
      <el-form-item label="是否所有人"
                    class="form-input">
        <el-radio-group size="mini"
                        v-model="form.alluser">
          <el-radio-button :label="1">是</el-radio-button>
          <el-radio-button :label="0">否</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否发布"
                    class="form-input">
        <el-radio-group size="mini"
                        v-model="form.isRelease">
          <el-radio-button :label="1">是</el-radio-button>
          <el-radio-button :label="0">否</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="参与用户"
                    v-if="form.alluser === 0"
                    class="form-title">
        <p>
          <el-button size="small"
                     type="primary"
                     @click="addJoinMan">添加用户</el-button>
        </p>
        <div class="join-man"
             v-if="!form.alluser">
          <span class="label">已选人员:</span>
          <el-tag class="tag"
                  v-for="(item,index) in userData"
                  :key="index"
                  @close="deleteJoinMan(index)"
                  closable>
            {{item.name}}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="是否分享"
                    class="form-input">
        <el-radio-group size="mini"
                        v-model="form.isShare">
          <el-radio-button :label="1">是</el-radio-button>
          <el-radio-button :label="0">否</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否匿名">
        <el-radio-group size="mini"
                        v-model="form.isAnonymous">
          <el-radio-button :label="1">是</el-radio-button>
          <el-radio-button :label="0">否</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <div class="form-btn">
        <el-button type="primary"
                   @click="onSubmit('addform')">提交</el-button>
      </div>
    </el-form>
    <!-- <zy-pop-up v-model="userShow" :title="'选择值班'+$position()"> -->
    <zy-pop-up v-model="userShow"
               :title="'选择参与用户'">
      <candidates-user point="point_19"
                       :data="userData"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>

<script>
import _ from 'lodash'
export default {
  props: {
    id: String
  },
  data () {
    return {
      form: {
        name: '',
        type: null,
        time: [],
        instructions: '',
        isRelease: 1,
        isShare: 1,
        isAnonymous: 0,
        alluser: 1
      },
      typeList: [
        { label: '单选', value: 1 },
        { label: '多选', value: 2 }
      ],
      rule: {
        name: [{ required: true, message: '请输入主题', trigger: 'blur' }],
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        time: [{ required: true, message: '请选择时间', trigger: 'change' }]
      },
      userData: [],
      userShow: false
    }
  },
  created () {
    this.getInfo()
  },
  methods: {
    // 查看详情
    getInfo () {
      if (!this.id) return
      this.$api.appManagement.pollInfo(this.id).then(res => {
        const { type, name, instructions, isShare, isAnonymous, isRelease, startTime, endTime, alluser, chooseUserVo } = res.data
        this.form.type = type
        this.form.name = name
        if (alluser === 0) {
          this.userData = chooseUserVo.map(item => {
            return {
              userId: item.userId,
              name: item.name,
              userName: item.username
            }
          })
        }
        this.form = {
          type: type,
          name: name,
          instructions: instructions,
          isShare: isShare,
          time: [startTime, endTime],
          isAnonymous: isAnonymous,
          isRelease: isRelease,
          alluser: alluser
        }
      })
    },
    // 激活选人
    addJoinMan () {
      this.userShow = !this.userShow
    },
    // 选人回调
    userCallback (data, type) {
      if (type) this.userData = data
      this.userShow = !this.userShow
    },
    // 删除已选人员
    deleteJoinMan (index) {
      this.userData.splice(index, 1)
    },
    // 提交
    onSubmit (formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const data = _.cloneDeep(this.form)
          data.startTime = this.form.time[0]
          data.endTime = this.form.time[1]
          delete data.time
          if (!this.form.alluser) {
            data.userIds = this.userData.map(v => v.userId).join(',')
          }
          if (this.id) {
            data.id = this.id
            this.$api.appManagement.pollEdit(data).then(() => {
              this.$message.success('编辑投票成功')
              this.$emit('callback')
            })
          } else {
            this.$api.appManagement.pollAdd(data).then(() => {
              this.$message.success('新增投票成功')
              this.$emit('callback')
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.poll-add {
  width: 682px;
  .join-man {
    width: 100%;
    border: 1px solid #ededed;
    border-radius: 4px;
    padding: 0 10px;
    overflow: hidden;
    max-height: 150px;
    .label {
      color: $zy-color;
    }
    .tag {
      margin-left: 10px;
    }
  }
  .join-man:hover {
    overflow-y: overlay;
  }
  .join-man::-webkit-scrollbar {
    width: 6px;
  }
  .join-man::-webkit-scrollbar-track {
    border-radius: 6px;
  }
  .join-man::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.1);
  }
  .form-btn {
    text-align: center;
    height: 56px;
    line-height: 56px;
  }
}
</style>
