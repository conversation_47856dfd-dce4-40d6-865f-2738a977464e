<template>
  <div class="activityAskLeaveDetails details">
    <div class="details-title">活动请假详情</div>
    <div class="details-item-box">
      <div class="details-item">
        <div class="details-item-label">请假人</div>
        <div class="details-item-value">{{details.userNames}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">请假理由</div>
        <div class="details-item-value">{{details.reasonChooseName}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">请假时间</div>
        <div class="details-item-value">{{details.leaveDatetime}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">请假凭证</div>
        <div class="details-item-value">
          <div class="details-item-file"
               v-for="(item, index) in details.attachmentList"
               :key="index"
               @click="fileClick(item)">{{item.fileName}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">请假说明</div>
        <div class="details-item-value reason">{{details.reason}}</div>
      </div>
      <div class="details-item">
        <div class="details-item-label">审核状态</div>
        <div class="details-item-value reason">{{details.statusName}}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'activityAskLeaveDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.leaveInfo()
  },
  methods: {
    async leaveInfo () {
      const res = await this.$api.activity.leaveInfo(this.id)
      var { data } = res
      this.details = data
    },
    fileClick (data) {
      this.$api.proposal.downloadFile({ id: data.id }, data.fileName)
    }
  }
}
</script>
<style lang="scss">
.activityAskLeaveDetails {
  padding: 24px;
  .reason {
    text-overflow: clip !important;
    white-space: normal !important;
  }
}
</style>
