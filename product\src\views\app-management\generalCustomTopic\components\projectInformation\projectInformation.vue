<template>
  <div class="projectInformation">
    <div class="button-box-list">
      <el-button type="primary"
                 v-permissions="'auth:specialsubjectnews:add'"
                 @click="newData">新增</el-button>
      <el-button type="primary"
                 v-permissions="'auth:specialsubjectnews:dels'"
                 @click="deleteClick">删除</el-button>
      <el-input placeholder="请输入内容"
                clearable
                v-model="name"
                @keyup.enter.native="search">
        <div slot="prefix"
             class="input-search"></div>
      </el-input>
    </div>
    <div class="tableData tableSmall">
      <zy-table>
        <el-table slot="zytable"
                  :data="tableData"
                  ref="table"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           width="48">
          </el-table-column>
          <!-- <el-table-column label="标题"
                         show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button @click="details(scope.row)"
                       type="text"
                       size="small">{{scope.row.title}}</el-button>
          </template>
        </el-table-column> -->
          <el-table-column label="排序"
                           width="60"
                           prop="sort">
          </el-table-column>
          <el-table-column label="标题"
                           prop="title"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="发布人"
                           prop="createName"
                           width="120"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="发布时间"
                           width="160"
                           prop="publishDate"
                           show-overflow-tooltip>
          </el-table-column>
          <el-table-column label="是否发布"
                           width="90">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isPublish=='1'"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作"
                           v-if="$hasPermission(['auth:specialsubjectnews:edit'])"
                           width="80">
            <template slot-scope="scope">
              <el-button @click="handleClick(scope.row)"
                         type="text"
                         size="small">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <xyl-popup-window v-model="show"
                      :title="mosaicId==''?'新增专题资讯':'编辑专题资讯'">
      <projectInformationNew :id="id"
                             :mosaicId="mosaicId"
                             @callback="newCallback"></projectInformationNew>
    </xyl-popup-window>

  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import projectInformationNew from './projectInformationNew'
export default {
  name: 'projectInformation',
  data () {
    return {
      name: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      mosaicId: '',
      show: false
    }
  },
  mixins: [tableData],
  props: ['id'],
  components: {
    projectInformationNew
  },
  mounted () {
    if (this.id) {
      this.customTopicinformation()
    }
  },
  methods: {
    search () {
      this.page = 1
      this.customTopicinformation()
    },
    async customTopicinformation () {
      const res = await this.$api.appManagement.customTopicinformation({
        title: this.name,
        subjectId: this.id,
        pageNo: this.page,
        pageSize: this.pageSize
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    handleClick (row) {
      this.mosaicId = row.id
      this.show = true
    },
    howManyArticle (val) {
      this.customTopicinformation()
    },
    whatPage (val) {
      this.customTopicinformation()
    },
    newData () {
      this.mosaicId = ''
      this.show = true
    },
    newCallback () {
      this.customTopicinformation()
      this.show = false
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的专题资讯, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.customTopicinformationDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async customTopicinformationDel (id) {
      const res = await this.$api.appManagement.customTopicinformationDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.customTopicinformation()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.projectInformation {
  width: 988px;
  height: calc(85vh - 52px);
  padding: 16px 24px;
  padding-bottom: 0;

  .button-box-list {
    height: 36px;
    margin-bottom: 12px;

    .el-button {
      width: 64px;
      max-width: 64px;
      min-width: 64px;
      padding: 0;
      height: 36px;
      font-size: 12px;
    }

    .el-input {
      width: 268px;
      float: right;

      .el-input__inner {
        height: 36px;
        line-height: 36px;
        padding-left: 31px;
        font-size: 12px;
        border-radius: 2px;
      }

      .el-input__prefix {
        width: 31px;
        height: 100%;
        display: flex;
        align-items: center;
        left: 0;
        padding-left: 9px;
        box-sizing: border-box;

        .input-search {
          width: 14px;
          height: 14px;
          background: url("../../../../../assets/img/input-search.png");
          background-size: 100% 100%;
        }
      }
    }
  }

  .tableData {
    height: calc(100% - 100px);
  }
}
</style>
