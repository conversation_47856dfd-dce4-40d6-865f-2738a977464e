<template>
  <el-form :model="form"
           class="qd-form vote-add-page-form"
           ref="form"
           label-position="right"
           label-width="150px"
           inline
           :rules="rules">
    <div class="base-form-item">
      <div class="base-form-item-label">页面样式</div>
      <div class="base-form-item-content">
        <el-form-item label="大张图"
                      prop="files"
                      class="form-item-wd100">
          <qd-upload-img v-model="form.files"
                         module="bigImageforVote"></qd-upload-img>
        </el-form-item>
        <el-form-item label="背景图"
                      class="form-item-wd100">
          <qd-upload-img v-model="form.files1"
                         module="bgImageforVote"></qd-upload-img>
        </el-form-item>
        <el-form-item label="配色方案"
                      class="form-item-wd100">
          <el-select v-model="form.color"
                     placeholder="请选择">
            <el-option v-for="item in colourList"
                       :key="item.id"
                       :label="item.value"
                       :value="item.id"
                       style="display:flex;align-items:center;justify-content: space-between;padding:0 10px;">
              <span>{{ item.value }}</span>
              <span class="colour"
                    :style="{backgroundColor: item.id,width:'36px',height:'18px'}"></span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="投票选项显示方式"
                      class="form-item-wd100">
          <el-radio-group v-model="form.displayMode">
            <el-radio :label="1">列表式</el-radio>
            <el-radio :label="2">图表式</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </div>
    <div class="base-form-item">
      <div class="base-form-item-label">基本显示</div>
      <div class="base-form-item-content">
        <el-form-item label="投票页显示"
                      class="form-item-wd100"
                      prop="pageConfig">
          <el-checkbox-group v-model="form.pageConfig">
            <el-checkbox v-for="item in configList"
                         :key="item.id"
                         :label="item.id">{{item.value}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script>
export default {
  data () {
    return {
      form: {
        files: [],
        files1: [],
        displayMode: 1,
        color: '#007BFF',
        pageConfig: []
      },
      rules: {
        files: [{ required: true, message: '请上传图片', trigger: 'file' }],
        pageConfig: [{ required: true, message: '请选择显示项', trigger: 'change' }]
      },
      colourList: [
        { id: '#007BFF', value: '蓝' },
        { id: '#DB222D', value: '红' }
      ],
      configList: []
    }
  },
  created () {
    this.getDictionary()
  },
  methods: {
    // 获取字典
    getDictionary () {
      this.$api.systemSettings.dictionaryPubkvs({ types: 'page_config_type' }).then(res => {
        this.configList = res.data.page_config_type
      })
    },
    validForm () {
      let result = false
      this.$refs.form.validate((valid) => { result = valid })
      return result
    },
    reset () {
      this.$refs.form.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.vote-add-page-form {
  margin: 25px auto;
}
</style>
