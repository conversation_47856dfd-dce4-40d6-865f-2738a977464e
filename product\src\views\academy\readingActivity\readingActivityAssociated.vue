<template>
  <div class="readingActivityAssociated">
    <el-button type="primary"
               @click="determine"
               class="announcementAssociatedButton">确定</el-button>
    <el-button type="primary"
               @click="BooksClick"
               class="announcementAssociatedButton">选择书籍</el-button>
    <div class="readingActivityAssociatedItemBox">
      <div class="readingActivityAssociatedItem"
           v-for="(item, index) in book"
           :key="index">
        <div class="readingActivityAssociatedItemImg">
          <img :src="item.coverImgUrl"
               alt="">
        </div>
        <div class="readingActivityAssociatedItemBoxs">
          <div class="readingActivityAssociatedItemName">{{item.bookName}}</div>
          <div class="readingActivityAssociatedItemIntroduction">{{item.bookDescription}}</div>
          <div class="readingActivityAssociatedItemAuthor">
            <div class="readingActivityAssociatedItemAuthorText">{{item.authorName}}</div>
            <div class="libraryAllItemButton">
              <el-button type="text"
                         @click.stop="bookDel(item)">删除</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <xyl-popup-window v-model="show"
                      title="选择书籍">
      <RelatedBooks :data="book"
                    @callback="callback"></RelatedBooks>
    </xyl-popup-window>
  </div>
</template>
<script>
import RelatedBooks from '../RelatedBooks/RelatedBooks'
export default {
  name: 'readingActivityAssociated',
  data () {
    return {
      show: false,
      book: []
    }
  },
  props: ['id'],
  components: {
    RelatedBooks
  },
  mounted () {
    if (this.id) {
      this.syBookList()
    }
  },
  methods: {
    async syBookList () {
      const res = await this.$api.academy.syBookList({
        pageNo: 1,
        pageSize: 9999,
        schemeId: this.id
      })
      var { data } = res
      this.book = data || []
    },
    bookDel (row) {
      var book = this.book
      this.book = book.filter(item => item.id !== row.id)
    },
    BooksClick () {
      this.show = true
    },
    callback (data, type) {
      this.book = data
      this.show = false
    },
    determine () {
      if (this.book.length) {
        var arr = []
        this.book.forEach(item => {
          arr.push(item.id)
        })
        this.readschemeJoin(arr.join(','))
      } else {
        this.$message({
          message: '请选择关联的书籍！',
          type: 'warning'
        })
      }
    },
    async readschemeJoin (id) {
      const res = await this.$api.academy.readschemeJoin({
        id: this.id,
        bookIds: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$emit('callback')
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.readingActivityAssociated {
  width: 820px;
  padding: 24px;
  .readingActivityAssociatedItemBox {
    display: flex;
    flex-wrap: wrap;
    padding-top: 18px;
    .readingActivityAssociatedItem {
      display: flex;
      justify-content: space-between;
      margin-right: 24px;
      margin-bottom: 24px;
      width: 362px;
      height: 128px;
      cursor: pointer;
      .readingActivityAssociatedItemImg {
        height: 128px;
        width: 95px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .readingActivityAssociatedItemBoxs {
        width: 252px;
        height: 100%;
        position: relative;
        .readingActivityAssociatedItemName {
          color: #333;
          line-height: 21px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          font-size: 16px;
          margin-bottom: 7px;
        }
        .readingActivityAssociatedItemIntroduction {
          line-height: 24px;
          color: #666;
          letter-spacing: 0.93px;
          height: 72px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          font-size: 13px;
        }
        .readingActivityAssociatedItemAuthor {
          position: absolute;
          left: 0;
          bottom: -2px;
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .readingActivityAssociatedItemAuthorText {
            font-size: 13px;
            color: #999;
            letter-spacing: 1px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .libraryAllItemButton {
            .el-button {
              font-size: 13px;
              padding: 0;
              span {
                display: inline-block;
                line-height: 16px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
