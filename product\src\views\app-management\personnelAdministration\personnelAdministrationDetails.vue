<template>
  <div class="personnelAdministrationDetails">
    <el-scrollbar class="personnelAdministrationDetailsBox">
      <div class="details">
        <div class="details-title">{{name}}人员信息
        </div>
        <div class="details-item-box">
          <div class="details-item-column">
            <div class="details-item">
              <div class="details-item-label">姓名</div>
              <div class="details-item-value">{{details.name}}</div>
            </div>
            <div class="details-item">
              <div class="details-item-label">性别</div>
              <div class="details-item-value">{{details.genderName}}</div>
            </div>
          </div>
          <div class="details-item-column">
            <div class="details-item">
              <div class="details-item-label">民族</div>
              <div class="details-item-value">{{details.nationName}}</div>
            </div>
            <div class="details-item">
              <div class="details-item-label">出生年月</div>
              <div class="details-item-value">{{details.birthday}}</div>
            </div>
          </div>
          <div class="details-item-column">
            <div class="details-item">
                  <div class="details-item-label">政治面貌</div>
                  <div class="details-item-value">{{ details.politicCountenanceName}}</div>
              </div>
            <div class="details-item">
              <div class="details-item-label">入党时间</div>
              <div class="details-item-value">{{ details.joinPartyDate}}</div>
            </div>
          </div>
          <div class="details-item-column"  v-if="name === '退休'">
            <div class="details-item">
                  <div class="details-item-label">原职务</div>
                  <div class="details-item-value">{{ details.dutyName}}</div>
              </div>
            <div class="details-item">
              <div class="details-item-label">退休时间</div>
              <div class="details-item-value">{{ details.retireDate}}</div>
            </div>
          </div>
          <div class="details-item" v-if="name === '退休'">
                <div class="details-item-label">级别</div>
                <div class="details-item-value">{{ details.rankName}}</div>
          </div>
          <div class="details-item-column" v-if="name !== '退休'">
            <div class="details-item">
                  <div class="details-item-label">教育情况</div>
                  <div class="details-item-value">{{ details.educationName}}</div>
              </div>
            <div class="details-item">
              <div class="details-item-label">参加工作时间</div>
              <div class="details-item-value">{{ details.joinWorkDate}}</div>
            </div>
          </div>
          <div class="details-item-column" v-if="name !== '退休'">
            <div class="details-item">
                  <div class="details-item-label">所在部门</div>
                  <div class="details-item-value">{{ details.departmentName}}</div>
              </div>
            <div class="details-item">
              <div class="details-item-label">进入政协时间</div>
              <div class="details-item-value">{{ details.joinCppccDate}}</div>
            </div>
          </div>
          <div class="details-item-column" v-if="name !== '退休'">
            <div class="details-item">
                  <div class="details-item-label">职务</div>
                  <div class="details-item-value">{{ details.dutyName}}</div>
              </div>
            <div class="details-item">
              <div class="details-item-label">职级</div>
              <div class="details-item-value">{{ details.rankName}}</div>
            </div>
          </div>
          <div class="details-item-title" v-if="name === '退休'">
            <div class="details-item-label">所获市级以上荣誉</div>
            <div class="details-item-value">
              <div class="PublicOpinionTitleBox">
                <div class="PublicOpinionTitle" v-html="details.honor" style="white-space:pre-line"></div>
              </div>
            </div>
          </div>
          <div class="details-item-title">
            <div class="details-item-label">备注</div>
            <div class="details-item-value">
              <div class="PublicOpinionTitleBox">
                <div class="PublicOpinionTitle" v-html="details.remarks" style="white-space:pre-line"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script>
export default {
  name: 'personnelAdministrationDetails',
  data () {
    return {
      id: this.$route.query.id,
      name: this.$route.query.name,
      details: {},
      opinion: '',
      form: {
        opinion: ''
      },
      file: [],
      sendBackObj: {}
    }
  },
  mounted () {
    if (this.id) {
      this.socialinfolook()
    }
  },
  activated () {
    if (this.id) {
      this.socialinfolook()
    }
  },
  methods: {
    async socialinfolook () {
      var res = ''
      if (this.name === '退休') {
        res = await this.$api.appManagement.retireeinfoInfo(this.id)
      } else {
        res = await this.$api.appManagement.incumbentinfoInfo(this.id)
      }
      var { data } = res
      this.details = data
    }
  }
}
</script>
<style lang="scss">
.personnelAdministrationDetails {
  width: 100%;
  height: 100%;
  .form-upload-demo {
    padding: 12px;
    .el-upload {
      width: 100%;

      .el-upload-dragger {
        height: 79px;
        width: 100%;
        max-width: 952px;
        background-color: #e6e5e8;

        .el-upload__text {
          padding-top: 22px;
          font-size: 14px;
          line-height: 22px;
        }

        .el-upload__tip {
          font-size: 12px;
          line-height: 20px;
          color: #6e6e6e;
          margin-top: 0;
        }
      }
    }
  }
  .details-item-box .details-item-column + .details-item-column{
      border-left: none;
  }
}
.personnelAdministrationDetailsBox {
  width: 100%;
  height: 100%;

  .el-scrollbar__wrap {
    overflow-x: hidden;
  }

  .is-vertical {
    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.8);
    }
  }
}
.details {
  margin: auto;
  padding-bottom: 24px;
}
</style>
