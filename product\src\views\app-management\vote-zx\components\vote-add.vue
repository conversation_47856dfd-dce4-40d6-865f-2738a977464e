<template>
  <div class="vote-add scrollBar">
    <div class="vote-add-box">
      <el-form :model="addform"
               ref="addform"
               :rules="addRule"
               :label-position="'top'">
        <el-form-item label="主题"
                      prop="name">
          <el-input v-model="addform.name"></el-input>
        </el-form-item>
        <el-form-item label="简介"
                      prop="instructions">
          <el-input type="textarea"
                    v-model="addform.instructions"></el-input>
        </el-form-item>
        <el-form-item label="开始时间-结束时间"
                      prop="time">
          <el-date-picker v-model="addform.time"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="发布时间"
                      prop="publishDate">
          <el-date-picker v-model="addform.publishDate"
                          type="datetime"
                          placeholder="发布时间"
                          value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="组织方">
          <zy-cascader width="222"
                       node-key="id"
                       clearable
                       v-model="addform.organize"
                       :data="organizeList"
                       placeholder="组织方">
          </zy-cascader>
        </el-form-item>
        <el-form-item label="参与用户">
          <p>
            <span style="margin-left:10px;">所有人</span>
            <el-switch v-model="isAll"
                       active-color="#94070A"
                       inactive-color="#ddd">
            </el-switch>
          </p>
          <p v-if="!isAll">
            <el-button size="small"
                       type="primary"
                       @click="addMan">添加用户</el-button>
          </p>
          <div class="join-man"
               v-if="!isAll">
            <span class="label">已选人员:</span>
            <el-tag class="tag"
                    v-for="(item,index) in userData"
                    :key="index"
                    @close="deleteMan(index)"
                    closable>{{item.name}}</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="是否发布"
                      prop="isPublish">
          <el-radio-group v-model="addform.isPublish">
            <el-radio :label="'1'">是</el-radio>
            <el-radio :label="'0'">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="onSubmit('addform')">提交</el-button>
          <el-button @click="cancel('addform')">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
    <zy-pop-up v-model="userShow"
               title="选择参与用户">
      <candidates-user point="point_19"
                       :data="userData"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>

<script>
export default {
  props: {
    editData: Object
  },
  data () {
    return {
      isAll: true,
      addform: {
        isPublish: '0',
        name: '',
        instructions: '',
        organize: '',
        time: [],
        publishDate: ''
      },
      organizeList: [],
      addRule: {
        name: [{ required: true, message: '请输入主题', trigger: 'blur' }],
        instructions: [{ required: true, message: '请输入说明', trigger: 'blur' }],
        time: [{ required: true, message: '请选择时间', trigger: 'blur' }],
        isRelease: [{ required: true, message: '请选择是否发布', trigger: 'change' }],
        publishDate: [{ required: true, message: '请选择时间', trigger: 'change' }]
      },
      userData: [],
      userShow: false
    }
  },
  mounted () {
    if (this.editData) {
      this.$api.appManagement.voteInfo(this.editData.id).then(res => {
        console.log(res)
        if (res.errcode === 200) {
          const data = res.data
          this.addform.name = data.name
          this.addform.instructions = data.instructions
          this.addform.time = [data.startTime, data.endTime]
          this.addform.organize = data.organize
          this.addform.publishDate = data.publishDate
          this.isAll = data.isVisible !== 1
          if (!this.isAll) {
            this.userData = res.data.users.map(item => {
              return {
                userId: item.id,
                name: item.username,
                userName: item.username
              }
            })
          }
          this.addform.isPublish = data.isPublish
        }
      })
    }
    this.getTypeList()
  },
  methods: {

    // 激活选人
    addMan () {
      this.userShow = !this.userShow
    },
    // 选人回调
    userCallback (data, type) {
      if (type) {
        this.userData = data
      }
      this.userShow = !this.userShow
    },
    // 删除已选人员
    deleteMan (index) {
      this.userData.splice(index, 1)
    },
    // 提交
    onSubmit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const data = this.addform
          if (this.isAll) {
            data.isVisible = 0
          } else {
            data.isVisible = 1
            if (this.userData.length === 0) {
              return this.$message.warning('请选择参与人员')
            }
            data.joinUserId = this.userData.map(item => item.userId).join(',')
          }
          data.startTime = this.addform.time[0]
          data.endTime = this.addform.time[1]
          if (this.editData) {
            data.id = this.editData.id
          }
          data.module = 'questionnaire'
          this.$api.appManagement.voteAdd(data).then(res => {
            if (res.errcode === 200) {
              if (this.editData) {
                this.$message.success('修改问卷成功')
              } else {
                this.$message.success('新增问卷成功')
              }
              this.$emit('cancel', true)
            }
          })
        } else {
          return false
        }
      })
    },
    // 取消
    cancel (formName) {
      this.$refs[formName].resetFields()
      this.userData = []
      this.$emit('cancel')
    },
    // 获取所属分类
    getTypeList () {
      this.$api.systemSettings.treeList({ treeType: 1 }).then(res => {
        if (res.errcode === 200) {
          this.organizeList = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.vote-add {
  .vote-add-box {
    width: 600px;
    max-height: 500px;
    padding: 10px 20px;
    .option-item {
      display: flex;
      .source {
        width: 60px;
        margin: 0 10px;
      }
      .source-left-none {
        margin-left: 0px;
      }
      .text {
        width: calc(100% - 160px);
      }
      .close {
        display: none;
        width: 32px;
        height: 32px;
        font-size: 18px;
        color: red;
        text-align: center;
        line-height: 32px;
      }
    }
    .option-item:hover {
      .close {
        display: block;
      }
    }
    .join-man {
      width: 100%;
      border: 1px solid #ededed;
      border-radius: 4px;
      padding: 10px;
      overflow: hidden;
      max-height: 150px;
      .label {
        color: #94070a;
      }
      .tag {
        margin-left: 10px;
      }
    }
    .join-man:hover {
      overflow-y: overlay;
    }
    .join-man::-webkit-scrollbar {
      width: 6px;
    }
    .join-man::-webkit-scrollbar-track {
      border-radius: 6px;
    }
    .join-man::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
