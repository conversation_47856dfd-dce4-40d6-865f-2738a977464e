<template>
  <div class="behalfAuditDetails">
    <el-form :model="form"
             inline
             ref="form"
             :rules="rules"
             label-position="top"
             class="newForm">
      <el-form-item label="用户名"
                    class="form-input">
        <el-input placeholder="请输入用户名"
                  disabled
                  v-model="form.olduserName"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新用户名"
                    class="form-input">
        <el-input placeholder="请输入曾用名"
                  disabled
                  v-model="form.newuserName"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="职务"
                    class="form-input">
        <el-input placeholder="请输入职务"
                  disabled
                  v-model="form.oldposition"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新职务"
                    class="form-input">
        <el-input placeholder="请输入职务"
                  disabled
                  v-model="form.newposition"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="手机"
                    class="form-input">
        <el-input placeholder="请输入手机"
                  disabled
                  v-model="form.oldmobile"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新手机"
                    class="form-input">
        <el-input placeholder="请输入手机"
                  disabled
                  v-model="form.newmobile"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="办公电话"
                    class="form-input">
        <el-input placeholder="请输入办公电话"
                  disabled
                  v-model="form.oldofficePhone"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新办公电话"
                    class="form-input">
        <el-input placeholder="请输入办公电话"
                  disabled
                  v-model="form.newofficePhone"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="通讯地址"
                    class="form-input">
        <el-input placeholder="请输入通讯地址"
                  disabled
                  v-model="form.oldcallAddress"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新通讯地址"
                    class="form-input">
        <el-input placeholder="请输入通讯地址"
                  disabled
                  v-model="form.newcallAddress"
                  clearable>
        </el-input>
      </el-form-item>

      <!-- <el-form-item label="学历"
                    class="form-input">
        <el-input placeholder="请输入学历"
                  disabled
                  v-model="form.oldeducation"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新学历"
                    class="form-input">
        <el-input placeholder="请输入学历"
                  disabled
                  v-model="form.neweducation"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="地域"
                    class="form-input">
        <el-input placeholder="请输入地域"
                  disabled
                  v-model="form.oldterritory"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新地域"
                    class="form-input">
        <el-input placeholder="请输入地域"
                  disabled
                  v-model="form.newterritory"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="健康状态"
                    class="form-input">
        <el-input placeholder="请输入健康状态"
                  disabled
                  v-model="form.oldhealth"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新健康状态"
                    class="form-input">
        <el-input placeholder="请输入健康状态"
                  disabled
                  v-model="form.newhealth"
                  clearable>
        </el-input>
      </el-form-item> -->

      <el-form-item label="邮箱"
                    class="form-input">
        <el-input placeholder="请输入邮箱"
                  disabled
                  v-model="form.oldemail"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新邮箱"
                    class="form-input">
        <el-input placeholder="请输入邮箱"
                  disabled
                  v-model="form.newemail"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="社会荣誉"
                    class="form-input">
        <el-input placeholder="请输入社会荣誉"
                  disabled
                  v-model="form.oldhonorInfo"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新社会荣誉"
                    class="form-input">
        <el-input placeholder="请输入社会荣誉"
                  disabled
                  v-model="form.newhonorInfo"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="社会贡献"
                    class="form-input">
        <el-input placeholder="请输入社会贡献"
                  disabled
                  v-model="form.oldcontribute"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="新社会贡献"
                    class="form-input">
        <el-input placeholder="请输入社会贡献"
                  disabled
                  v-model="form.newcontribute"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="审核人"
                    v-if="hasAudit"
                    class="form-input">
        <el-input placeholder=""
                  :disabled="hasAudit"
                  v-model="form.auditUser"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="审核结果"
                    prop="auditResult"
                    class="form-title">
        <el-radio-group v-model="form.auditResult"
                        :disabled="hasAudit">
          <el-radio :label="1">审核通过</el-radio>
          <el-radio :label="0">审核不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核备注"
                    class="form-title">
        <el-input placeholder=""
                  :disabled="hasAudit"
                  v-model="form.remarks"
                  clearable>
        </el-input>
      </el-form-item>
      <div class="form-button"
           v-if="!hasAudit">
        <el-button type="primary"
                   @click="submitForm('form')">确定</el-button>
        <el-button @click="resetForm('form')">取消</el-button>
      </div>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'behalfAuditDetails',
  data () {
    return {
      form: {
        olduserName: '',
        newuserName: '',
        oldposition: '',
        newposition: '',
        oldmobile: '',
        newmobile: '',
        oldofficePhone: '',
        newofficePhone: '',
        oldcallAddress: '',
        newcallAddress: '',
        oldeducation: '',
        neweducation: '',
        oldterritory: '',
        newterritory: '',
        oldhealth: '',
        newhealth: '',
        oldemail: '',
        newemail: '',
        oldhonorInfo: '',
        newhonorInfo: '',
        oldcontribute: '',
        newcontribute: '',
        auditResult: '',
        remarks: ''
      },
      rules: {
        auditResult: [
          { required: true, message: '请选择审核结果', trigger: 'blur' }
        ]
      }
    }
  },
  props: ['id', 'hasAudit'],
  mounted () {
    if (this.id) {
      this.userauditInfo()
    }
  },
  methods: {
    async userauditInfo () {
      const res = await this.$api.memberInformation.userauditInfo({ systemType: '2', id: this.id })
      var { data: { auditUser, auditResult, remarks, olduserName, newuserName, oldposition, newposition, oldmobile, newmobile, oldofficePhone, newofficePhone, oldcallAddress, newcallAddress, oldeducation, neweducation, oldterritory, newterritory, oldhealth, newhealth, oldemail, newemail, oldhonorInfo, newhonorInfo, oldcontribute, newcontribute } } = res
      this.form.olduserName = olduserName
      this.form.newuserName = newuserName
      this.form.oldposition = oldposition
      this.form.newposition = newposition
      this.form.oldmobile = oldmobile
      this.form.newmobile = newmobile
      this.form.oldofficePhone = oldofficePhone
      this.form.newofficePhone = newofficePhone
      this.form.oldcallAddress = oldcallAddress
      this.form.newcallAddress = newcallAddress
      this.form.oldeducation = oldeducation
      this.form.neweducation = neweducation
      this.form.oldterritory = oldterritory
      this.form.newterritory = newterritory
      this.form.oldhealth = oldhealth
      this.form.newhealth = newhealth
      this.form.oldemail = oldemail
      this.form.newemail = newemail
      this.form.oldhonorInfo = oldhonorInfo
      this.form.newhonorInfo = newhonorInfo
      this.form.oldcontribute = oldcontribute
      this.form.newcontribute = newcontribute
      this.form.auditUser = auditUser
      this.form.auditResult = auditResult
      this.form.remarks = remarks
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/useraudit/audit?'
          this.$api.general.generalAdd(url, {
            id: this.id,
            auditResult: this.form.auditResult,
            remarks: this.form.remarks
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    resetForm (formName) {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.behalfAuditDetails {
  width: 682px;
}
</style>
