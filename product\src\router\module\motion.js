/**
 * 议案者
 */
// 提交议案
const motionNew = () => import('@/views/mainModule/motion/motionBehalf/motionNew')
// 我领衔的议案
const motionLedBy = () => import('@/views/mainModule/motion/motionBehalf/motionLedBy')
// 我附议的议案
const motionJoint = () => import('@/views/mainModule/motion/motionBehalf/motionJoint')
// 草稿箱
const motionDraftBox = () => import('@/views/mainModule/motion/motionBehalf/motionDraftBox')
/**
 * 议案管理
 */
// 所有议案
const motionAll = () => import('@/views/mainModule/motion/motionManagement/motionAll/motionAll')
// 待审查
const motionReview = () => import('@/views/mainModule/motion/motionManagement/motionReview/motionReview')
// 人大交办中
const motionNpcAssigned = () => import('@/views/mainModule/motion/motionManagement/motionAssigned/motionNpcAssigned')
// 办理中
const DealtWithInMotion = () => import('@/views/mainModule/motion/motionManagement/DealtWithInMotion/DealtWithInMotion')
// 已答复
const haveReplyMotion = () => import('@/views/mainModule/motion/motionManagement/haveReplyMotion/haveReplyMotion')
// 已办结
const haveTransferredMotion = () => import('@/views/mainModule/motion/motionManagement/haveTransferredMotion/haveTransferredMotion')
// 办理单位申请调整
const unitApplyForAdjustMotion = () => import('@/views/mainModule/motion/motionManagement/unitApplyForAdjustMotion/unitApplyForAdjustMotion')
// 办理单位申请延期
const applyForDelayMotion = () => import('@/views/mainModule/motion/motionManagement/applyForDelayMotion/applyForDelayMotion')
// 统计分析
const motionStatistical = () => import('@/views/mainModule/motion/motionManagement/motionStatistical/motionStatistical')
// 统计分析详情列表
const motionStatisticalList = () => import('@/views/mainModule/motion/motionManagement/motionStatistical/motionStatisticalList')
/**
 * 办理单位
 */
// 单位办理中
const UnitDealtWithInMotion = () => import('@/views/mainModule/motion/undertakeUnit/UnitDealtWithInMotion/UnitDealtWithInMotion')
// 单位已答复
const UnitHaveReplyMotion = () => import('@/views/mainModule/motion/undertakeUnit/UnitHaveReplyMotion/UnitHaveReplyMotion')
// 单位已办结
const UnitHaveTransferredMotion = () => import('@/views/mainModule/motion/undertakeUnit/UnitHaveTransferredMotion/UnitHaveTransferredMotion')
/**
 * 配置管理
 */
// 提案分类
const motionType = () => import('@/views/mainModule/motion/motionType/motionType')

// 提案详情
const motionDetails = () => import('@/views/mainModule/motion/components/motionDetails')

const motion = [
  { // 提交议案
    path: '/motionNew',
    name: 'motionNew',
    component: motionNew
  },
  { // 我领衔的议案
    path: '/motionLedBy',
    name: 'motionLedBy',
    component: motionLedBy
  },
  { // 我附议的议案
    path: '/motionJoint',
    name: 'motionJoint',
    component: motionJoint
  },
  { // 草稿箱
    path: '/motionDraftBox',
    name: 'motionDraftBox',
    component: motionDraftBox
  },
  { // 所有议案
    path: '/motionAll',
    name: 'motionAll',
    component: motionAll
  },
  { // 待审查
    path: '/motionReview',
    name: 'motionReview',
    component: motionReview
  },
  { // 人大交办中
    path: '/motionNpcAssigned',
    name: 'motionNpcAssigned',
    component: motionNpcAssigned
  },
  { // 办理中
    path: '/DealtWithInMotion',
    name: 'DealtWithInMotion',
    component: DealtWithInMotion
  },
  { // 已答复
    path: '/haveReplyMotion',
    name: 'haveReplyMotion',
    component: haveReplyMotion
  },
  { // 已办结
    path: '/haveTransferredMotion',
    name: 'haveTransferredMotion',
    component: haveTransferredMotion
  },
  { // 办理单位申请调整
    path: '/unitApplyForAdjustMotion',
    name: 'unitApplyForAdjustMotion',
    component: unitApplyForAdjustMotion
  },
  { // 办理单位申请延期
    path: '/applyForDelayMotion',
    name: 'applyForDelayMotion',
    component: applyForDelayMotion
  },
  { // 统计分析
    path: '/motionStatistical',
    name: 'motionStatistical',
    component: motionStatistical
  },
  { // 统计分析详情列表
    path: '/motionStatisticalList',
    name: 'motionStatisticalList',
    component: motionStatisticalList
  },
  { // 单位办理中
    path: '/UnitDealtWithInMotion',
    name: 'UnitDealtWithInMotion',
    component: UnitDealtWithInMotion
  },
  { // 单位已答复
    path: '/UnitHaveReplyMotion',
    name: 'UnitHaveReplyMotion',
    component: UnitHaveReplyMotion
  },
  { // 单位已办结
    path: '/UnitHaveTransferredMotion',
    name: 'UnitHaveTransferredMotion',
    component: UnitHaveTransferredMotion
  },
  { // 议案详情
    path: '/motionType',
    name: 'motionType',
    component: motionType
  },
  { // 议案详情
    path: '/motionDetails',
    name: 'motionDetails',
    component: motionDetails
  }
]
export default motion
