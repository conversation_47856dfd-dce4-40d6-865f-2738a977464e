<template>
  <div class="candidates-user"
       v-loading="loading"
       element-loading-text="拼命加载中">
    <div class="candidates-user-box">
      <div class="candidates-user-content">
        <div class="search-box">
          <el-input placeholder="搜索人员名字"
                    v-model="name"
                    clearable
                    @keyup.enter.native="search">
            <div slot="prefix"
                 class="input-search"></div>
          </el-input>
        </div>
        <div class="user-box">
          <div class="user-tree-box">
            <div class="institutions-text">选择机构</div>
            <div class="user-tree">
              <!-- <zy-tree :tree="tree"
                       :choiceId.sync="choiceval"
                       @on-choice-click="choiceClick"></zy-tree> -->
              <zy-tree :tree="tree"
                       v-model="choiceval"
                       :props="{ children: 'children', label: 'name'}"
                       @on-tree-click="choiceClick"></zy-tree>
            </div>
          </div>
          <div class="user-personnel-box">
            <div class="personnel-checkbox">
              <div class="personnel-checkbox-text">人员列表</div>
              <el-checkbox :indeterminate="isIndeterminate"
                           v-model="checkAll"
                           v-if="max!=1"
                           @change="handleCheckAllChange"></el-checkbox>
            </div>
            <div class="user-content-box scrollBar">
              <el-checkbox-group v-model="checkedCities"
                                 @change="handleCheckedCitiesChange">
                <div class="user-content"
                     v-for="city in cities"
                     :key="city.userId">
                  <div class="user-content-icon-name">
                    <div class="user-content-icon"></div>
                    <div class="user-content-name el-checkbox__label ellipsis">{{city.userName}}</div>
                  </div>
                  <el-checkbox :value="city.userId"
                               :label="city.userId"
                               :disabled="maxUser(city.userId)"
                               @click="add(city)"></el-checkbox>
                </div>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>
      <div class="selected-user-box">
        <div class="selected-user-number">
          <div class="selected-user-number-text">
            <!-- {{point}} -->已选择({{storageData.length}}人)
          </div>
          <div class="selected-user-icon-delete"
               @click="deleteAll"></div>
        </div>
        <div class="selected-user scrollBar">
          <div class="selected-user-content"
               v-for="(item, index) in storageData"
               :key="index">
            <div class="selected-user-icon">
              <div class="selected-user-icon-name"></div>
            </div>
            <div class="selected-user-information">
              <div class="selected-user-name">{{item.userName}}</div>
              <div class="selected-user-text ellipsis">{{item.position}}</div>
            </div>
            <div class="selected-user-delete">
              <div class="selected-user-icon-delete"
                   @click="deleteclick(item)"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="candidates-user-button">
      <el-button type="primary"
                 @click="submitForm">确定</el-button>
      <el-button @click="resetForm">取消</el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'candidatesUser',
  data () {
    return {
      choiceval: '',
      tree: [],
      name: '',
      checkAll: false,
      checkedCities: [],
      cities: [],
      isIndeterminate: true,
      storage: {},
      storageData: [],
      selectObj: [],
      loading: false
    }
  },
  props: {
    point: {
      type: String,
      default: ''
    },
    max: {
      type: Number,
      default: 10000
    },
    data: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Array,
      default: () => []
    }
  },
  created () {
    if (this.data.length) {
      this.default()
    }
    this.pointrees()
  },
  methods: {
    maxUser (id) {
      var show = false
      if (this.storageData.length >= this.max) {
        show = true
      }
      this.storageData.forEach(item => {
        if (item.userId === id) {
          show = false
        }
      })
      this.storageData.forEach(item => {
        if (item.disabled && item.userId === id) {
          show = true
        }
      })
      // this.disabled.forEach(item => {
      //   if (item.userId === id) {
      //     show = true
      //   }
      // })
      return show
    },
    search () {
      this.loading = true
      this.roleChooseusers()
    },
    default () {
      var arr = []
      this.data.forEach(item => {
        arr.push(item.userId)
      })
      this.poinexistsids(arr.join(','))
    },
    async poinexistsids (ids) {
      const res = await this.$api.general.poinexistsids(`${this.point}?ids=${ids}`)
      var { data } = res
      this.storageData = data
      data.forEach(item => {
        this.selectObj[item.userId] = item.userId
      })
    },
    async pointrees () {
      const res = await this.$api.general.pointrees(this.point)
      var { data } = res
      this.tree = data
    },
    choiceClick (item) {
      this.loading = true
      this.roleChooseusers()
    },
    async roleChooseusers () {
      var datas = {
        pointCode: this.point,
        treeId: this.choiceval,
        keyword: this.name
      }
      const res = await this.$api.general.pointreeUsers(datas)
      var { data } = res
      this.disabled.forEach(item => {
        data = data.filter(tab => tab.userId !== item.userId)
      })
      this.cities = data
      this.loading = false
      this.memoryChecked()
    },
    handleCheckAllChange (val) {
      if (this.storageData.length >= this.max) {
        return
      }
      var arr = []
      this.cities.forEach(item => {
        arr.push(item.userId)
        if (Object.prototype.hasOwnProperty.call(this.selectObj, item.userId)) {
          if (!val) {
            this.deleteData(item)
          }
          val ? null : delete this.selectObj[item.userId] // eslint-disable-line
        } else {
          this.selectObj[item.userId] = item.userId
          this.pushData(item.userId)
        }
      })
      this.checkedCities = val ? arr : []
      this.storage[this.choiceval] = val ? arr : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange (value) {
      this.storage[this.choiceval] = value
      const checkedCount = value.length
      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length
      var values = []
      value.forEach(item => {
        values[item] = item
      })
      this.cities.forEach((item) => {
        if (Object.prototype.hasOwnProperty.call(values, item.userId)) {
        } else {
          delete this.selectObj[item.userId]
          this.deleteData(item)
        }
      })
      value.forEach(item => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, item)) {
        } else {
          this.selectObj[item] = item
          this.pushData(item)
        }
      })
    },
    deleteAll () {
      this.$confirm('此操作将删除当前已选的所有用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var arr = []
        this.storageData.forEach(item => {
          if (item.disabled) {
            arr.push(item)
          }
        })
        this.storageData = arr
        this.selectObj = []
        arr.forEach(item => {
          this.selectObj[item.userId] = item.userId
        })
        if (arr.length) {
          this.$message({
            type: 'info',
            message: '当前选中用户有部分不能移除'
          })
        }
        this.memoryChecked()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    deleteclick (data) {
      if (data.disabled) {
        this.$message({
          type: 'info',
          message: '当前选中用户不能移除'
        })
        return
      }
      this.deleteData(data)
      delete this.selectObj[data.userId]
      this.memoryChecked()
    },
    deleteData (data) {
      const arr = this.storageData
      arr.forEach((item, index) => {
        if (item.userId === data.userId) {
          arr.splice(index, 1)
        }
      })
      this.storageData = arr
    },
    pushData (id) {
      this.cities.forEach((item, index) => {
        if (item.userId === id) {
          this.storageData.push(item)
        }
      })
    },
    memoryChecked () {
      var add = []
      this.cities.forEach((row, index) => {
        if (Object.prototype.hasOwnProperty.call(this.selectObj, row.userId)) {
          add.push(row.userId)
        }
      })
      this.checkedCities = add
      const checkedCount = add.length
      this.checkAll = checkedCount > this.cities.length || checkedCount === this.cities.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length
    },
    submitForm () {
      this.$emit('userCallback', this.storageData, true)
    },
    resetForm () {
      this.$emit('userCallback', this.data, false)
    }
  }
}
</script>
<style lang="scss">
@import "./candidates-user.scss";
</style>
