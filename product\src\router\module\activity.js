/**
 * 活动补录
 */
// 活动管理
const activityAll = () => import('@/views/activity/activityAll/activityAll')
// 活动数据统计
const activityAllStatistics = () => import('@/views/activity/activityAll/activityAllStatistics')
// 活动新增
const activityNew = () => import('@/views/activity/activityNew/activityNew')
// 活动管理补录
const activityAllRecording = () => import('@/views/activity/activityAll/activityAllRecording')
// 活动补录新增
const activityNewRecording = () => import('@/views/activity/activityNew/activityNewRecording')
// 我的活动
const myActivity = () => import('@/views/activity/myActivity/myActivity')
// 活动资料
const activityData = () => import('@/views/activity/activityAll/activityData/activityData')
// 活动行程
const activityTrip = () => import('@/views/activity/activityAll/activityTrip/activityTrip')
// 活动请假
const activityAskLeave = () => import('@/views/activity/activityAll/activityAskLeave/activityAskLeave')
// 活动报告
const activityReport = () => import('@/views/activity/activityAll/activityReport/activityReport')
// 新增补录
const NewActivitiesThrough = () => import('@/views/activity/ActivitiesThrough/NewActivitiesThrough')
// 活动补录
const ActivitiesThrough = () => import('@/views/activity/ActivitiesThrough/ActivitiesThrough')
const activity = [
  {
    path: '/activityAll',
    name: 'activityAll',
    component: activityAll
  },
  {
    path: '/activityAllStatistics',
    name: 'activityAllStatistics',
    component: activityAllStatistics
  },
  {
    path: '/activityAllRecording',
    name: 'activityAllRecording',
    component: activityAllRecording
  },
  {
    path: '/activityData',
    name: 'activityData',
    component: activityData
  },
  {
    path: '/activityTrip',
    name: 'activityTrip',
    component: activityTrip
  },
  {
    path: '/activityAskLeave',
    name: 'activityAskLeave',
    component: activityAskLeave
  },
  {
    path: '/activityReport',
    name: 'activityReport',
    component: activityReport
  },
  {
    path: '/activityNew',
    name: 'activityNew',
    component: activityNew
  },
  {
    path: '/activityNewRecording',
    name: 'activityNewRecording',
    component: activityNewRecording
  },
  {
    path: '/myActivity',
    name: 'myActivity',
    component: myActivity
  },
  {
    path: '/NewActivitiesThrough',
    name: 'NewActivitiesThrough',
    component: NewActivitiesThrough
  },
  {
    path: '/ActivitiesThrough',
    name: 'ActivitiesThrough',
    component: ActivitiesThrough
  }

]

export default activity
