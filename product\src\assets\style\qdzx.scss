.qd-search-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 68px;
  padding: 0 15px;
  background-color: #fff;
  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);
  border-radius: 10px;
  margin-top: 20px;

  >.el-input {
    width: 220px;
    margin-right: 12px;
  }
}

.qd-list-wrap {
  height: calc(100% - 88px);
  margin-top: 20px;
  background-color: #fff;
  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);
  border-radius: 10px;
  padding: 0 15px;

  .qd-list-tab {
    height: 50px;
    box-sizing: border-box;
    border-bottom: 1px solid #ebebeb;
    display: flex;

    .tab-item {
      height: 50px;
      line-height: 50px;
      padding: 0 12px;
      margin: 0 10px;
      box-sizing: border-box;
      cursor: pointer;
    }

    .tab-item.active {
      border-bottom: 2px solid #007bff;
      color: #007bff;
    }
  }

  .qd-btn-box {
    height: 68px;
    display: flex;
    align-items: center;

    .el-upload {
      margin: 0 10px;
    }
  }

  .qd-table-box {
    height: calc(100% - 136px);

    .el-icon-check {
      color: #058bd1;
    }

    .el-icon-close {
      color: #f56c6c;
    }
  }

  .qd-page-box {
    width: 100%;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.form-footer-btn {
  height: 56px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.add-form-title {
  line-height: 36px;
  margin-bottom: 20px;
}

.qd-form {

  .form-item-wd100 {
    width: 100%;
    display: flex;

    .el-form-item__label {
      flex-shrink: 0;
    }

    .el-form-item__content {
      flex: auto;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
    }
  }

  .form-upload {
    .el-upload__text {
      margin-top: 45px;
    }
  }

  .form-item-wd50 {
    width: calc(50% - 10px);
  }

  .form-column-wd50 {
    width: calc(50% - 10px);
    margin-right: 10px;
    float: left;
  }

  .join-man {
    width: 100%;
    border: 1px solid #ededed;
    border-radius: 4px;
    padding: 10px;
    overflow: hidden;
    max-height: 150px;

    .tag {
      margin-left: 10px;
    }
  }

  .join-man:hover {
    overflow-y: scroll;
    overflow-y: overlay;
  }

  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.1);
  }

  .base-form-item {
    display: flex;

    .base-form-item-label {
      width: 150px;
      text-align: left;
      flex-shrink: 0;
      font-size: 18px;
      color: #333;
    }

    .base-form-item-content {
      width: calc(100% - 150px);
    }
  }

  .border-top {
    border-top: 1px solid #ebebeb;
    padding-top: 24px;
  }

  .user-list {
    align-items: center;
    width: 100%;
    border: 1px solid #ededed;
    border-radius: 4px;
    margin: 10px 0;
    overflow: hidden;
    position: relative;
    box-sizing: border-box;

    .tag {
      margin-left: 10px;
    }

    .ellipsis {
      height: 54px;
      width: 36px;
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;

      >span {
        width: 4px;
        height: 4px;
        background-color: #007bff;
        border-radius: 50%;
        margin-right: 4px;
      }
    }
  }
}