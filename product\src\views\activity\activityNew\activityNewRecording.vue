<template>
  <div class="activityNew">
    <el-steps :active="active" finish-status="success" simple>
      <el-step title="会议/活动基本信息"></el-step>
      <el-step title="时间、地点和人员"></el-step>
      <el-step title="各项设置"></el-step>
    </el-steps>
    <el-scrollbar class="activityNewScrollbar">
      <el-form :model="form" v-show="active == 0" :rules="rules0" inline ref="form0" label-position="top"
        class="newForm">
        <el-form-item label="活动大类" prop="meetType" class="form-input">
          <el-select v-model="form.meetType" filterable clearable placeholder="请选择活动大类" @change="activeTypeChange">
            <el-option v-for="item in meetType" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="活动小类"
                      prop="typeSmall"
                      class="form-input">
          <el-select v-model="form.typeSmall"
                     filterable
                     clearable
                     placeholder="请选择活动小类">
            <el-option v-for="item in typeSmall"
                       :key="item.id"
                       :label="item.name"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item class="form-title" prop="meetName">
          <span slot="label" class="label-button">活动主题 <span style="color: red;">(最多50字)</span>
          </span>
          <el-input placeholder="请输入活动主题" v-model="form.meetName" clearable prop="meetName">
          </el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content" class="form-ue">
          <UEditor v-model="form.content"></UEditor>
        </el-form-item>
        <el-form-item label="组织部门" prop="organizer" class="form-item-wd100">
          <el-autocomplete class="inline-input" v-model="form.organizer" :fetch-suggestions="querySearch"
            placeholder="请输入内容"></el-autocomplete>
        </el-form-item>
        <el-form-item label="群众人数" class="form-item-wd100">
          <el-input-number v-model="form.registration" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="活动主体" class="form-title">
          <!-- <el-autocomplete class="inline-input" v-model="form.pubOrganizer" :fetch-suggestions="organizerSearch"
            placeholder="请输入内容"></el-autocomplete> -->
          <el-checkbox-group v-model="form.pubOrganizer">
            <el-checkbox v-for="item in orgArrOrganizer" :key="item.value" :label="item.value"
              :disabled="isOptionDisabled(item.value)">{{ item.value }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <div class="form-button">
          <el-button type="primary" @click="oneClick('form0')">填好了,下一步</el-button>
          <el-button @click="oneReset">重置以上内容</el-button>
          <el-button type="primary" v-if="id" @click="SubmitEdit()">填好了,提交</el-button>
        </div>
      </el-form>
      <el-form :model="form" v-show="active == 1" :rules="rules1" inline ref="form1" label-position="top"
        class="newForm qd-form">
        <el-form-item class="form-title" label="活动地点" prop="address">
          <el-input placeholder="请输入活动地点" v-model="form.address" clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="报名时间" class="form-title" prop="signUpTime">
          <el-date-picker v-model="form.signUpTime" type="datetimerange" value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至" start-placeholder="报名开始日期" end-placeholder="报名结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="签到时间" class="form-title" prop="signTime">
          <el-date-picker v-model="form.signTime" type="datetimerange" @change="change" :picker-options="pickerOptions"
            value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="签到开始日期" end-placeholder="签到结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="活动时间" class="form-title" prop="activityTime">
          <el-date-picker v-model="form.activityTime" type="datetimerange" :picker-options="pickerOptions"
            value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" start-placeholder="活动开始日期" end-placeholder="活动结束日期">
          </el-date-picker>
        </el-form-item>

        <!-- <el-form-item class="form-title">
          <span slot="label">邀请参与活动人员 <span class="red">(若不选择人员,则表示该活动为公开活动,所有人都可以参与)</span></span>
          <div class="form-user-box"
               @click="userClick('1')">
            <div v-if="!inviters.length"
                 class="form-user-box-text">请选择参与活动人员</div>
            <el-tag v-for="tag in inviters"
                    :key="tag.userId"
                    size="medium"
                    closable
                    :disable-transitions="false"
                    @close.stop="remove(tag,'1')">
              {{tag.name}}
            </el-tag>
          </div>
        </el-form-item> -->

        <!-- <el-form-item class="form-item-user form-title ">
          <span slot="label">邀请参与活动人员 <span class="red">(若不选择人员,则表示该活动为公开活动,所有人都可以参与)</span></span>
          <el-button size="small"
                     plain
                     icon="el-icon-plus"
                     @click="userClick('1')">选择人员（{{ inviters.length }}）</el-button>
          <el-button @click="importFiles('1')"
                     type="primary"
                     size="small"
                     plain
                     icon="el-icon-download">导入参会人员</el-button>
          <el-button type="primary"
                     size="small"
                     plain
                     icon="el-icon-upload2"
                     @click="exportFile">下载导入模板</el-button>
          <div class="user-list"
               ref="userRef"
               v-if="inviters.length"
               :style="{ paddingRight: (isMore ? 60 : 0) + 'px' }">
            <el-tag class="tag"
                    v-for="tag in inviters"
                    :key="tag.userId"
                    :disable-transitions="false"
                    @close.stop="remove(tag,'1')"
                    closable>
              {{ tag.name }}
            </el-tag>
            <div class="ellipsis"
                 v-if="isMore">
              <span></span><span></span><span></span>
            </div>
          </div>

        </el-form-item> -->

        <!-- <el-form-item class="form-item-user form-title">
          <span slot="label">已报名人员 <span class="red">(此项为管理员后台处理数据所用)</span></span> -->
        <!-- <div class="form-user-box"
               @click="userClick('2')">
            <div v-if="!signUps.length"
                 class="form-user-box-text">请选择报名人</div>
            <el-tag v-for="tag in signUps"
                    :key="tag.userId"
                    size="medium"
                    closable
                    :disable-transitions="false"
                    @close.stop="remove(tag,'2')">
              {{tag.name}}
            </el-tag>
          </div> -->
        <!-- <el-button size="small"
                     plain
                     icon="el-icon-plus"
                     @click="userClick('2')">选择报名人（{{ signUps.length }}）</el-button>
          <el-button @click="importFiles('2')"
                     type="primary"
                     size="small"
                     plain
                     icon="el-icon-download">导入报名人员</el-button>
          <el-button type="primary"
                     size="small"
                     plain
                     icon="el-icon-upload2"
                     @click="exportFile">下载导入模板</el-button>
          <div class="user-list"
               ref="userRef"
               v-if="signUps.length"
               :style="{ paddingRight: (isMore ? 60 : 0) + 'px' }">
            <el-tag class="tag"
                    v-for="tag in signUps"
                    :key="tag.userId"
                    :disable-transitions="false"
                    @close.stop="remove(tag,'2')"
                    closable>
              {{ tag.name }}
            </el-tag>
          </div>

        </el-form-item> -->

        <el-form-item class="form-item-user form-title">
          <span slot="label">已签到人员 <span class="red">(此项为管理员后台处理数据所用)</span></span>
          <!-- <div class="form-user-box"
               @click="userClick('3')">
            <div v-if="!signIns.length"
                 class="form-user-box-text">请选择签到人</div>
            <el-tag v-for="tag in signIns"
                    :key="tag.userId"
                    size="medium"
                    closable
                    :disable-transitions="false"
                    @close.stop="remove(tag,'3')">
              {{tag.name}}
            </el-tag>
          </div> -->

          <el-button size="small" plain icon="el-icon-plus" @click="userClick('3')">选择签到人（{{ signIns.length
          }}）</el-button>
          <el-button @click="importFiles('3')" type="primary" size="small" plain
            icon="el-icon-download">导入签到人员</el-button>
          <el-button type="primary" size="small" plain icon="el-icon-upload2" @click="exportFile">下载导入模板</el-button>
          <div class="user-list" ref="userRef" v-if="signIns.length"
            :style="{ paddingRight: (isMore ? 60 : 0) + 'px' }">
            <el-tag class="tag" v-for="tag in signIns" :key="tag.userId" :disable-transitions="false"
              @close.stop="remove(tag, '3')" closable>
              {{ tag.name }}
            </el-tag>
          </div>

        </el-form-item>
        <div class="form-button">
          <el-button type="primary" @click="twoClick('form1')">填好了,下一步</el-button>
          <el-button @click="twoReturn">返回上一步</el-button>
          <el-button @click="twoReset">重置以上内容</el-button>
          <el-button type="primary" v-if="id" @click="SubmitEdit()">填好了,提交</el-button>
        </div>
      </el-form>
      <el-form :model="form" v-show="active == 2" :rules="rules" inline ref="form" label-position="top" class="newForm">
        <el-form-item label="是否在手机APP上显示该活动" v-if="$isAppShow()" prop="isAppShow" class="form-input">
          <el-radio-group v-model="form.isAppShow">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="是否让所有人可以看到该活动"
                        prop="isPublish"
                        class="form-input">
            <el-radio-group v-model="form.isPublish">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item> -->
        <!-- <el-form-item label="是否让所有人可以报名参加该活动"
                        prop="isPublishbm"
                        class="form-input">
            <el-radio-group v-model="form.isPublishbm">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item> -->
        <el-form-item label="是否需要用短信通知活动邀请人" prop="isNotice" class="form-input">
          <el-radio-group v-model="form.isNotice">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否同步生成活动通知" prop="isRelease" class="form-input">
          <el-radio-group v-model="form.isRelease">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <br>
        <!-- <el-form-item label="请您为本次活动选几个标签吧">
          <el-checkbox-group v-model="form.label">
            <el-checkbox v-for="item in activity_label"
                         :key="item.value"
                         :label="item.id">{{item.value}}</el-checkbox>
          </el-checkbox-group>
        </el-form-item> -->
        <div class="form-button">
          <el-button type="primary" @click="submitForm('form')">填好了,提交</el-button>
          <el-button @click="threeReturn">返回上一步</el-button>
          <el-button @click="threeReset">重置以上内容</el-button>
        </div>
      </el-form>
    </el-scrollbar>
    <xyl-popup-window v-model="userShow" title="人员选择">
      <candidates-user point="point_15" :data="userData" @userCallback="userCallback"></candidates-user>
    </xyl-popup-window>
    <zy-pop-up v-model="showAdd" title="导入">
      <importFile @cloesWin="cloesWin"></importFile>
    </zy-pop-up>
  </div>
</template>
<script>
import importFile from '../../../views/newMeeting/newMeetingAdd/add/widget/widget/import.vue'
export default {
  name: 'activityNew',
  data () {
    return {
      id: this.$route.query.id,
      toId: this.$route.query.toId,
      active: 0,
      form: {
        meetName: '', // 标题
        meetType: '', // 活动父类型
        typeSmall: '', // 活动子类型
        meetingotherTypename: '', // 报送单位
        organizer: '', // 组织部门
        pubOrganizer: [], // 发布部门
        num: '', // 排序
        address: '', // 地点
        signUpTime: null, // 报名时间
        signTime: null, // 签到时间
        activityTime: null, // 活动时间
        isAppShow: 0, // 是否app显示
        isPublish: 1, // 是否公开
        isPublishbm: 1, // 是否公开报名
        isNotice: 0, // 是否短信通知邀请人
        isRelease: 0, // 是否发布通知
        label: [], // 标签
        content: '', // 内容,
        registration: 0
      },
      showAdd: false,
      isMore: false,
      importType: '',
      rules: {},
      rules0: {
        meetName: [
          { required: true, message: '请输入活动主题', trigger: 'blur' }
        ],
        meetType: [
          { required: true, message: '请选择活动大类', trigger: 'blur' }
        ],
        typeSmall: [
          { required: false, message: '请选择活动小类', trigger: ['blur', 'change'] }
        ],
        organizer: [
          { required: true, message: '请选择组织部门', trigger: ['blur', 'change'] }
        ],
        content: [
          { required: true, message: '请输入活动内容', trigger: 'blur' }
        ]
      },
      rules1: {
        address: [
          { required: true, message: '请输入活动地点', trigger: 'blur' }
        ],
        activityTime: [
          { required: true, message: '请选择活动时间', trigger: 'blur' }
        ],
        signUpTime: [
          { required: true, message: '请选择报名时间', trigger: 'blur' }
        ],
        signTime: [
          { required: true, message: '请选择签到时间', trigger: 'blur' }
        ]
      },
      orgArrOrganizer: [],
      currentMeetTypeName: '',
      meetType: [],
      typeSmall: [],
      activity_label: [],
      type: '',
      userShow: false,
      userData: [],
      inviters: [], // 邀请人
      signUps: [], // 报名人
      signIns: [], // 签到人
      pickerOptions: {
        disabledDate: (time) => {
          const nowDate = this.form.signUpTime ? new Date(this.form.signUpTime[1]) : new Date()
          const oneDay = 1000 * 60 * 60 * 24
          const oneYearLater = new Date(nowDate.getTime() + (oneDay * 365))
          return time.getTime() < nowDate || time.getTime() > oneYearLater
        }
      }
    }
  },
  components: {
    importFile
  },
  inject: ['tabDel', 'refresh'],
  watch: {
    'form.meetType' (val) {
      if (val) {
        this.meetType.forEach(item => {
          if (item.id === val) {
            this.form.typeSmall = ''
            this.typeSmall = item.children
            if (this.typeSmall.length) {
              this.rules0.typeSmall = [{ required: true, message: '请选择活动小类', trigger: ['blur', 'change'] }]
            } else {
              this.rules0.typeSmall = [{ required: false, message: '请选择活动小类', trigger: ['blur', 'change'] }]
              this.$refs.form0.clearValidate('typeSmall')
            }
          }
        })
      } else {
        this.form.typeSmall = ''
        this.typeSmall = []
        this.rules0.typeSmall = [{ required: false, message: '请选择活动小类', trigger: ['blur', 'change'] }]
        this.$refs.form0.clearValidate('typeSmall')
      }
    }
  },
  mounted () {
    this.treelist(3)
    // this.treelist(5)
    this.dictionaryPubkvs()
    if (this.id) {
      this.activityInfo()
    }
    this.orgArr = this.loadAll()
    // this.orgArrOrganizer = this.loadAllOrganizer()
  },
  methods: {
    activeTypeChange (selectedId) {
      const selectedItem = this.meetType.find(item => item.id === selectedId)
      this.currentMeetTypeName = selectedItem?.name || ''
    },
    isOptionDisabled (optionValue) {
      // 未选择活动大类时，所有选项均可选
      if (!this.currentMeetTypeName) return false
      // 完全匹配时才禁用
      return optionValue === this.currentMeetTypeName
    },
    async exportFile () {
      await this.$api.meeting.newMeeting.getConferenceUserTemplate()
    },
    cloesWin (data) {
      this.showAdd = false
      if (data.length) {
        if (this.importType === '1') {
          this.inviters = data
        }
        if (this.importType === '2') {
          this.signUps = data
        }
        if (this.importType === '3') {
          this.signIns = data
        }
      }
    },
    importFiles (importType) {
      this.importType = importType
      this.showAdd = true
    },
    collapse () {
      var userRef = this.$refs.userRef
      if (userRef) {
        var width = 0
        for (let index = 0; index < userRef.childNodes.length; index++) {
          if (userRef.childNodes[index].offsetWidth !== undefined) {
            width += userRef.childNodes[index].offsetWidth + 10
          }
        }
        if (userRef.offsetWidth < width) {
          this.isMore = true
        } else {
          this.isMore = false
        }
      }
    },
    change (val) {
      if (val) {
        var sdtime = new Date(val[1])
        var time = sdtime.setHours(sdtime.getHours() + 3)
        this.form.activityTime = [val[1], this.$format(time)]
      } else {
        this.form.activityTime = null
      }
    },
    /**
     *详情
    */
    async activityInfo () {
      const res = await this.$api.activity.activityInfo(this.id)
      var { data } = res
      this.form.meetName = data.meetName // 标题
      this.form.organizer = data.organizer // 组织部门
      this.form.pubOrganizer = data.pubOrganizer ? data.pubOrganizer.split(',') : [] // 发布部门
      this.form.num = data.num // 排序
      this.form.address = data.address // 地点
      this.form.meetingotherTypename = data.meetingotherTypename // 报送单位
      this.form.meetType = data.meetType // 活动父类型
      setTimeout(() => {
        this.form.typeSmall = data.typeSmall // 活动子类型
      }, 520)
      this.form.isAppShow = data.isAppShow // 是否app显示
      this.form.isPublish = data.isPublish // 是否公开
      this.form.isPublishbm = data.isPublishbm // 是否公开报名
      this.form.isNotice = data.isNotice // 是否短信通知邀请人
      this.form.isRelease = data.isRelease // 是否发布通知
      this.form.label = data.label ? data.label.split(',') : []
      this.form.content = data.content
      this.inviters = data.inviterList // 邀请人
      this.signUps = data.signUpList // 报名人
      this.signIns = data.signInList // 签到人
      this.form.signUpTime = [data.signBeginTime, data.signEndTime] // 报名时间
      this.form.signTime = [data.meetSignBeginTime, data.meetSignEndTime] // 签到时间
      this.form.activityTime = [data.meetStartTime, data.meetEndTime] // 活动时间
      this.form.registration = data.registration
    },
    async treelist (treeType) {
      // (5,委员通讯录树),
      // (3,”活动类型”),
      const res = await this.$api.activity.treelist({
        treeType: treeType
      })
      var { data } = res
      if (treeType === 3) {
        this.meetType = data
      } if (treeType === 5) {
        // this.meetingotherTypenameList = data
      }
    },
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'activity_label,activity_subject'
      })
      var { data } = res
      this.activity_label = data.activity_label
      this.orgArrOrganizer = data.activity_subject
    },
    userClick (type) {
      this.type = type
      if (type === '1') {
        this.userData = this.inviters
      } else if (type === '2') {
        this.userData = this.signUps
      } else if (type === '3') {
        this.userData = this.signIns
      }
      this.userShow = !this.userShow
    },
    userCallback (data, isOr) {
      if (isOr) {
        if (this.type === '1') {
          this.inviters = data
        } else if (this.type === '2') {
          this.signUps = data
        } else if (this.type === '3') {
          this.signIns = data
        }
      }
      this.userShow = !this.userShow
      this.$nextTick(() => {
        this.collapse()
      })
    },
    // 移除tag
    remove (data, type) {
      var userData = []
      if (type === '1') {
        userData = this.inviters
        this.inviters = userData.filter(v => v.userId !== data.userId)
      } else if (type === '2') {
        userData = this.signUps
        this.signUps = userData.filter(v => v.userId !== data.userId)
      } else if (type === '3') {
        userData = this.signIns
        this.signIns = userData.filter(v => v.userId !== data.userId)
      }
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var inviters = []
          this.inviters.forEach(item => {
            inviters.push(item.userId)
          })
          var signUps = []
          this.signUps.forEach(item => {
            signUps.push(item.userId)
          })
          var signIns = []
          this.signIns.forEach(item => {
            signIns.push(item.userId)
          })
          var data = this.form
          this.empty = '1' // 置空过滤标识
          data.pubOrganizer = this.form.pubOrganizer.join(',') // 活动主体
          data.signBeginTime = this.form.signUpTime[0] // 报名开始时间
          data.signEndTime = this.form.signUpTime[1] // 报名截止时间
          data.meetSignBeginTime = this.form.signTime[0] // 开始签到时间
          data.meetSignEndTime = this.form.signTime[1] // 签到截至时间
          data.meetStartTime = this.form.activityTime[0] // 活动开始时间
          data.meetEndTime = this.form.activityTime[1] // 活动结束时间
          // data.label = this.form.label.join(',') // 标签
          data.inviters = inviters.join(',') // 邀请人ids 以逗号隔开
          data.signUps = signUps.join(',') // 报名人ids 以逗号隔开
          data.signIns = signIns.join(',') // 签到人ids 以逗号隔开
          if (this.form.label) {
            data.label = this.form.label.join(',') // 标签
          }
          if (this.id) {
            data.id = this.id
          }
          data.isPublishbm = inviters.length ? 0 : 1
          data.isCollection = 1
          data.registration = this.form.registration
          this.activitySaveActivity(data)
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    async activitySaveActivity (data) {
      const res = await this.$api.activity.activitySaveActivity(data)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        if (this.id) {
          this.tabDel(this.id, this.toId)
        } else {
          this.active = 0
          this.form.address = ''
          this.form.signUpTime = null
          this.form.signTime = null
          this.form.activityTime = null
          this.oneReset()
          this.refresh()
        }
      }
    },
    oneClick (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.active = 1
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    oneReset () {
      this.form.meetName = '' // 标题
      this.form.meetType = '' // 活动父类型
      this.form.typeSmall = '' // 活动子类型
      this.form.content = '' // 内容
      this.form.organizer = '' // 组织部门
      this.form.pubOrganizer = [] // 发布部门
    },
    twoClick (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.active = 2
        } else {
          console.log(this.form)
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    twoReturn () {
      this.active = 0
    },
    twoReset () {
      this.form.address = '' // 地点
      this.form.signUpTime = [] // 报名时间
      this.form.signTime = [] // 签到时间
      this.form.activityTime = [] // 活动时间
      this.inviters = [] // 邀请人
      this.signUps = [] // 报名人
      this.signIns = [] // 签到人
    },
    threeReturn () {
      this.active = 1
    },
    threeReset () {
      this.form.isAppShow = 1 // 是否app显示
      this.form.isPublish = 1 // 是否公开
      this.form.isPublishbm = 1 // 是否公开报名
      this.form.isNotice = 0 // 是否短信通知邀请人
      this.form.isRelease = 0 // 是否发布通知
      this.form.label = [] // 标签
    },
    SubmitEdit () {
      var index = null
      this.$refs.form0.validate((valid) => {
        if (valid) {
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          index = 0
          return false
        }
      })
      if (index !== null) {
        this.active = index
        return
      }
      this.$refs.form1.validate((valid) => {
        if (valid) {
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          index = 1
          return false
        }
      })
      if (index !== null) {
        this.active = index
        return
      }
      if (index === null) {
        this.submitForm('form')
      }
    },
    /**
     * 取消按钮
    */
    cancel () {
      this.$emit('callback')
    },
    querySearch (queryString, cb) {
      var orgArr = this.orgArr
      var results = queryString ? orgArr.filter(this.createFilter(queryString)) : orgArr
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    loadAll () {
      return [
        { id: '001', value: '办公室' },
        { id: '002', value: '研究室' },
        { id: '003', value: '专委会一室' },
        { id: '004', value: '专委会二室' },
        { id: '005', value: '专委会三室' },
        { id: '006', value: '专委会四室' },
        { id: '007', value: '专委会五室' },
        { id: '008', value: '专委会六室' },
        { id: '009', value: '委室联合分党组' },
        { id: '010', value: '委员工作站' },
        { id: '011', value: '专委会' },
        { id: '012', value: '界别' }
      ]
    },
    organizerSearch (organizerSearch, cb) {
      var orgArrOrganizer = this.orgArrOrganizer
      var results = organizerSearch ? orgArrOrganizer.filter(this.organizerFilter(organizerSearch)) : orgArrOrganizer
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    organizerFilter (organizerSearch) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(organizerSearch.toLowerCase()) === 0)
      }
    },
    loadAllOrganizer () {
      return [
        { id: '001', value: '专委会活动' },
        { id: '002', value: '界别活动' },
        { id: '003', value: '委员工作站活动' }]
    }
  }
}
</script>
<style lang="scss">
.activityNew {
  width: 100%;
  height: 100%;

  .el-steps {
    width: 974px;
  }

  .activityNewScrollbar {
    height: calc(100% - 46px);
    width: 100%;

    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }

    .newForm {
      width: 988px;

      .form-button {
        justify-content: flex-start;

        .el-button {
          padding: 0 22px;
        }
      }

      .red {
        color: red;
      }
    }
  }

  .form-user-box {
    width: 100%;
    min-height: 40px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    padding: 0 16px;
    padding-right: 40px;
    padding-top: 6px;

    .form-user-box-text {
      color: #999;
      font-size: 14px;
      padding-bottom: 6px;
      line-height: 28px;
    }

    .el-tag {
      margin-bottom: 6px;
      margin-right: 12px;
    }

    &:hover {
      border-color: $zy-color;
    }

    &:focus {
      border-color: $zy-color;
    }
  }
}
</style>
