<template>
  <div class="AddActivitiesThrough">
    <el-form :model="form"
             :rules="rules"
             inline
             ref="form"
             label-position="top"
             class="newForm qd-form">
      <el-form-item label="标题"
                    prop="title"
                    class="form-title">
        <el-input placeholder="请输入标题"
                  v-model="form.title"
                  clearable>
        </el-input>
      </el-form-item>
      <el-form-item label="活动大类"
                    prop="bigType"
                    class="form-input">
        <el-select v-model="form.bigType"
                   filterable
                   clearable
                   placeholder="请选择活动大类">
          <el-option v-for="item in bigType"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="活动小类"
                    prop="smallType"
                    class="form-input">
        <el-select v-model="form.smallType"
                   filterable
                   clearable
                   placeholder="请选择活动小类">
          <el-option v-for="item in smallType"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id">
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="组织部门"
                    prop="org"
                    class="form-input">
        <el-autocomplete class="inline-input"
                         v-model="form.org"
                         :fetch-suggestions="querySearch"
                         placeholder="请输入内容"></el-autocomplete>
      </el-form-item>
      <el-form-item label="活动主体"
                    class="form-input">
        <el-autocomplete class="inline-input"
                         v-model="form.remarks "
                         :fetch-suggestions="organizerSearch"
                         placeholder="请输入内容"></el-autocomplete>
      </el-form-item>
      <el-form-item label="活动日期"
                    prop="dataTime"
                    class="form-input">
        <el-date-picker v-model="form.dataTime"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="请选择活动日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="上传附件"
                    class="form-upload">
        <el-upload class="form-upload-demo"
                   drag
                   action="/"
                   :before-remove="beforeRemove"
                   :before-upload="handleImg"
                   :http-request="imgUpload"
                   :file-list="file"
                   multiple>
          <div class="el-upload__text">将附件拖拽至此区域，或<em>点击上传</em></div>
          <div class="el-upload__tip">仅支持pdf、txt、doc、docx、xls、xlsx、zip、rar格式</div>
        </el-upload>
      </el-form-item>

      <!-- <el-form-item :label="'参加'+$position()"
                    class="form-title">
        <span slot="label"><span class="red">*</span>参加{{$position()}} </span>

        <candidates-box point="point_33"
                        :placeholder="'请选择参加'+$position()"
                        :data.sync="userData"></candidates-box>
      </el-form-item> -->

      <el-form-item class="form-item-user form-title ">
        <span slot="label"><span class="red">*</span>参加{{$position()}} </span>
        <el-button size="small"
                   plain
                   icon="el-icon-plus"
                   @click="userClick('1')">选择人员（{{ inviters.length }}）</el-button>
        <el-button @click="importFiles"
                   type="primary"
                   size="small"
                   plain
                   icon="el-icon-download">导入人员</el-button>
        <el-button type="primary"
                   size="small"
                   plain
                   icon="el-icon-upload2"
                   @click="exportFile">下载导入模板</el-button>
        <div class="user-list"
             ref="userRef"
             v-if="inviters.length"
             :style="{ paddingRight: (isMore ? 60 : 0) + 'px' }">
          <el-tag class="tag"
                  v-for="tag in inviters"
                  :key="tag.userId"
                  :disable-transitions="false"
                  @close.stop="remove(tag,'1')"
                  closable>
            {{ tag.name }}
          </el-tag>

        </div>

      </el-form-item>

      <el-form-item prop="content"
                    label="内容"
                    class="form-ue">
        <UEditor v-model="form.content"
                 :maximumWords="2000"></UEditor>
      </el-form-item>
      <div class="form-button">
        <el-button type="primary"
                   @click="submitForm('form')">保存</el-button>
        <el-button @click="reset"
                   v-if="!id&&!type">重置</el-button>
        <el-button @click="cancel"
                   v-if="id||type">取消</el-button>
      </div>
    </el-form>
    <xyl-popup-window v-model="userShow"
                      title="选择参加人员">
      <candidates-user point="point_33"
                       :data="userData"
                       @userCallback="userCallback"></candidates-user>
    </xyl-popup-window>
    <zy-pop-up v-model="showAdd"
               title="导入">
      <importFile @cloesWin="cloesWin"></importFile>
    </zy-pop-up>
  </div>
</template>
<script>
import importFile from '../../../views/newMeeting/newMeetingAdd/add/widget/widget/import.vue'
export default {
  name: 'AddActivitiesThrough',
  data () {
    return {
      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),
      form: {
        title: '', // 提案案由
        bigType: '', // 大类
        smallType: '', // 小类
        dataTime: '',
        content: '', // 提案内容
        org: '', // 组织部门
        remarks: '' // 发布部门
      },
      inviters: [], // 参加人
      userShow: false,
      showAdd: false,
      isMore: false,
      importType: '',
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        bigType: [
          { required: true, message: '请选择活动大类', trigger: ['blur', 'change'] }
        ],
        smallType: [
          { required: false, message: '请选择活动小类', trigger: ['blur', 'change'] }
        ],
        org: [
          { required: true, message: '请选择组织部门', trigger: ['blur', 'change'] }
        ],
        dataTime: [
          { required: true, message: '请选择活动日期', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' }
        ]
      },
      bigType: [],
      smallType: [],
      userData: [],
      file: []
    }
  },
  components: {
    importFile
  },
  props: ['id', 'type'],
  mounted () {
    this.treeList()
    if (this.id) {
      this.activityfillInfo()
    }
    this.orgArr = this.loadAll()
    this.orgArrOrganizer = this.loadAllOrganizer()
  },
  watch: {
    'form.bigType' (val) {
      if (val) {
        this.bigType.forEach(item => {
          if (item.id === val) {
            this.form.smallType = ''
            this.smallType = item.children
            if (this.smallType.length) {
              this.rules.smallType = [{ required: true, message: '请选择活动小类', trigger: ['blur', 'change'] }]
            } else {
              this.rules.smallType = [{ required: false, message: '请选择活动小类', trigger: ['blur', 'change'] }]
              this.$refs.form.clearValidate('smallType')
            }
          }
        })
      } else {
        this.form.smallType = ''
        this.smallType = []
        this.rules.smallType = [{ required: false, message: '请选择活动小类', trigger: ['blur', 'change'] }]
        this.$refs.form.clearValidate('smallType')
      }
    }
  },
  methods: {
    // 移除tag
    remove (data, importFile) {
      var userData = []
      if (importFile === '1') {
        userData = this.inviters
        this.inviters = userData.filter(v => v.userId !== data.userId)
      }
    },
    async exportFile () {
      await this.$api.meeting.newMeeting.getConferenceUserTemplate()
    },
    cloesWin (data) {
      this.showAdd = false
      if (data.length) this.inviters = data
    },
    importFiles () {
      this.showAdd = true
    },
    collapse () {
      var userRef = this.$refs.userRef
      if (userRef) {
        var width = 0
        for (let index = 0; index < userRef.childNodes.length; index++) {
          if (userRef.childNodes[index].offsetWidth !== undefined) {
            width += userRef.childNodes[index].offsetWidth + 10
          }
        }
        if (userRef.offsetWidth < width) {
          this.isMore = true
        } else {
          this.isMore = false
        }
      }
    },
    userClick (importType) {
      this.importType = importType
      if (importType === '1') {
        this.userData = this.inviters
      }
      this.userShow = !this.userShow
    },
    userCallback (data, flag) {
      if (flag) {
        this.inviters = data
      }
      this.userShow = !this.userShow
      this.$nextTick(() => {
        this.collapse()
      })
    },
    /**
     *机构树
    */
    async treeList () {
      // const res = await this.$api.systemSettings.treeList({ treeType: 4 })
      const res = await this.$api.systemSettings.treeList({ treeType: 3 })
      var { data } = res
      this.bigType = data
    },
    /**
     *详情
    */
    async activityfillInfo () {
      const res = await this.$api.activity.activityfillInfo(this.id)
      var { data } = res
      console.log(data)
      this.form.title = data.name
      this.form.bigType = data.bigType
      setTimeout(() => {
        this.form.smallType = data.smallType
      }, 520)
      this.form.dataTime = data.dataTime
      this.form.content = data.content
      this.inviters = data.chooseUserVo
      this.form.org = data.org // 组织部门
      this.form.remarks = data.remarks // 发布部门
      if (data.attachmentList) {
        data.attachmentList.forEach(item => {
          item.name = item.fileName
        })
        this.file = data.attachmentList
      }
    },
    /**
     * 限制上传附件的文件类型
    */
    handleImg (file, fileList) {
    },
    /**
     * 上传附件请求方法
    */
    imgUpload (files) {
      const areaId = sessionStorage.getItem('areaId' + this.$logo()) || ''
      const param = new FormData()
      param.append('module', 'ta')
      param.append('siteId', JSON.parse(areaId))
      param.append('attachment', files.file)
      this.$api.proposal.proposalfile(param).then(res => {
        var { data } = res
        data[0].name = data[0].fileName
        this.file.push(data[0])
      }).catch(() => {
        files.onError()
      })
    },
    /**
     * 删除附件
    */
    beforeRemove (file, fileList) {
      var fileData = this.file
      this.file = fileData.filter(item => item.id !== file.id)
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/activityfill/add'
          if (this.id) {
            url = '/activityfill/edit'
          }
          if (!this.inviters.length) {
            this.$message({
              message: '请选择参加' + this.$position(),
              type: 'warning'
            })
            return
          }
          var userIds = []
          var attachmentIds = []
          this.inviters.forEach(item => {
            userIds.push(item.userId)
          })
          this.file.forEach(item => {
            attachmentIds.push(item.id)
          })
          this.$api.systemSettings.generalAdd(url, {
            empty: '1', // 置空过滤标识
            id: this.id,
            name: this.form.title,
            bigType: this.form.bigType,
            smallType: this.form.smallType,
            dataTime: this.form.dataTime,
            content: this.form.content,
            attachmentIds: attachmentIds.join(','),
            userIds: userIds.join(','),
            org: this.form.org, // 组织部门
            remarks: this.form.remarks // 发布部门
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              this.$emit('callback')
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    reset () {
      this.$confirm('重置将不会保存当前已编辑的内容, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('callback')
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重置'
        })
      })
    },
    /**
     * 取消按钮
    */
    cancel () {
      this.$emit('callback')
    },
    querySearch (queryString, cb) {
      var orgArr = this.orgArr
      var results = queryString ? orgArr.filter(this.createFilter(queryString)) : orgArr
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter (queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0)
      }
    },
    loadAll () {
      return [
        { id: '001', value: '办公室' },
        { id: '002', value: '研究室' },
        { id: '003', value: '专委会一室' },
        { id: '004', value: '专委会二室' },
        { id: '005', value: '专委会三室' },
        { id: '006', value: '专委会四室' },
        { id: '007', value: '专委会五室' },
        { id: '008', value: '专委会六室' }]
    },
    organizerSearch (organizerSearch, cb) {
      var orgArrOrganizer = this.orgArrOrganizer
      var results = organizerSearch ? orgArrOrganizer.filter(this.organizerFilter(organizerSearch)) : orgArrOrganizer
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    organizerFilter (organizerSearch) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(organizerSearch.toLowerCase()) === 0)
      }
    },
    loadAllOrganizer () {
      return [
        { id: '001', value: '专委会活动' },
        { id: '002', value: '界别活动' },
        { id: '003', value: '委员工作站活动' }]
    }
  }
}
</script>
<style lang="scss">
.AddActivitiesThrough {
  width: 988px;
  .red {
    color: #f56c6c;
    margin-right: 4px;
  }
  .newForm
    .form-upload
    .form-upload-demo
    .el-upload
    .el-upload-dragger
    .el-upload__text {
    padding-top: 0px;
    margin-top: 22px;
  }
}
</style>
