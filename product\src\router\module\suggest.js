/**
 * 建议者
 */
// 提交建议
const suggestNew = () => import('@/views/mainModule/suggest/suggestBehalf/suggestNew')
// 我领衔的建议
const suggestLedBy = () => import('@/views/mainModule/suggest/suggestBehalf/suggestLedBy')
// 我附议的建议
const suggestJoint = () => import('@/views/mainModule/suggest/suggestBehalf/suggestJoint')
// 草稿箱
const suggestDraftBox = () => import('@/views/mainModule/suggest/suggestBehalf/suggestDraftBox')
/**
 * 建议管理
 */
// 所有建议
const suggestAll = () => import('@/views/mainModule/suggest/suggestManagement/suggestAll/suggestAll')
// 所有建议
const suggestLogo = () => import('@/views/mainModule/suggest/suggestManagement/suggestLogo/suggestLogo')
// 代表团审查
const delegationReview = () => import('@/views/mainModule/suggest/suggestManagement/suggestReview/delegationReview')
// 待审查
const suggestReview = () => import('@/views/mainModule/suggest/suggestManagement/suggestReview/suggestReview')
// 待复审
const suggestRecheck = () => import('@/views/mainModule/suggest/suggestManagement/suggestReview/suggestRecheck')
// 待审定
const suggestApproval = () => import('@/views/mainModule/suggest/suggestManagement/suggestReview/suggestApproval')
// 不予立案
const NotToPutOnRecordSuggest = () => import('@/views/mainModule/suggest/suggestManagement/NotThrough/NotToPutOnRecordSuggest')
// 转参阅件
const TurnToReferToaSuggest = () => import('@/views/mainModule/suggest/suggestManagement/NotThrough/TurnToReferToaSuggest')
// 撤案
const cancelSuggest = () => import('@/views/mainModule/suggest/suggestManagement/NotThrough/cancelSuggest')
// 人大交办中
const npcAssigned = () => import('@/views/mainModule/suggest/suggestManagement/suggestAssigned/npcAssigned')
// 政府交办中
const governmentSuggest = () => import('@/views/mainModule/suggest/suggestManagement/suggestAssigned/governmentSuggest')
// 党委交办中
const partyCommitteeSuggest = () => import('@/views/mainModule/suggest/suggestManagement/suggestAssigned/partyCommitteeSuggest')
// 两院交办中
const bothHousesSuggest = () => import('@/views/mainModule/suggest/suggestManagement/suggestAssigned/bothHousesSuggest')
// 法院交办中
const courtSuggest = () => import('@/views/mainModule/suggest/suggestManagement/suggestAssigned/courtSuggest')
// 检察院交办中
const procuratorateSuggest = () => import('@/views/mainModule/suggest/suggestManagement/suggestAssigned/procuratorateSuggest')
// 待签收建议
const waitingSignSuggest = () => import('@/views/mainModule/suggest/suggestManagement/suggestAssigned/waitingSignSuggest')
// 待签收申请调整
const applyAdjustSuggest = () => import('@/views/mainModule/suggest/suggestManagement/suggestAssigned/applyAdjustSuggest')
// 办理中
const DealtWithInSuggest = () => import('@/views/mainModule/suggest/suggestManagement/DealtWithInSuggest/DealtWithInSuggest')
// 已答复
const haveReplySuggest = () => import('@/views/mainModule/suggest/suggestManagement/haveReplySuggest/haveReplySuggest')
// 已办结
const haveTransferredSuggest = () => import('@/views/mainModule/suggest/suggestManagement/haveTransferredSuggest/haveTransferredSuggest')
// 办理单位申请调整
const unitApplyForAdjustSuggest = () => import('@/views/mainModule/suggest/suggestManagement/unitApplyForAdjustSuggest/unitApplyForAdjustSuggest')
// 办理单位申请延期
const applyForDelaySuggest = () => import('@/views/mainModule/suggest/suggestManagement/applyForDelaySuggest/applyForDelaySuggest')
// 跟踪办理
const TrackingHandleSuggest = () => import('@/views/mainModule/suggest/suggestManagement/TrackingHandleSuggest/TrackingHandleSuggest')
// 统计分析
const suggestStatistical = () => import('@/views/mainModule/suggest/suggestManagement/suggestStatistical/suggestStatistical')
// 统计分析详情列表
const suggestStatisticalList = () => import('@/views/mainModule/suggest/suggestManagement/suggestStatistical/suggestStatisticalList')
/**
 * 办理单位
 */
// 单位所有建议
const suggestAllUnit = () => import('@/views/mainModule/suggest/undertakeUnit/suggestAllUnit/suggestAllUnit')
// 单位待签收列表
const waitingSignSuggestUnit = () => import('@/views/mainModule/suggest/undertakeUnit/assignedByUnit/waitingSignSuggestUnit')
// 单位申请调整列表
const applyAdjustSuggestUnit = () => import('@/views/mainModule/suggest/undertakeUnit/assignedByUnit/applyAdjustSuggestUnit')
// 单位历史调整建议
const AdjustRecordSuggest = () => import('@/views/mainModule/suggest/undertakeUnit/assignedByUnit/AdjustRecordSuggest')
// 单位办理中
const UnitDealtWithInSuggest = () => import('@/views/mainModule/suggest/undertakeUnit/UnitDealtWithInSuggest/UnitDealtWithInSuggest')
// 单位已答复
const UnitHaveReplySuggest = () => import('@/views/mainModule/suggest/undertakeUnit/UnitHaveReplySuggest/UnitHaveReplySuggest')
// 单位已办结
const UnitHaveTransferredSuggest = () => import('@/views/mainModule/suggest/undertakeUnit/UnitHaveTransferredSuggest/UnitHaveTransferredSuggest')
// 单位跟踪办理
const UnitTrackingHandleSuggest = () => import('@/views/mainModule/suggest/undertakeUnit/UnitTrackingHandleSuggest/UnitTrackingHandleSuggest')
/**
 * 配置管理
 */
// 界次编号
const suggestTimeSerialNumber = () => import('@/views/mainModule/suggest/suggestConfiguration/suggestTimeSerialNumber/suggestTimeSerialNumber')
// 界次年份
const suggestTimeYear = () => import('@/views/mainModule/suggest/suggestConfiguration/suggestTimeYear/suggestTimeYear')
// 提案分类
const suggestType = () => import('@/views/mainModule/suggest/suggestConfiguration/suggestType/suggestType')
// 办理单位
// const suggestHandleUnit = () => import('@/views/mainModule/suggest/suggestConfiguration/suggestHandleUnit/suggestHandleUnit')
// 提案分类用户
const suggestBusinessAndUsers = () => import('@/views/mainModule/suggest/suggestConfiguration/suggestBusinessAndUsers/suggestBusinessAndUsers')

// 提案详情
const suggestDetails = () => import('@/views/mainModule/suggest/components/suggestDetails')

const suggest = [
  { // 提交建议
    path: '/suggestNew',
    name: 'suggestNew',
    component: suggestNew
  },
  { // 我领衔的建议
    path: '/suggestLedBy',
    name: 'suggestLedBy',
    component: suggestLedBy
  },
  { // 我附议的建议
    path: '/suggestJoint',
    name: 'suggestJoint',
    component: suggestJoint
  },
  { // 草稿箱
    path: '/suggestDraftBox',
    name: 'suggestDraftBox',
    component: suggestDraftBox
  },
  { // 所有建议
    path: '/suggestAll',
    name: 'suggestAll',
    component: suggestAll
  },
  { // 所有建议
    path: '/suggestLogo',
    name: 'suggestLogo',
    component: suggestLogo
  },
  { // 代表团审查
    path: '/delegationReview',
    name: 'delegationReview',
    component: delegationReview
  },
  { // 待审查
    path: '/suggestReview',
    name: 'suggestReview',
    component: suggestReview
  },
  { // 待复审
    path: '/suggestRecheck',
    name: 'suggestRecheck',
    component: suggestRecheck
  },
  { // 待审定
    path: '/suggestApproval',
    name: 'suggestApproval',
    component: suggestApproval
  },
  { // 不予立案
    path: '/NotToPutOnRecordSuggest',
    name: 'NotToPutOnRecordSuggest',
    component: NotToPutOnRecordSuggest
  },
  { // 转参阅件
    path: '/TurnToReferToaSuggest',
    name: 'TurnToReferToaSuggest',
    component: TurnToReferToaSuggest
  },
  { // 撤案
    path: '/cancelSuggest',
    name: 'cancelSuggest',
    component: cancelSuggest
  },
  { // 人大交办中
    path: '/npcAssigned',
    name: 'npcAssigned',
    component: npcAssigned
  },
  { // 政府交办中
    path: '/governmentSuggest',
    name: 'governmentSuggest',
    component: governmentSuggest
  },
  { // 党委交办中
    path: '/partyCommitteeSuggest',
    name: 'partyCommitteeSuggest',
    component: partyCommitteeSuggest
  },
  { // 两院交办中
    path: '/bothHousesSuggest',
    name: 'bothHousesSuggest',
    component: bothHousesSuggest
  },
  { // 法院交办中
    path: '/courtSuggest',
    name: 'courtSuggest',
    component: courtSuggest
  },
  { // 检察院交办中
    path: '/procuratorateSuggest',
    name: 'procuratorateSuggest',
    component: procuratorateSuggest
  },
  { // 待签收建议
    path: '/waitingSignSuggest',
    name: 'waitingSignSuggest',
    component: waitingSignSuggest
  },
  { // 待签收申请调整
    path: '/applyAdjustSuggest',
    name: 'applyAdjustSuggest',
    component: applyAdjustSuggest
  },
  { // 办理中
    path: '/DealtWithInSuggest',
    name: 'DealtWithInSuggest',
    component: DealtWithInSuggest
  },
  { // 已答复
    path: '/haveReplySuggest',
    name: 'haveReplySuggest',
    component: haveReplySuggest
  },
  { // 已办结
    path: '/haveTransferredSuggest',
    name: 'haveTransferredSuggest',
    component: haveTransferredSuggest
  },
  { // 办理单位申请调整
    path: '/unitApplyForAdjustSuggest',
    name: 'unitApplyForAdjustSuggest',
    component: unitApplyForAdjustSuggest
  },
  { // 办理单位申请调整
    path: '/applyForDelaySuggest',
    name: 'applyForDelaySuggest',
    component: applyForDelaySuggest
  },
  { // 跟踪办理
    path: '/TrackingHandleSuggest',
    name: 'TrackingHandleSuggest',
    component: TrackingHandleSuggest
  },
  { // 统计分析
    path: '/suggestStatistical',
    name: 'suggestStatistical',
    component: suggestStatistical
  },
  { // 统计分析详情列表
    path: '/suggestStatisticalList',
    name: 'suggestStatisticalList',
    component: suggestStatisticalList
  },
  { // 单位待签收列表
    path: '/suggestAllUnit',
    name: 'suggestAllUnit',
    component: suggestAllUnit
  },
  { // 单位待签收列表
    path: '/waitingSignSuggestUnit',
    name: 'waitingSignSuggestUnit',
    component: waitingSignSuggestUnit
  },
  { // 单位申请调整列表
    path: '/applyAdjustSuggestUnit',
    name: 'applyAdjustSuggestUnit',
    component: applyAdjustSuggestUnit
  },
  { // 单位历史调整建议
    path: '/AdjustRecordSuggest',
    name: 'AdjustRecordSuggest',
    component: AdjustRecordSuggest
  },
  { // 单位办理中
    path: '/UnitDealtWithInSuggest',
    name: 'UnitDealtWithInSuggest',
    component: UnitDealtWithInSuggest
  },
  { // 单位已答复
    path: '/UnitHaveReplySuggest',
    name: 'UnitHaveReplySuggest',
    component: UnitHaveReplySuggest
  },
  { // 单位已办结
    path: '/UnitHaveTransferredSuggest',
    name: 'UnitHaveTransferredSuggest',
    component: UnitHaveTransferredSuggest
  },
  { // 单位跟踪办理
    path: '/UnitTrackingHandleSuggest',
    name: 'UnitTrackingHandleSuggest',
    component: UnitTrackingHandleSuggest
  },
  { // 界次编号
    path: '/suggestTimeSerialNumber',
    name: 'suggestTimeSerialNumber',
    component: suggestTimeSerialNumber
  },
  { // 界次年份
    path: '/suggestTimeYear',
    name: 'suggestTimeYear',
    component: suggestTimeYear
  },
  { // 提案分类
    path: '/suggestType',
    name: 'suggestType',
    component: suggestType
  },
  { // 用户关系管理
    path: '/suggestBusinessAndUsers',
    name: 'suggestBusinessAndUsers',
    component: suggestBusinessAndUsers
  },
  { // 建议详情
    path: '/suggestDetails',
    name: 'suggestDetails',
    component: suggestDetails
  }
]
export default suggest
