<template>
  <div class="activityData">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="newData">新增</el-button>
        <el-button type="primary"
                   @click="deleteClick">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="资料名称"
                           min-width="220"
                           prop="materialName">
            <template slot-scope="scope">
              <el-button @click="details(scope.row)"
                         type="text">{{scope.row.materialName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="是否APP显示"
                           v-if="$isAppShow()"
                           width="120">
            <template slot-scope="scope">
              <div class="table-icon">
                <i class="el-icon-check"
                   v-if="scope.row.isAppShow"></i>
                <i class="el-icon-close"
                   v-else></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="创建时间"
                           width="160">
            <template slot-scope="scope">{{scope.row.createDate|datefmt('YYYY-MM-DD')}}</template>
          </el-table-column>
          <el-table-column label="创建人"
                           min-width="120"
                           prop="createName"></el-table-column>
          <el-table-column label="操作"
                           fixed="right"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="editor(scope.row)"
                         type="primary"
                         plain
                         size="mini">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <xyl-popup-window v-model="show"
                      :title="id==''?'新增活动资料':'编辑活动资料'">
      <activityDataNew :id="id"
                       :meetId="meetId"
                       @newCallback="newCallback"></activityDataNew>
    </xyl-popup-window>
    <xyl-popup-window v-model="detailsShow"
                      title="活动资料详情">
      <activityDataDetails :id="id"></activityDataDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import activityDataNew from './activityDataNew'
import activityDataDetails from './activityDataDetails'
export default {
  name: 'activityData',
  data () {
    return {
      meetId: this.$route.query.meetId, // 活动会议id
      reRuter: this.$route.query.reRuter, // 上个路由地址
      keyword: '',
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      show: false,
      detailsShow: false
    }
  },
  mixins: [tableData],
  components: {
    activityDataNew,
    activityDataDetails
  },
  mounted () {
    this.materiainfoList()
  },
  methods: {
    search () {
      this.page = 1
      this.materiainfoList()
    },
    reset () {
      this.keyword = ''
      this.materiainfoList()
    },
    newData () {
      this.id = ''
      this.show = true
    },
    details (row) {
      this.id = row.id
      this.detailsShow = true
    },
    editor (row) {
      this.id = row.id
      this.show = true
    },
    newCallback () {
      this.materiainfoList()
      this.show = false
    },
    async materiainfoList () {
      const res = await this.$api.activity.materiainfoList({
        pageNo: this.page,
        pageSize: this.pageSize,
        meetId: this.meetId,
        keyword: this.keyword
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    howManyArticle (val) {
      this.materiainfoList()
    },
    whatPage (val) {
      this.materiainfoList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除当前选中的活动资料, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.materiainfoDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async materiainfoDel (id) {
      const res = await this.$api.activity.materiainfoDel({
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.materiainfoList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.activityData {
  width: 100%;
  height: 100%;
  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
