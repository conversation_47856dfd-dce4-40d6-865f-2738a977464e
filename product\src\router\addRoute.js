import Vue from 'vue'
import router from './index'
import _import from './_import'
const addRoute = (data = []) => {
  var routerPath = JSON.parse(sessionStorage.getItem('router' + Vue.prototype.$logo())) || data
  var routers = [
    {
      path: '/login',
      name: 'login',
      component: _import(`${routerPath.login}/${routerPath.login}`)
    },
    {
      path: '/home',
      name: 'home',
      component: _import(`${routerPath.home}/${routerPath.home}`)
    }
  ]
  let constantRouters = router.options.routes
  constantRouters = constantRouters.concat(routers)
  router.addRoutes(routers)
  router.options.routes = constantRouters
}
export { addRoute }
