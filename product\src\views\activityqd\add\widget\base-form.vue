<template>
  <el-form :model="form"
           class="newForm"
           ref="form"
           label-position="top"
           inline
           :rules="rules">
    <el-form-item label="活动主题"
                  prop="meetName"
                  class="form-title">
      <el-input v-model="form.meetName"></el-input>
    </el-form-item>
    <el-form-item label="活动类型"
                  prop="meetType"
                  class="form-input">
      <zy-cascader v-model="form.meetType"
                   :data="typeList"
                   placeholder="请选择类型">
      </zy-cascader>
    </el-form-item>
    <el-form-item label="组织部门"
                  prop="organizer"
                  class="form-input">
      <el-cascader clearable
                   placeholder="请选择部门"
                   v-model="form.organizer"
                   :options="orgList"
                   filterable
                   :props="{checkStrictly: true, emitPath: false,expandTrigger: true,label: 'name',value: 'id'}"
                   :show-all-levels="false"
                   ref="cascader"></el-cascader>
    </el-form-item>
    <el-form-item label="活动地址"
                  prop="address"
                  class="form-title">
      <el-input v-model="form.address"></el-input>
    </el-form-item>
    <el-form-item label="活动时间"
                  prop="time2"
                  class="form-title">
      <el-date-picker v-model="form.time2"
                      type="datetimerange"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间">
      </el-date-picker>
    </el-form-item>
    <el-form-item v-if="type !== 'make-up'"
                  label="签到时间"
                  prop="time1"
                  class="form-title">
      <el-date-picker v-model="form.time1"
                      type="datetimerange"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      range-separator="至"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间">
      </el-date-picker>
    </el-form-item>
    <el-form-item v-if="type !== 'make-up'"
                  label="活动报名截止时间"
                  prop="signEndTime"
                  class="form-input">
      <el-date-picker v-model="form.signEndTime"
                      placeholder="请选择时间"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss">
      </el-date-picker>
    </el-form-item>
    <br>
    <el-form-item label="是否APP显示"
                  class="form-input">
      <el-radio-group v-model="form.isAppShow">
        <el-radio :label="1">是</el-radio>
        <el-radio :label="0">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="短信通知邀请人"
                  class="form-input">
      <el-radio-group v-model="form.isNotice">
        <el-radio :label="1">是</el-radio>
        <el-radio :label="0">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="是否发布通知"
                  class="form-input">
      <el-radio-group v-model="form.isRelease">
        <el-radio :label="1">是</el-radio>
        <el-radio :label="0">否</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="内容"
                  prop="content"
                  class="form-ue">
      <UEditor v-model="form.content"></UEditor>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  props: {
    type: String
  },
  data () {
    var ckDate = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请选择'))
      } else {
        if (this.form.time2.length) {
          if (this.getTimes(value) > this.getTimes(this.form.time2[0])) {
            callback(new Error('报名截止时间不能晚于活动开始时间'))
          }
        }
        callback()
      }
    }
    var ckDateRange = (rule, value, callback) => {
      if (!value.length) {
        callback(new Error('请选择'))
      } else {
        if (this.form.time2.length) {
          if (this.getTimes(value[1]) > this.getTimes(this.form.time2[1])) {
            callback(new Error('签到结束时间不能晚于活动结束时间'))
          }
        }
        callback()
      }
    }
    return {
      form: {
        meetName: '',
        meetType: '',
        organizer: '',
        address: '',
        signEndTime: '',
        time1: [],
        time2: [],
        isAppShow: 1,
        isNotice: 1,
        isRelease: 1,
        content: ''
      },
      typeList: [],
      orgList: [],
      rules: {
        meetName: [{ required: true, message: '请输入活动主题', trigger: 'blur' }],
        meetType: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
        organizer: [{ required: true, message: '请选择组织部门', trigger: 'change' }],
        address: [{ required: true, message: '请输入活动地址', trigger: 'blur' }],
        // signEndTime: [{ required: true, message: '请选择签到截止时间', trigger: 'change' }],
        // time1: [{ required: true, message: '请选择签到时间', trigger: 'change' }],
        time2: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        time1: [{ type: 'array', required: true, validator: ckDateRange, trigger: 'change' }],
        signEndTime: [{ required: true, validator: ckDate, trigger: 'change' }]
      } // 签到人
      // pickerOptions: {
      //   disabledDate: (time) => {
      //     const nowDate = this.form.time2 ? new Date(this.form.time2[0]) : new Date()
      //     const oneDay = 1000 * 60 * 60 * 24
      //     const oneYearLater = new Date(nowDate.getTime() + (oneDay * 365))
      //     return time.getTime() < nowDate || time.getTime() > oneYearLater
      //   }
      // }
    }
  },
  created () {
    this.getTypeList()
  },
  methods: {
    getTimes (val) {
      var data = new Date(val)
      return data.getTime()
    },
    // 获取所属分类
    getTypeList () {
      this.$api.activity.treelist({ treeType: 1 }).then(res => {
        this.orgList = res.data
        this.filterList(this.orgList)
      })
      this.$api.activity.treelist({ treeType: 3 }).then(res => {
        this.typeList = res.data
      })
    },
    filterList (arr) {
      arr.forEach(item => {
        if (!item.children.length) {
          item.children = null
        } else {
          this.filterList(item.children)
        }
      })
    },
    validForm () {
      let result = false
      this.$refs.form.validate((valid) => { result = valid })
      return result
    },
    reset () {
      this.$refs.form.resetFields()
    }
  }
}
</script>
