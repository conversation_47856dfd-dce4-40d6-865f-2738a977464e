// 导入封装的方法
import {
  get,
  post,
  postform,
  exportFile
} from '../http'

export const getlog = params => get('', params)
export const login = params => post('', params)
export const home = params => postform('', params)
export const exe = params => exportFile('', params)

const BehalfTraining = {
  npctrainingstats (params) {
    return post('/npctraining/stats?', params)
  },
  totalstats (params) {
    return post('/npctraining/totalstats?', params)
  },
  // 资料查阅情况
  materialReadList (data) {
    return get('/npctraining/datastats', data)
  },
  // 考试情况
  examinationList (data) {
    return post('/paper/stats', data)
  },
  // 考试总体情况
  examinationAll (data) {
    return post('/paper/totalstats', data)
  },
  // 积分列表
  TrainingSourceList (data) {
    return post('/pcintegral/list', data)
  },
  // 生成积分
  TrainingSourceCreate (data) {
    return post('/pcintegral/generate', data)
  }
}

export default BehalfTraining
