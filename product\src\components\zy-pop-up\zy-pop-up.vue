
<template>
  <div class="pop-up">
    <div class="pop-up-cover"
         ref="popUp"
         v-if="hidden"></div>
    <transition enter-active-class="animate__animated animate__zoomIn"
                leave-active-class="animate__animated animate__zoomOut"
                :duration="300">
      <div class="pop-up-box"
           ref="popUpBox"
           v-if="hidden">
        <div class="pop-up-title"
             @mousedown="onMovedown"
             @mouseup="onMoveup"
             ref="popUpTitle">
          <div class="pop-up-text">{{title}}</div>
          <div @click.stop="Shutclick">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="pop-up-body">
          <el-scrollbar class="zy-pop-up-body"
                        ref="scrollMenuRef">
            <div class="zy-pop-up-body-box"
                 ref="zy-pop-up-body-box"
                 v-show="hiddens">
              <slot></slot>
            </div>
            <div v-show="!hiddens"
                 ref="placeholder"
                 class="placeholder"></div>
          </el-scrollbar>
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
import animated from 'animate.css' // eslint-disable-line
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
export default {
  name: 'zyPopUp',
  data () {
    return {
      hidden: this.value,
      hiddens: true,
      width: null,
      height: null,
      positionX: 0,
      positionY: 0,
      number: 0
    }
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    value: {
      type: Boolean,
      default: false
    },
    beforeClose: Function,
    appendChild: {
      type: String,
      default: 'body'
    },
    mount: {
      type: Boolean,
      default: true
    },
    scroll: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'id'
  },
  watch: {
    value (val) {
      this.hidden = val
      if (val) {
        this.$nextTick(() => {
          if (this.scroll) {
            this.$refs.scrollMenuRef.wrap.addEventListener('scroll', this.scrollMenu)
          }
          const that = this
          erd.listenTo(this.$refs['zy-pop-up-body-box'], (element) => {
            that.$nextTick(() => {
              that.width = element.offsetWidth
              that.height = element.offsetHeight
              that.size()
            })
          })
        })
      } else {
        erd.uninstall(this.$refs['zy-pop-up-body-box'])
        this.number = 0
      }
    }
  },
  mounted () {
    this.$nextTick(() => {
      if (this.mount) {
        const body = this.$route.matched[this.$route.matched.length - 1].instances.default.$el
        if (body.append) {
          body.append(this.$el)
        } else {
          body.appendChild(this.$el)
        }
      } else {
        const body = document.querySelector(this.appendChild)
        if (body.append) {
          body.append(this.$el)
        } else {
          body.appendChild(this.$el)
        }
      }
    })
  },
  methods: {
    scrollMenu (e) {
      var top = this.$refs.scrollMenuRef.wrap.scrollTop
      this.$emit('scroll', e, top)
    },
    size () {
      const body = this.$refs.popUp
      const PopUpBox = this.$refs.popUpBox
      if (this.height > body.offsetHeight * 0.8) {
        PopUpBox.style.height = '80%'
      } else {
        PopUpBox.style.height = this.height + 42 + 'px'
      }
      PopUpBox.style.top = (body.offsetHeight - PopUpBox.offsetHeight) / 2 + 'px'
      if (this.number === 0) {
        this.positionX = (body.offsetHeight - PopUpBox.offsetHeight) / 2
        this.positionY = (body.offsetWidth - PopUpBox.offsetWidth) / 2
        PopUpBox.style.top = (body.offsetHeight - PopUpBox.offsetHeight) / 2 + 'px'
        PopUpBox.style.left = (body.offsetWidth - PopUpBox.offsetWidth) / 2 + 'px'
        this.$nextTick(() => {
          var placeholder = this.$refs.placeholder
          placeholder.style.width = this.width + 'px'
          if (this.height > body.offsetHeight * 0.8) {
            placeholder.style.height = 'calc(100% - 42px)'
          } else {
            placeholder.style.height = this.height + 'px'
          }
          setTimeout(() => {
            this.hiddens = false
            setTimeout(() => {
              this.hiddens = true
            }, 22)
          }, 299)
        })
        this.number++
      }
    },
    // 鼠标按下移动
    onMovedown (e) {
      const pdiv = this.$refs.popUp
      const odiv = this.$refs.popUpBox
      const disX = e.clientX - odiv.offsetLeft
      const disY = e.clientY - odiv.offsetTop
      const diffWidth = pdiv.offsetWidth - odiv.offsetWidth
      const diffHeight = pdiv.offsetHeight - odiv.offsetHeight
      if (diffWidth <= 0 || diffHeight <= 0) {
        document.onmousemove = null
        document.onmouseup = null
        this.$refs.popUpTitle.style.cursor = 'default'
        return
      }
      document.onmousemove = (e) => {
        let left = e.clientX - disX
        let top = e.clientY - disY
        const minWidth = pdiv.offsetLeft
        const minHeight = pdiv.offsetTop
        const maxWidth = (pdiv.offsetLeft + pdiv.offsetWidth) - odiv.offsetWidth
        const maxHeight = (pdiv.offsetTop + pdiv.offsetHeight) - odiv.offsetHeight
        left = left < minWidth ? minWidth : left
        top = top < minHeight ? minHeight : top
        left = left > maxWidth ? maxWidth : left
        top = top > maxHeight ? maxHeight : top
        this.positionX = top
        this.positionY = left
        odiv.style.left = left + 'px'
        odiv.style.top = top + 'px'
      }
      document.onmouseup = (e) => {
        document.onmousemove = null
        document.onmouseup = null
      }
    },
    onMoveup () {
      document.onmousemove = null
      document.onmouseup = null
    },
    Shutclick () {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(this.Shut)
      } else {
        this.Shut()
      }
    },
    Shut () {
      this.$emit('id', false)
    }
  }
}
</script>
<style lang="scss">
@import "./zy-pop-up.scss";
</style>
