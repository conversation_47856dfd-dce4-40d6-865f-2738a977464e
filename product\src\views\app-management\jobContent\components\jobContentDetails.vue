<template>
  <div class="jobContentDetails details">
    <div class="details-title">详情</div>
    <div class="details-item-box">
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">排序</div>
          <div class="details-item-value">{{details.createBy}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">党组</div>
          <div class="details-item-value">{{details.leadingPartyGroupName}}</div>
        </div>
      </div>
      <div class="details-content"
           v-html="details.jobContent"></div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'jobContentDetails',
  data () {
    return {
      details: {}
    }
  },
  props: ['id'],
  mounted () {
    this.informationListInfo()
  },
  methods: {
    async informationListInfo () {
      const res = await this.$api.appManagement.jobcontentInfo(this.id)
      var { data } = res
      this.details = data
      console.log(this.details.attachList)
    },
    fileClick (row) {
      this.$api.proposal.downloadFile({ id: row.id }, row.fileName)
    }
  }
}
</script>
<style lang="scss">
.jobContentDetails {
  height: 100%;
  padding: 24px;

  .details-content {
    width: 100%;
    padding: 40px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
