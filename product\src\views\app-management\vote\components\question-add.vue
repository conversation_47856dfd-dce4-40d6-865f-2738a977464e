<template>
  <div class="question-add">
    <div class="add-box scrollBar">
      <el-form :model="addform"
               ref="addform"
               :rules="addRule"
               :label-position="'top'">
        <el-form-item label="题目"
                      prop="name">
          <el-input v-model="addform.name"></el-input>
        </el-form-item>
        <el-form-item label="所属分类"
                      prop="type">
          <zy-cascader width="222"
                       node-key="id"
                       clearable
                       v-model="addform.type"
                       :data="typeList"
                       placeholder="请选择分类">
          </zy-cascader>
          <!-- <el-select v-model="addform.type"
            placeholder="请选择">
            <el-option v-for="item in typeList"
              :key="item.id"
              :label="item.value"
              :value="item.id">
            </el-option>
          </el-select> -->
        </el-form-item>
        <!-- <el-form-item label="是否必答" prop="isFacing">
          <el-radio-group v-model="addform.isFacing">
            <el-radio :label="0">是</el-radio>
            <el-radio :label="1">否</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="题型"
                      prop="topic">
          <el-select v-model="addform.topic"
                     placeholder="请选择">
            <el-option v-for="item in topicList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选项"
                      v-if="addform.topic === 'single' || addform.topic === 'multi'">
          <el-button type="primary"
                     @click="addOption"
                     size="small">添加选项</el-button>
          <div class="option-item"
               v-for="(item,index) in option"
               :key="index">
            <el-input v-model="item.text"
                      placeholder="请输入选项"
                      class="input"
                      size="small"></el-input>
            <div class="close"
                 @click="delOption(index)">
              <i class="el-icon-close"></i>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="正确选项">
          <div v-if="addform.topic === 'single'">
            <el-radio-group v-model="AnswerSingle">
              <p v-for="(op,idx) in option"
                 :key="idx"
                 class="options-item">
                <el-radio :label="op.text">{{op.text}}</el-radio>
              </p>
            </el-radio-group>
          </div>
          <div v-if="addform.topic === 'multi'">
            <el-checkbox-group v-model="AnswerMulti">
              <p v-for="(ops,idx) in option"
                 :key="idx"
                 class="options-item">
                <el-checkbox :label="ops.text"></el-checkbox>
              </p>
            </el-checkbox-group>
          </div>
          <div v-if="addform.topic === 'judge'">
            <el-radio-group v-model="AnswerJudge">
              <p options-item>
                <el-radio :label="'1'">
                  <i class="el-icon-check"></i>
                </el-radio>
                <el-radio :label="'0'">
                  <i class="el-icon-close"></i>
                </el-radio>
              </p>
            </el-radio-group>
          </div>
          <!-- <el-select v-model="addform.topic" placeholder="请选择">
            <el-option v-for="item in topicList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="onSubmit('addform')">提交</el-button>
          <el-button @click="cancel('addform')">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    editData: Object,
    paperId: String
  },
  data () {
    return {
      isAdd: true,
      addform: {
        name: '',
        type: '',
        topic: ''
        // isFacing: ''
      },
      addRule: {
        name: [{ required: true, message: '请输入题目', trigger: 'blur' }],
        type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        isFacing: [{ required: true, message: '请选择回答', trigger: 'change' }],
        topic: [{ required: true, message: '请选择题型', trigger: 'blur' }]
      },
      option: [],
      AnswerSingle: '',
      AnswerJudge: '',
      AnswerMulti: [],
      typeList: [],
      topicList: [
        { value: 'single', label: '单选' },
        { value: 'multi', label: '多选' },
        { value: 'judge', label: '判断' }
        // { value: 'text', label: '文本' }
      ]
    }
  },
  mounted () {
    if (this.editData) {
      this.addform.name = this.editData.name
      this.addform.type = this.editData.type
      this.addform.topic = this.editData.topic
      if (this.editData.topic === 'single') {
        this.AnswerSingle = this.editData.modelAnswer
      }
      if (this.editData.topic === 'multi') {
        this.AnswerMulti = this.editData.modelAnswer.split('|')
      }
      if (this.editData.topic === 'judge') {
        this.AnswerJudge = this.editData.modelAnswer
      }
      console.log(this.editData)
      // this.addform.isFacing = this.editData.isFacing
      if (this.editData.options) {
        console.log(1)
        this.option = this.editData.options.split('|').map(item => {
          return { text: item }
        })
      }
    }
    this.getTypeList()
  },
  methods: {
    // 添加选项
    addOption () {
      this.option.push({ text: '' })
    },
    // 删除选项
    delOption (index) {
      this.option.splice(index, 1)
    },
    // 提交
    onSubmit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const data = this.addform
          if (this.paperId) { // 投票新增题目
            data.paperId = this.paperId
            data.isPublic = 1
          } else { // 题库新增题目
            data.isPublic = 0
          }
          if (data.topic === 'single' || data.topic === 'multi') {
            data.options = this.option.map(item => item.text).join('|')
          }
          if (data.topic === 'single' && this.AnswerSingle === '') {
            return this.$message.warning('请勾选正确的答案')
          }
          if (data.topic === 'multi' && this.AnswerMulti.length === 0) {
            return this.$message.warning('请勾选正确的答案')
          }
          if (data.topic === 'judge' && this.AnswerJudge === '') {
            return this.$message.warning('请勾选正确的答案')
          }
          if (data.topic === 'single') {
            data.modelAnswer = this.AnswerSingle
          }
          if (data.topic === 'multi') {
            data.modelAnswer = this.AnswerMulti.join('|')
          }
          if (data.topic === 'judge') {
            data.modelAnswer = this.AnswerJudge
          }
          if (this.editData) {
            data.id = this.editData.id
            this.$api.appManagement.questionEdit(data).then(res => {
              if (res.errcode) {
                this.$message.success('修改成功')
                this.$emit('cancel', true)
              }
            })
          } else {
            this.$api.appManagement.questionAdd(data).then(res => {
              if (res.errcode) {
                this.$message.success('新增成功')
                this.$emit('cancel', true)
              }
            })
          }
        } else {
          return false
        }
      })
    },
    // 取消
    cancel (formName) {
      this.$refs[formName].resetFields()
      this.option = [{ text: '' }]
      this.$emit('cancel')
    },
    // 获取所属分类
    getTypeList () {
      this.$api.systemSettings.treeList({ treeType: 13 }).then(res => {
        if (res.errcode === 200) {
          this.typeList = res.data
        } else {
          this.$message.error(res.errmsg)
        }
      })
      // this.$api.systemSettings.dictionaryPubkvs({ types: 'paper_type' }).then(res => {
      //   if (res.errcode === 200) {
      //     this.typeList = res.data.paper_type
      //   }
      // })
    }
  },
  watch: {
    isAdd () {
      if (!this.isAdd) {
        this.$emit('cancel')
      }
    }
  }
}
</script>

<style lang="scss">
.question-add {
  .add-box {
    width: 600px;
    padding: 10px 20px;
    max-height: 500px;
    .option-item {
      display: flex;
      .input {
        width: calc(100% - 42px);
      }
      .close {
        display: none;
        width: 32px;
        height: 32px;
        font-size: 18px;
        color: red;
        text-align: center;
        line-height: 32px;
      }
    }
    .options-item {
      line-height: 36px;
    }
    .option-item:hover {
      .close {
        display: block;
      }
    }
  }
}
</style>
