<template>
  <div class="academyAnnouncementDetails details">
    <div class="details-title">读书公告详情</div>
    <div class="details-item-box">
      <div class="details-item-title">
        <div class="details-item-label">标题</div>
        <div class="details-item-value">{{details.title}}</div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">发布部门</div>
          <div class="details-item-value">{{details.officeName}}</div>
        </div>
      </div>
      <div class="details-item-column">
        <div class="details-item">
          <div class="details-item-label">发布日期</div>
          <div class="details-item-value">{{details.publishDate|datefmt('YYYY-MM-DD HH:mm:ss')}}</div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">关联书籍</div>
        <div class="details-item-value">
          <div class="academyAnnouncementDetailsItem"
               v-for="(item) in book"
               :key="item.id">
            <div class="academyAnnouncementDetailsItemImg">
              <img :src="item.coverImgUrl"
                   alt="">
            </div>
            <div class="academyAnnouncementDetailsItemBox">
              <div class="academyAnnouncementDetailsItemName">{{item.bookName}}</div>
              <div class="academyAnnouncementDetailsItemIntroduction">{{item.bookDescription}}</div>
              <div class="academyAnnouncementDetailsItemAuthor">
                <div class="academyAnnouncementDetailsItemAuthorText">{{item.authorName}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="details-item">
        <div class="details-item-label">关联活动</div>
        <div class="details-item-value activityUser">
          <div class="details-item-file"
               v-for="(item, index) in list"
               :key="index"
               @click="fileClick(item)">标题：{{item.title}}</div>
        </div>
      </div>
      <div class="details-content"
           v-html="details.content"></div>
    </div>
    <xyl-popup-window v-model="show"
                      title="读书活动详情">
      <readingActivityDetails :id="uid"> </readingActivityDetails>
    </xyl-popup-window>
  </div>
</template>
<script>
import readingActivityDetails from '../readingActivity/readingActivityDetails'
export default {
  name: 'academyAnnouncementDetails',
  data () {
    return {
      details: {},
      book: [],
      list: [],
      uid: '',
      show: false
    }
  },
  props: ['id'],
  components: {
    readingActivityDetails
  },
  mounted () {
    this.syNoticeInfo()
    this.readschemeList()
  },
  methods: {
    async syNoticeInfo () {
      const res = await this.$api.academy.syNoticeInfo({ id: this.id })
      var { data } = res
      this.details = data
      if (data.books) {
        this.book = data.books
      }
    },
    async readschemeList () {
      const res = await this.$api.academy.readschemeList({
        noticeId: this.id
      })
      var { data } = res
      this.list = data
    },
    fileClick (row) {
      this.uid = row.id
      this.show = true
    }
  }
}
</script>
<style lang="scss">
.academyAnnouncementDetails {
  height: 100%;
  padding: 24px;
  .academyAnnouncementDetailsItem {
    display: flex;
    justify-content: space-between;
    width: 632px;
    height: 128px;
    margin: 12px 0;
    cursor: pointer;
    .academyAnnouncementDetailsItemImg {
      height: 128px;
      width: 95px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .academyAnnouncementDetailsItemBox {
      width: 522px;
      height: 100%;
      position: relative;
      .academyAnnouncementDetailsItemName {
        color: #333;
        line-height: 21px;
        font-size: 16px;
        margin-bottom: 7px;
      }
      .academyAnnouncementDetailsItemIntroduction {
        line-height: 24px;
        color: #666;
        letter-spacing: 0.93px;
        height: 72px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        font-size: 13px;
        white-space: normal !important;
      }
      .academyAnnouncementDetailsItemAuthor {
        position: absolute;
        left: 0;
        bottom: -2px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .academyAnnouncementDetailsItemAuthorText {
          font-size: 13px;
          color: #999;
          letter-spacing: 1px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
  .details-item-file {
    line-height: 22px;
    padding: 6px 0;
  }
  .activityUser {
    text-overflow: clip !important;
    white-space: normal !important;
  }
  .details-content {
    width: 100%;
    padding: 40px;

    img {
      max-width: 700px;
      margin: auto;
    }
  }
}
</style>
