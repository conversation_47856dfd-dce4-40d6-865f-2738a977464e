<template>
  <div class="materialScienceAdd">
    <materialScienceNew :id="id"
                      v-if="show"
                      @callback="callback"></materialScienceNew>
  </div>
</template>
<script>
const materialScienceNew = () => import('../materialScienceNew')
export default {
  name: 'materialScienceAdd',
  data () {
    return {
      show: true,
      id: this.$route.query.id,
      toId: this.$route.query.toId
    }
  },
  components: {
    materialScienceNew
  },
  inject: ['tabDel'],
  methods: {
    callback () {
      if (this.id) {
        this.tabDel(this.id, this.toId)
      } else {
        this.show = false
        setTimeout(() => {
          this.show = true
        }, 200)
      }
    }
  }
}
</script>
