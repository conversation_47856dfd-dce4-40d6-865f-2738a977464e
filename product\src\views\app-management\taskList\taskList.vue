<template>
  <div class="task-list">
    <div class="task-list-top">
      <el-tabs v-model="activeName"
               @tab-click="handleClick">
        <el-tab-pane label="政协党组"
                     name="1"></el-tab-pane>
        <el-tab-pane label="机关党组"
                     name="2"></el-tab-pane>
        <el-tab-pane label="分党组"
                     name="3"></el-tab-pane>
        <el-tab-pane label="党总支、各支部"
                     name="4"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="task-list-bottom-top">
      <div class=" task-list-bottom-table-title">
        <div class="table-title-top">通州区{{name}}{{year}}年党建工作任务清单</div>
        <div class="table-title-bottom">
          <ul>
            <li v-for="(item,index) in items"
                :key="index">
              {{item.label}}
            </li>
          </ul>
        </div>
      </div>
    </div>
    <el-scrollbar class="task-list-bottom">
        <div class="task-list-bottom-table-content">
          <div class="table-content-list"
              v-for="(item1,index1) in itemsContent"
              :key="index1">
            <div class="table-content-list-left">{{item1.jobContent}}</div>
            <div class="table-content-list-right">
              <div class="table-content-task"
                  v-for="(item,index) in item1.itemsTask"
                  :key="index">
                <div class="content-task-title">
                  <div>{{index+1}}</div>
                  <div>{{item.jobContent}}</div>
                </div>
                <div class="table-content-add">
                  <div class="table-content-box"
                      v-for="(list,index2) in item.formDataList"
                      :key="index2">
                    <div class="table-content-num">{{index2+1}}</div>
                    <div class="table-content-num">
                      <el-input type="textarea"
                                :autosize="{ minRows: 2, maxRows: 4}"
                                placeholder="请输入具体事项"
                                v-model="list.specificMatters">
                      </el-input>
                    </div>
                    <div class="table-content-num">
                      <zy-select node-key="id"
                                v-model="list.subjectOfResponsibility"
                                :data="institutions"
                                @select="subjectOfChoose"
                                placeholder="请选择"></zy-select>
                    </div>
                    <div class="table-content-num">
                      <candidates-box point="point_22"
                                      placeholder="请选择"
                                      :data.sync="list.subjectOfResponsibilityUserText"></candidates-box>
                    </div>
                    <div class="table-content-num">
                      <zy-select node-key="id"
                                v-model="list.undertakingReminder"
                                :data="institutions"
                                @select="undertakingChoose"
                                placeholder="请选择"></zy-select>
                    </div>
                    <div class="table-content-num">
                      <candidates-box point="point_22"
                                      placeholder="请选择"
                                      :data.sync="list.undertakingReminderUserText"></candidates-box>
                    </div>
                    <div class="table-content-num">
                      <el-input v-model="list.keyIssuesTime"
                                placeholder="请输入"></el-input>
                    </div>
                    <div class="table-content-num">
                      <el-date-picker v-model="list.sendSmsTime"
                                      type="datetime"
                                      value-format="yyyy-MM-dd HH:mm:ss"
                                      placeholder="请选择时间">
                      </el-date-picker>
                    </div>
                    <div class="table-content-num">
                      <el-select v-model="list.smsTemplateType"
                                filterable
                                clearable
                                placeholder="请选择">
                        <el-option v-for="items in smsTemplateTypeData"
                                  :key="items.code"
                                  :label="items.name"
                                  :value="items.code">
                        </el-option>
                      </el-select>
                    </div>
                    <div class="table-content-num">
                      <el-button  v-if="list.id" size="small"
                                @click="saveDomain(list.id,item1.id,list,'edit')">编辑</el-button>
                      <el-button v-else size="small"
                                @click="saveDomain(item.id,item1.id,list,'add')">保存</el-button>
                      <el-button size="small"
                                @click="delDomain(index1,index,list.id,index2)">删除</el-button>
                    </div>
                  </div>
                  <div class="table-content-button">
                    <el-button size="small"
                              @click="addDomain(index1,index)">添加一项</el-button>
                    <!-- <el-button size="small"
                              @click="delDomain(index)">删除一项</el-button> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </el-scrollbar>
  </div>
</template>
<script>
export default {
  data () {
    return {
      name: '政协党组',
      year: '',
      activeName: '1',
      items: [{
        label: '工作内容'
      }, {
        label: '工作任务'
      }, {
        label: '具体事项'
      }, {
        label: '责任主体'
      }, {
        label: '责任人'
      }, {
        label: '承办提醒主体'
      }, {
        label: '承办提醒人'
      }, {
        // label: '重点事项时间截点'
        label: '时间截点'
      }, {
        label: '定时发送短信'
      }, {
        label: '短信模板'
      }, {
        label: '操作'
      }],
      itemsContent: [],
      input: '',
      textarea: '',
      smsTemplateTypeData: [],
      disabled: false,
      mainSubmitUser: [],
      institutions: [],
      subjectList: [],
      undertakingList: []
    }
  },
  beforeMount () {
    this.contentList()
  },
  mounted () {
    const date = new Date()
    this.year = date.getFullYear()
    this.taskList()
    this.treeList()
    this.smsTemplateTypeList()
  },
  methods: {
    subjectOfChoose(data) {
      if (this.subjectList.length > 0) {
        let i = 0
        for (; i < this.subjectList.length; i++) {
          if (this.subjectList[i].id && this.subjectList[i].id === data.id) {
            break
          }
        }
        if (i >= this.subjectList.length) {
          this.subjectList.push({ id: data.id, label: data.label })
        }
      } else {
        this.subjectList.push({ id: data.id, label: data.label })
      }
    },
    undertakingChoose(data) {
      if (this.undertakingList.length > 0) {
        let i = 0
        for (; i < this.undertakingList.length; i++) {
          if (this.undertakingList[i].id && this.undertakingList[i].id === data.id) {
            break
          }
        }
        if (i >= this.undertakingList.length) {
          this.undertakingList.push({ id: data.id, label: data.label })
        }
      } else {
        this.undertakingList.push({ id: data.id, label: data.label })
      }
    },
    /**
     *机构树
    */
    async treeList () {
      const res = await this.$api.systemSettings.treeList({})
      var { data } = res
      this.institutions = data
    },
    handleClick (tab, event) {
      this.contentList()
      this.taskList()
      this.smsTemplateTypeList()
      this.name = tab.label
    },
    addDomain (index1, index) {
      const temp = {
        index: this.itemsContent[index1].itemsTask[index].formDataList.length,
        specificMatters: '',
        subjectOfResponsibility: '',
        subjectOfResponsibilityText: '',
        subjectOfResponsibilityUser: [],
        subjectOfResponsibilityUserText: [],
        undertakingReminder: '',
        undertakingReminderText: '',
        undertakingReminderUser: [],
        undertakingReminderUserText: [],
        keyIssuesTime: '',
        sendSmsTime: '',
        smsTemplateType: '',
        smsTemplateName: ''
      }
      this.itemsContent[index1].itemsTask[index].formDataList.push(temp)
    },
    async delDomain (index1, index, rowid, listIndex) {
      // if (rowid) {
      this.$confirm('此操作将删除当前数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await this.$api.appManagement.tasklistDel(rowid)
        var { errcode, errmsg } = res
        if (errcode === 200) {
          this.itemsContent[index1].itemsTask[index].formDataList.splice(listIndex, 1)
          // this.contentList()
          // this.taskList()
          this.$message({
            message: errmsg,
            type: 'success'
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
      // }
    },
    async saveDomain (item, item1, list, type) {
      const subjectOfResponsibilityUser = [] // 责任人
      const subjectOfResponsibilityUserText = [] // 责任人
      if (list.subjectOfResponsibilityUserText) {
        list.subjectOfResponsibilityUserText.forEach(item => {
          subjectOfResponsibilityUserText.push(item.name)
          subjectOfResponsibilityUser.push(item.userId)
        })
      }
      const undertakingReminderUser = [] // 承办提醒人
      const undertakingReminderUserText = [] // 承办提醒人
      if (list.undertakingReminderUserText) {
        list.undertakingReminderUserText.forEach(item => {
          undertakingReminderUser.push(item.userId)
          undertakingReminderUserText.push(item.name)
        })
      }
      const param = {
        specificMatters: list.specificMatters,
        subjectOfResponsibility: list.subjectOfResponsibility,
        subjectOfResponsibilityText: this.getSubjectOfResponsibilityText(list.subjectOfResponsibility),
        subjectOfResponsibilityUser: subjectOfResponsibilityUser.join(','),
        subjectOfResponsibilityUserText: subjectOfResponsibilityUserText.join(','),
        undertakingReminder: list.undertakingReminder,
        undertakingReminderText: this.getUndertakingReminderText(list.undertakingReminder),
        undertakingReminderUser: undertakingReminderUser.join(','),
        undertakingReminderUserText: undertakingReminderUserText.join(','),
        keyIssuesTime: list.keyIssuesTime,
        sendSmsTime: list.sendSmsTime,
        smsTemplateType: list.smsTemplateType,
        smsTemplateName: list.smsTemplateName
      }
      var res = ''
      if (type === 'add') {
        param.jobContentId = item
        res = await this.$api.appManagement.tasklistAdd(param)
      } else {
        param.id = item
        res = await this.$api.appManagement.tasklistEdit(param)
      }
      const { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    },
    async smsTemplateTypeList () {
      var activeName = '1'
      if (this.activeName === '3') {
        activeName = 1
      } else {
        activeName = this.activeName
      }
      const res = await this.$api.appManagement.tasklistGetSmsTemplateList(activeName)
      var { data } = res
      this.smsTemplateTypeData = data
    },
    async contentList () { // 内容,任务
      const res = await this.$api.appManagement.jobcontentList({
        pageNo: this.page,
        pageSize: this.pageSize,
        leadingPartyGroup: this.activeName,
        orderBy: 'sort'
      })
      const contentList = []
      res.data.forEach(item => {
        if (item.parentId === '0') {
          contentList.push(item)
        }
      })

      contentList.forEach(item => {
        const itemsTask = []
        res.data.forEach(items => {
          if (item.id === items.parentId) {
            items.formDataList = []
            itemsTask.push(items)
          }
        })
        item.itemsTask = itemsTask
      })
      this.$nextTick(() => {
        this.itemsContent = contentList
      })
    },
    async taskList () {
      const res = await this.$api.appManagement.tasklistList({
        pageNo: this.page,
        pageSize: this.pageSize
      })
      if (this.itemsContent) {
        this.itemsContent.forEach(item => {
          item.itemsTask.forEach(items => {
            const formDataList = []
            res.data.forEach(datas => {
              if (items.id === datas.jobContentId) {
                items.formDataList = []
                datas.subjectOfResponsibilityUser = this.changeToArray(datas.subjectOfResponsibilityUser)
                datas.subjectOfResponsibilityUserText = this.changeToArray(datas.subjectOfResponsibilityUserText)
                if (datas.subjectOfResponsibilityUser) {
                  datas.subjectOfResponsibilityUser.forEach((item, index) => {
                    datas.subjectOfResponsibilityUserText[index] = { id: item, name: datas.subjectOfResponsibilityUserText[index] }
                  })
                }
                datas.undertakingReminderUser = this.changeToArray(datas.undertakingReminderUser)
                datas.undertakingReminderUserText = this.changeToArray(datas.undertakingReminderUserText)
                if (datas.undertakingReminderUser) {
                  datas.undertakingReminderUser.forEach((item, index) => {
                    datas.undertakingReminderUserText[index] = { id: item, name: datas.undertakingReminderUserText[index] }
                  })
                }
                formDataList.push(datas)
              }
            })
            items.formDataList = formDataList
          })
        })
      }
    },
    changeToArray (str) {
      if (str === null || str === '') return
      let arr = []
      if (str.length > 0) {
        if (str.indexOf(',') > -1) {
          arr = str.split(',')
        } else {
          arr = [str]
          return arr
        }
      }
      return arr
    },
    getSubjectOfResponsibilityText(subjectOfResponsibility) {
      let result = ''
      this.subjectList.forEach(item => {
        if (item.id === subjectOfResponsibility) {
          result = item.label
        }
      })
      return result
    },
    getUndertakingReminderText(undertakingReminder) {
      let result = ''
      this.undertakingList.forEach(item => {
        if (item.id === undertakingReminder) {
          result = item.label
        }
      })
      return result
    }
  }
}
</script>
<style lang="scss">
.task-list {
  width: 100%;
  height: 100%;
  padding: 10px;
  .task-list-top {
  }
  .task-list-bottom-top{
    .task-list-bottom-table-title {
      .table-title-top {
        text-align: center;
        font-size: 22px;
        color: #333;
        padding: 15px 0;
      }
      .table-title-bottom {
        ul {
          background: #92cddc;
          // display: flex;
          flex-wrap: nowrap;
          text-align: center;
          overflow: hidden;
          li {
            // flex: 1;
            border-right: 1px solid #333;
            padding: 15px 0;
            float: left;
          }
          li:nth-child(1) {
            width: 100px;
          }
          li:nth-child(2) {
            width: 210px;
          }
          li:nth-child(3) {
            width: 225px;
          }
          li:nth-child(4) {
            width: 105px;
          }
          li:nth-child(5) {
            width: 135px;
          }
          li:nth-child(6) {
            width: 140px;
          }
          li:nth-child(7) {
            width: 160px;
          }
          li:nth-child(8) {
            width: 130px;
          }
          li:nth-child(9) {
            width: 145px;
          }
          li:nth-child(10) {
            width: 175px;
          }
          li:nth-child(11) {
            width: 125px;
          }
          li:last-child {
            border-right: none;
          }
        }
      }
    }
  }
  .task-list-bottom {
    // height: calc(100% - 62px);
    height: calc(100% - 152px);
    border: 1px solid #000;
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    .task-list-bottom-table-content {
      .table-content-list {
        display: flex;
        width: 100%;
        border-bottom: 1px solid #000;
        .table-content-list-left {
          width: 100px;
          border-right: 1px solid #000;
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 5px;
        }
        .table-content-list-right {
          width: calc(100% - 100px);
          display: flex;
          flex-direction: column;
          height: 100%;
        }
      }
      .table-content-task {
        border-bottom: 1px solid #000;
        display: flex;
        width: 100%;
        overflow: hidden;
        height: 100%;
        .content-task-title {
          width: 212px;
          border-right: 1px solid #000;
          display: flex;
          div {
            display: flex;
            justify-content: center;
            align-items: center;
            float: left;
            width: 85%;
          }
          div:first-child {
            width: 15%;
            float: left;
            padding: 15px;
            border-right: 1px solid #000;
          }
          div:last-child {
            padding: 15px;
          }
        }
        .table-content-add {
          // width: 86.4%;
          float: left;
          width: calc(100% - 212px);
          .table-content-box {
            // display: flex;
            overflow: hidden;
            border-bottom: 1px solid #707070;
            .table-content-num {
              // flex: 3;
              border-right: 1px solid #707070;
              display: flex;
              justify-content: center;
              align-items: center;
              float: left;
              padding: 0 10px;
              // height: 50px;
              height: 100px;
              .el-textarea {
                .el-textarea__inner {
                  border: none;
                }
              }
              .el-select {
                .el-input__inner {
                  border: none;
                }
              }
              .el-input {
                .el-input__inner {
                  border: none;
                }
              }
              .zy-tree-select {
                width: 105px;
              }
              .el-date-editor.el-input {
                width: 104px;
              }
              .form-user-box-text {
                padding-top: 6px;
              }
            }
            .table-content-num:nth-child(1) {
              width: 20px;
            }
            .table-content-num:nth-child(2) {
              width: 205px;
            }
            .table-content-num:nth-child(3) {
              width: 106px;
            }
            .table-content-num:nth-child(4) {
              width: 136px;
            }
            .table-content-num:nth-child(5) {
              width: 141px;
            }
            .table-content-num:nth-child(6) {
              width: 161px;
            }
            .table-content-num:nth-child(7) {
              width: 131px;
            }
            .table-content-num:nth-child(8) {
              width: 146px;
            }
            .table-content-num:nth-child(9) {
              width: 176px;
            }
            .table-content-num:nth-child(10) {
              width: 145px;
              padding-top: 5px;
            }
            .table-content-num:last-child {
              border-right: none;
            }
            .candidates-box {
              border: none;
            }
          }
          .table-content-button {
            text-align: center;
            padding: 10px 0;
          }
        }
      }
      .table-content-task:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
