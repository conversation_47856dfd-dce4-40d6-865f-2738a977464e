<template>
  <div class="NewActivitiesThrough">
    <AddActivitiesThrough :id="''"
                          v-if="show"
                          @callback="callback"></AddActivitiesThrough>
  </div>
</template>
<script>
import AddActivitiesThrough from './AddActivitiesThrough'
export default {
  // 活动补录
  name: 'NewActivitiesThrough',
  data () {
    return {
      show: true
    }
  },
  components: {
    AddActivitiesThrough
  },
  methods: {
    callback () {
      this.show = false
      setTimeout(() => {
        this.show = true
      }, 200)
    }
  }
}
</script>
<style lang="scss">
</style>
