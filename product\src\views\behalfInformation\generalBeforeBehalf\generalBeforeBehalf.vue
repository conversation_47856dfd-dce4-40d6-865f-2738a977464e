<template>
  <div class="generalBeforeBehalf">
    <xyl-search-button @search="search"
                       @reset="reset">
      <template slot="search">
        <el-input placeholder="请输入关键词"
                  v-model="keyword"
                  clearable
                  @keyup.enter.native="search">
        </el-input>
        <el-select v-model="representerElement"
                   clearable
                   placeholder="请选择所属结构">
          <el-option v-for="item in representerElementData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="representerTeam"
                   clearable
                   placeholder="请选择代表团">
          <el-option v-for="item in representerTeamData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="party"
                   clearable
                   placeholder="请选择党派">
          <el-option v-for="item in partyData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="nation"
                   clearable
                   placeholder="请选择民族">
          <el-option v-for="item in nationData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="sex"
                   clearable
                   placeholder="请选择性别">
          <el-option v-for="item in sexData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <!-- <el-select v-model="historyId"
                   clearable
                   placeholder="请选择历届历次">
          <el-option v-for="item in history"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select> -->
        <el-select v-model="circlesId"
                   clearable
                   placeholder="请选择届">
          <el-option v-for="item in circlesData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="boutId"
                   clearable
                   placeholder="请选择次">
          <el-option v-for="item in boutData"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-date-picker v-model="startYear"
                        type="year"
                        value-format="yyyy"
                        placeholder="请选择起始年份">
        </el-date-picker>
        <el-date-picker v-model="endYear"
                        type="year"
                        value-format="yyyy"
                        placeholder="请选择截止年份">
        </el-date-picker>
        <el-select v-model="startBirthday"
                   clearable
                   placeholder="请选择起始年龄段">
          <el-option v-for="item in Birthday"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
        <el-select v-model="endBirthday"
                   clearable
                   placeholder="请选择结束年龄段">
          <el-option v-for="item in Birthday"
                     :key="item.id"
                     :label="item.value"
                     :value="item.id">
          </el-option>
        </el-select>
      </template>
      <template slot="button">
        <el-button type="primary"
                   @click="exportMethods">导出excel</el-button>
        <el-button type="primary"
                   v-if="roleList.includes('超级管理员') || user.id == '1'"
                   @click="deleteClick">删除</el-button>
      </template>
    </xyl-search-button>
    <div class="tableData">
      <zy-table>
        <el-table :data="tableData"
                  ref="table"
                  slot="zytable"
                  @select="selected"
                  @select-all="selectedAll">
          <el-table-column type="selection"
                           fixed="left"
                           width="60"></el-table-column>
          <el-table-column label="姓名"
                           width="120">
            <template slot-scope="scope">
              <el-button @click="details(scope.row)"
                         type="text">{{scope.row.userName}}</el-button>
            </template>
          </el-table-column>
          <el-table-column :label="'代表证号'"
                           width="160"
                           prop="userNumber"></el-table-column>
          <el-table-column label="性别"
                           width="90"
                           prop="sex"></el-table-column>
          <el-table-column label="所属届次"
                           width="160"
                           prop="circlesBoutName"></el-table-column>
          <el-table-column :label="'代表团'"
                           width="160"
                           prop="representerTeam"></el-table-column>
          <el-table-column label="所属结构"
                           width="160"
                           prop="representerElement"></el-table-column>
          <el-table-column label="出生年月"
                           width="160">
            <template slot-scope="scope">{{scope.row.birthday|datefmt('YYYY-MM')}}</template>
          </el-table-column>
          <el-table-column label="民族"
                           min-width="120"
                           prop="nation"></el-table-column>
          <el-table-column label="手机号码"
                           min-width="190"
                           prop="mobile"></el-table-column>
        </el-table>
      </zy-table>
    </div>
    <div class="paging_box">
      <el-pagination @size-change="howManyArticle"
                     @current-change="whatPage"
                     :current-page.sync="page"
                     :page-sizes="$pageSizes()"
                     :page-size.sync="pageSize"
                     background
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <zy-pop-up v-model="exportShow"
               title="导出">
      <zy-export systemType="2"
                 :params="params"
                 :excelId="excelId"
                 :type="memberType==3?5:6"
                 @callback="callback"></zy-export>
    </zy-pop-up>
  </div>
</template>
<script>
import tableData from '@/mixins/tableData'
import behalf from '../behalf'
export default {
  name: 'generalBeforeBehalf',
  data () {
    return {
      user: JSON.parse(sessionStorage.getItem('user' + this.$logo())),
      roleList: JSON.parse(sessionStorage.getItem('roleList' + this.$logo())),
      tableData: [],
      total: 0,
      page: 1,
      pageSize: this.$pageSize(),
      id: '',
      exportShow: false,
      excelId: '',
      params: {}
    }
  },
  mixins: [tableData, behalf],
  inject: ['newTab'],
  props: ['memberType'],
  mounted () {
    this.historymemberList()
    this.historymemberSelect()
    this.dictionaryPubkvs()
  },
  methods: {
    search () {
      this.page = 1
      this.historymemberList()
    },
    async historymemberSelect () {
      const res = await this.$api.memberInformation.historymemberSelect({ memberType: this.memberType })
      var { data } = res
      this.history = data
    },
    async historymemberList () {
      const res = await this.$api.memberInformation.historymemberList({
        systemType: '2',
        pageNo: this.page,
        pageSize: this.pageSize,
        memberType: this.memberType,
        keyword: this.keyword,
        representerElement: this.representerElement,
        representerTeam: this.representerTeam,
        party: this.party,
        nation: this.nation,
        sex: this.sex,
        startBirthday: this.startBirthday,
        endBirthday: this.endBirthday,
        historyId: this.historyId,
        circlesId: this.circlesId,
        boutId: this.boutId,
        startYear: this.startYear,
        endYear: this.endYear
      })
      var { data, total } = res
      this.tableData = data
      this.total = total
      this.$nextTick(function () {
        this.memoryChecked()
      })
    },
    details (row) {
      this.newTab({ name: '代表信息详情', menuId: '16620092811', to: '/behalfDetails', params: { id: row.id, memberType: this.memberType, type: '1' } })
    },
    howManyArticle (val) {
      this.historymemberList()
    },
    whatPage (val) {
      this.historymemberList()
    },
    deleteClick () {
      if (this.choose.length) {
        this.$confirm('此操作将删除该往届代表, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.historymemberDel(this.choose.join(','))
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
      } else {
        this.$message({
          message: '请至少选择一条数据',
          type: 'warning'
        })
      }
    },
    async historymemberDel (id) {
      const res = await this.$api.memberInformation.historymemberDel({
        memberType: this.memberType,
        ids: id
      })
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.choose = []
        this.selectObj = []
        this.historymemberList()
        this.$message({
          message: errmsg,
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss">
.generalBeforeBehalf {
  height: 100%;
  width: 100%;

  .tableData {
    height: calc(100% - 116px);
  }
}
</style>
