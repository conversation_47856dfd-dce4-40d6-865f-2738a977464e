// 导入封装的方法
import {
  post,
  get,
  _exportFile
} from '../http'
const linkTogether = {
  pairManage: {
    // 结对管理列表
    list(data) {
      return post('/interknitpairing/list', data)
    },
    // 结对管理新增
    add(data) {
      return post('/interknitpairing/add', data)
    },
    // 结对管理新增
    edit(data) {
      return post('/interknitpairing/edit', data)
    },
    // 删除结对管理
    dels(data) {
      return post('/interknitpairing/dels', data)
    },
    // 结对管理详情
    info(id) {
      return post(`/interknitpairing/info/${id}`)
    },
    // 结对代表人员
    man(data) {
      return post('/interknitpairing/getInterknitPairingJoinUsers', data)
    }
  },
  reply: {
    list(params) {
      return post('/interknitmail/fingReplyMails', params)
    },
    add(params) {
      return post('/interknitmail/replyMail', params)
    },
    edit(params) {
      return post('/interknitmailreply/edit', params)
    },
    dels(ids) {
      return post('/interknitmailreply/dels', { ids })
    },
    info(id) {
      return post(`/interknitmailreply/info/${id}`)
    }
  },
  makeUp: {
    list(params) {
      return post('/interknit/supplement/list', params)
    },
    add(params) {
      return post('/interknit/supplement/add', params)
    },
    edit(params) {
      return post('/interknit/supplement/edit', params)
    },
    dels(ids) {
      return post('/interknit/supplement/dels', { ids })
    },
    info(id) {
      return post(`/interknit/supplement/info/${id}`)
    }
  },
  // 常委会查看代表来信
  leadMailbox(data) {
    return post('/interknitmail/leadList', data)
  },
  // 常委会查看代表和选民来信
  MailBoxHasVoteAndrepresentative(params) {
    return post('/interknitmail/committeeList', params)
  },
  // 管理员查看信箱列表
  // adminMailBox(data) {
  //   return post('/interknitmail/list', data)
  // },
  // 设置是否公开
  setPublicState(data) {
    return post('/interknitmail/updateIsPublic', data)
  },
  // 删除信箱
  MailBoxDels(data) {
    return post('/interknitmail/dels', data)
  },
  // 来信详情
  MailBoxInfo(id) {
    return get(`/interknitmail/info/${id}`)
  },
  // 查看选民来信
  voterMail(data) {
    return post('/interknitmail/lookVotersList', data)
  },
  // 宣讲台和捎话台列表
  PreachList(data) {
    return post('/interknitpreach/list', data)
  },
  // 宣讲台和捎话台新增
  PreachAdd(data) {
    return post('/interknitpreach/add', data)
  },
  // 宣讲台和捎话台编辑
  PreachEdit(data) {
    return post('/interknitpreach/edit', data)
  },
  // 宣讲台和捎话台发布
  PreachPublish(data) {
    return post('/interknitpreach/updateIsRelease', data)
  },
  // 宣讲台和捎话台删除
  PreachDelete(data) {
    return post('/interknitpreach/dels', data)
  },
  // 宣讲台和捎话台详情
  PreachInfo(id) {
    return get(`/interknitpreach/info/${id}`)
  },
  // 选民信息列表
  VoterList(data) {
    return post('/interknitvoters/list', data)
  },
  // 选民信息修改
  VoterInfoEdit(data) {
    return post('/interknitvoters/edit', data)
  },
  // 删除选民
  VoterListDel(data) {
    return post('/interknitvoters/dels', data)
  },
  // 选民信息详情
  VoterInfo(id) {
    return get(`/interknitvoters/info/${id}`)
  },
  // 常委会联系代表情况统计针对领导 表头
  situationCountForLeadHeader() {
    return post('/interknit/supplement/findSupplementTrees')
  },
  // 常委会联系代表情况统计针对领导
  situationCountForLead(data) {
    return post('/interknit/supplement/findInterKnitDutys', data)
  },
  // 常委会联系代表情况统计针对结对代表
  situationCountForDeputy(data) {
    return post('/interknit/supplement/findInterKnitDutyByDb', data)
  },
  // 常委会维度导出
  committeeCountExport(params) {
    return _exportFile('/interknit/supplement/exportExcelByTree', params, '常委会组成人员联系统计.xlsx')
  },
  representativeCountExcel(params) {
    return _exportFile('/interknit/supplement/exportExcelByDb', params, '联系代表情况统计.xlsx')
  },
  /** 以下暂未使用 */

  // 代表查看信箱列表
  representativeMailBox(data) {
    return post('/interknitmail/representativeList', data)
  },
  // 来信编辑
  MailBoxEdit(params) {
    return post('/interknitmail/edit', params)
  },
  // 选民来信详情
  voterLetterInfo(id) {
    return post('/interknitmail/getInterknitVotersMailDetail', { id: id })
  }

}
export default linkTogether
