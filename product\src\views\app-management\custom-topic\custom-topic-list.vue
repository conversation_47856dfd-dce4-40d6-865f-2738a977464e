<template>
  <div class="custom-topic-list">
    <generalCustomTopic :module="module"
                        :columnModule="columnModule"></generalCustomTopic>
  </div>
</template>
<script>
import generalCustomTopic from '@/views/app-management/generalCustomTopic/generalCustomTopic'
export default {
  name: 'customTopicList',
  components: {
    generalCustomTopic
  },
  data () {
    return {
      module: '1',
      columnModule: '1'
    }
  },
  mounted () {
    if (this.$route.query.module) {
      var arr = this.$route.query.module.split('-')
      this.module = arr[0]
      this.columnModule = arr[1]
    }
  }
}
</script>
<style lang="scss">
.custom-topic-list {
  height: 100%;
  width: 100%;
}
</style>
