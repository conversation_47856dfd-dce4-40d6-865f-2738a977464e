export function parseTime (time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  // eslint-disable-next-line
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  // eslint-disable-next-line
  return time_str
}

export function json2excel (tableJson, filename) {
  import('@/assets/export/Export-Excel').then(excel => {
    const merges = tableJson.merges || []
    const tHeader = tableJson.tHeader
    const styleList = [
      'A1',
      'B1',
      'C1',
      'D1',
      'E1',
      'F1',
      'G1',
      'H1',
      'I1',
      'J1',
      'K1',
      'L1',
      'M1',
      'N1',
      'O1',
      'P1',
      'Q1',
      'R1',
      'S1',
      'T1',
      'U1',
      'V1',
      'W1',
      'X1',
      'Y1',
      'Z1',
      'AA1',
      'AB1',
      'AC1',
      'AD1',
      'AE1',
      'AF1',
      'AG1',
      'AH1',
      'AI1',
      'AJ1',
      'AK1',
      'AL1',
      'AM1',
      'AN1',
      'AO1',
      'AP1',
      'AQ1',
      'AR1',
      'AS1',
      'AT1',
      'AU1',
      'AV1',
      'AW1',
      'AX1',
      'AY1',
      'AZ1'
    ]
    const multiHeader = tableJson.multiHeader || []
    console.log(tableJson.filterVal)
    console.log(tableJson.tableData)
    const data = formatJson(tableJson.filterVal, tableJson.tableData)
    excel.export_json_to_excel({
      data, // 数据
      merges, // 合并
      styleList, // 样式
      multiHeader, // 表头
      header: tHeader, // 表头
      filename: filename // 文件名
    })
  })
}
// 数据过滤，时间过滤
function formatJson (filterVal, jsonData) {
  return jsonData.map(v =>
    filterVal.map(j => {
      if (j === 'timestamp') {
        return parseTime(v[j])
      } else {
        return v[j]
      }
    })
  )
}
