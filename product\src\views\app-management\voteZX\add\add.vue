<template>
  <div class="vote-add">
    <step :active="active"></step>
    <base-form v-show="active === 1" ref="baseForm"></base-form>
    <page-settings v-show="active === 2" ref="pageSettings"></page-settings>
    <vote-item v-show="active === 3" ref="voteItem"></vote-item>
    <rules v-show="active===4" :isEdit="isEdit" ref="voteAddRules"></rules>
    <footerBtn :active.sync="active" @submit="handleSubmit"></footerBtn>
  </div>
</template>

<script>
import _ from 'lodash'
import step from './widget/step'
import baseForm from './widget/base-form'
import pageSettings from './widget/page-settings'
import voteItem from './widget/vote-item'
import footerBtn from './widget/footer-btn'
import Rules from './widget/rules.vue'
export default {
  components: { step, footerBtn, pageSettings, voteItem, baseForm, Rules },
  data() {
    return {
      active: 1,
      isEdit: false
    }
  },
  created() {
    const id = this.$route.query.id
    if (id) {
      this.getInfo(id)
      this.isEdit = true
    } else {
      this.isEdit = false
    }
    console.log(this.isEdit)
  },
  inject: ['tabDelJump'],
  methods: {
    getInfo(id) {
      this.$api.appManagement.vote.info(id).then(res => {
        const {
          theme, startTime,
          endTime, isPublish,
          isAlluser, voteUser,
          publishSet, voteRule,
          attachmentList, displayMode,
          color, pageConfig,
          qdVoteOptions, periodSet,
          voteNum, voteType, playerMax
        } = res.data
        // 基本信息回显
        this.$refs.baseForm.form = {
          theme,
          time: [startTime, endTime],
          isPublish,
          isAlluser,
          voteRule,
          publishSet: publishSet.split(',').map(v => parseInt(v))
        }
        if (isAlluser === 0 && voteUser.length !== 0) {
          this.$refs.baseForm.userData = voteUser
        }
        // 页面信息回显
        this.$refs.pageSettings.form = {
          files: attachmentList.filter(v => v.moduleType === 'bigImageforVote'),
          files1: attachmentList.filter(v => v.moduleType === 'bgImageforVote'),
          displayMode: parseInt(displayMode),
          color,
          pageConfig: pageConfig.split(',')
        }
        // 投票选项回显
        this.$refs.voteItem.list = qdVoteOptions.map(v => {
          const files = v.attachmentList.filter(v => v.moduleType === 'bgImageforVoteItem')
          return {
            kid: v.id,
            id: v.id,
            sort: v.sort,
            name: v.name,
            instructions: v.instructions,
            files: files,
            files1: v.attachmentList.filter(v => v.moduleType === 'voteItemFile').map(item => {
              return {
                name: item.fileName,
                size: item.fileSize,
                type: item.fileType,
                url: item.filePath,
                id: item.id,
                uid: item.uid
              }
            }),
            imgPath: files.length > 0 ? files[0].filePath : null
          }
        })
        // 投票规则回显
        const rulesForm = {
          periodSet: parseInt(periodSet),
          voteNum: parseInt(voteNum),
          voteType: parseInt(voteType)
        }
        if (voteType === '2') {
          rulesForm.playerMax = parseInt(playerMax)
        }
        this.$refs.voteAddRules.form = rulesForm
      })
    },
    handleSubmit() {
      // 第一步的投票基本信息
      const isForm = this.$refs.baseForm.validForm()
      if (!isForm) {
        return this.$message.warning('有必填项未输入或者未选择')
      }
      const data = _.cloneDeep(this.$refs.baseForm.form)
      data.startTime = data.time[0]
      data.endTime = data.time[1]
      data.publishSet = data.publishSet.join(',')
      delete data.time
      if (data.isAlluser === 0) {
        data.voteUserIds = this.$refs.baseForm.userData.map(v => v.userId).join(',')
      }
      // 第二步的投票页面设置
      const isSettings = this.$refs.pageSettings.validForm()
      if (!isSettings) {
        return this.$message.warning('有必填项未输入或者未选择')
      }
      const settingData = this.$refs.pageSettings.form
      // 处理投票的相关附件
      data.attachmentIds = settingData.files.map(v => v.id).join(',')
      if (settingData.files1.length > 0) {
        data.attachmentIds = `${data.attachmentIds},${settingData.files1.map(v => v.id).join(',')}`
      }
      Object.assign(data, settingData)
      delete data.files
      delete data.files1
      data.pageConfig = data.pageConfig.join(',')
      // 处理 第三步的投票选项
      const voteItemList = this.$refs.voteItem.list
      if (voteItemList.length > 0) {
        data.voteOptions = voteItemList.map(v => {
          let attachmentIds = ''
          if (v.files.length > 0) {
            attachmentIds = v.files.map(v => v.id).join(',')
          }
          if (v.files1.length > 0) {
            attachmentIds = v.files1.map(v => v.id).join(',')
          }
          if (v.files1.length > 0 && v.files.length > 0) {
            attachmentIds = `${v.files.map(v => v.id).join(',')},${v.files1.map(v => v.id).join(',')}`
          }
          return {
            id: v.id || '',
            name: v.name,
            instructions: v.instructions,
            sort: v.sort,
            attachmentIds: attachmentIds
          }
        })
      }
      // 处理 第四步的投票规则
      const rulesData = this.$refs.voteAddRules.form
      Object.assign(data, rulesData)
      if (rulesData.voteType === 1) { delete data.playerMax }
      const id = this.$route.query.id
      if (id) {
        data.id = id
        this.$api.appManagement.vote.edit(data).then(res => {
          this.handleClose()
        })
      } else {
        this.$api.appManagement.vote.add(data).then(res => {
          this.handleClose()
        })
      }
    },
    // 关闭页面
    handleClose() {
      const { id, toId, mid } = this.$route.query
      if (id) {
        this.$message.success('编辑投票成功')
        this.tabDelJump(mid, toId)
      } else {
        this.$confirm('新增投票成功, 是否离开此页面?', '提示', {
          confirmButtonText: '是',
          cancelButtonText: '否',
          type: 'warning'
        }).then(() => {
          this.tabDelJump(mid, toId)
        }).catch(() => {
          this.$refs.baseForm.reset()
          this.$refs.userData = []
          this.$refs.pageSettings.reset()
          this.$refs.voteItem.list = []
          this.$refs.voteItem.originList = []
          this.$refs.voteAddRules.reset()
        })
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.vote-add {
  width: 100%;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 4px 6px 0px rgba(233, 233, 233, 0.4);
  padding: 20px 30px;
}
</style>
