// 通知公告
const noticeAnnouncement = () => import('@/views/SmallModule/noticeAnnouncement/noticeAnnouncement')
// 远程协商
const RemoteNegotiationList = () => import('@/views/SmallModule/RemoteNegotiation/RemoteNegotiationList')
// 模块管理
const moduleManagement = () => import('@/views/SmallModule/moduleManagement/moduleManagement')
// 委员说
const membersSaid = () => import('@/views/SmallModule/membersSaid/membersSaid')
// app安装率统计
const InstallationRates = () => import('@/views/SmallModule/InstallationRates/InstallationRates')
// 网络议政=意见征集
const NetworkPolitics = () => import('@/views/SmallModule/NetworkPolitics/NetworkPolitics')
// 网络议政=意见征集---组织方发布
const organisersRelease = () => import('@/views/SmallModule/NetworkPolitics/organisersRelease/organisersRelease')
// 网络议政=意见征集---分析
const NetworkPoliticsAnalysis = () => import('@/views/SmallModule/NetworkPolitics/Analysis/NetworkPoliticsAnalysis')
// 读书心得
const readingExperiencePolitics = () => import('@/views/SmallModule/readingExperience/readingExperiencePolitics')
// 智慧小助手---关键字管理
const SpecificKeyword = () => import('@/views/SmallModule/wisdomLittleHelper/SpecificKeyword')
// 智慧小助手---开头语管理
const beginningLanguage = () => import('@/views/SmallModule/wisdomLittleHelper/beginningLanguage')
// 考试管理
const examManagement = () => import('@/views/SmallModule/examManagement/examManagement')
// 考试详情
const examManagementDetails = () => import('@/views/SmallModule/examManagement/examManagementDetails')
// 提问监控
const questioningMonitoring = () => import('@/views/SmallModule/questioningMonitoring/questioningMonitoring')
// 敏感词管理
const SensitiveWord = () => import('@/views/SmallModule/SensitiveWord/SensitiveWord')
// 通讯录
const addressBook = () => import('@/views/SmallModule/addressBook/addressBook')
// 通讯录群组
const addressBookCollection = () => import('@/views/SmallModule/addressBook/addressBookCollection')
// 通讯录分组
const addressBookGroup = () => import('@/views/SmallModule/addressBook/addressBookGroup')
// 群成员列表
const groupMembers = () => import('@/views/SmallModule/addressBook/groupMembers')
// 个人通讯录
const personalAddressBook = () => import('@/views/SmallModule/addressBook/personalAddressBook')
// 个人通讯录分组
const personalAddressBookGroup = () => import('@/views/SmallModule/addressBook/personalAddressBookGroup')
// 互动交流文件
const interactiveFile = () => import('@/views/SmallModule/interactiveFile/interactiveFile')
// 运河书院互动交流文件
const readingInteractiveFile = () => import('@/views/SmallModule/readingExperience/interactiveFile/interactiveFile')
// 运河书院交流群管理
const readingAcademyGroup = () => import('@/views/SmallModule/readingExperience/academyGroup/academyGroup')
// 运河书院交流群统计
const readingStists = () => import('@/views/SmallModule/readingExperience/readingStists')
// ios兑换码管理
const appDownAddressList = () => import('@/views/SmallModule/appDownAddress/appDownAddressList')

const SmallModule = [
  { // 提问监控
    path: '/questioningMonitoring',
    name: 'questioningMonitoring',
    component: questioningMonitoring
  },
  { // 通知公告
    path: '/noticeAnnouncement',
    name: 'noticeAnnouncement',
    component: noticeAnnouncement
  },
  { // 远程协商
    path: '/RemoteNegotiationList',
    name: 'RemoteNegotiationList',
    component: RemoteNegotiationList
  },
  { // 模块管理
    path: '/moduleManagement',
    name: 'moduleManagement',
    component: moduleManagement
  },
  { // 委员说
    path: '/membersSaid',
    name: 'membersSaid',
    component: membersSaid
  },
  { // app安装率统计
    path: '/InstallationRates',
    name: 'InstallationRates',
    component: InstallationRates
  },
  { // 网络议政=意见征集
    path: '/NetworkPolitics',
    name: 'NetworkPolitics',
    component: NetworkPolitics
  },
  { // 读书心得
    path: '/readingExperiencePolitics',
    name: 'readingExperiencePolitics',
    component: readingExperiencePolitics
  },
  { // 网络议政=意见征集---组织方发布
    path: '/organisersRelease',
    name: 'organisersRelease',
    component: organisersRelease
  },
  { // 网络议政=意见征集---分析
    path: '/NetworkPoliticsAnalysis',
    name: 'NetworkPoliticsAnalysis',
    component: NetworkPoliticsAnalysis
  },
  { // 智慧小助手---关键字管理
    path: '/SpecificKeyword',
    name: 'SpecificKeyword',
    component: SpecificKeyword
  },
  { // 智慧小助手---开头语管理
    path: '/beginningLanguage',
    name: 'beginningLanguage',
    component: beginningLanguage
  },
  { // 考试管理
    path: '/examManagement',
    name: 'examManagement',
    component: examManagement
  },
  { // 提问监控
    path: '/examManagementDetails',
    name: 'examManagementDetails',
    component: examManagementDetails
  },
  { // 敏感词管理
    path: '/SensitiveWord',
    name: 'SensitiveWord',
    component: SensitiveWord
  },
  { // 通讯录
    path: '/addressBook',
    name: 'addressBook',
    component: addressBook

  },
  { // 通讯录分组
    path: '/addressBookGroup',
    name: 'addressBookGroup',
    component: addressBookGroup

  },
  { // 通讯录群组
    path: '/addressBookCollection',
    name: 'addressBookCollection',
    component: addressBookCollection

  },
  { // 群成员列表
    path: '/groupMembers',
    name: 'groupMembers',
    component: groupMembers
  },
  { // 个人通讯录
    path: '/personalAddressBook',
    name: 'personalAddressBook',
    component: personalAddressBook
  },
  { // 个人通讯录分组
    path: '/personalAddressBookGroup',
    name: 'personalAddressBookGroup',
    component: personalAddressBookGroup
  },
  { // 互动交流文件
    path: '/interactiveFile',
    name: 'interactiveFile',
    component: interactiveFile
  },
  { // 运河书院互动交流文件
    path: '/readingInteractiveFile',
    name: 'readingInteractiveFile',
    component: readingInteractiveFile
  },
  { // 运河书院交流群管理
    path: '/readingAcademyGroup',
    name: 'readingAcademyGroup',
    component: readingAcademyGroup
  },
  { // 运河书院交流群统计
    path: '/readingStists',
    name: 'readingStists',
    component: readingStists
  },
  { // APP下载地址管理
    path: '/appDownAddressList',
    name: 'appDownAddressList',
    component: appDownAddressList
  }
]
export default SmallModule
