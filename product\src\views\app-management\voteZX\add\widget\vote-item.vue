<template>
  <div class="vote-item-list">
    <search-box @search-click="search" @reset-click="reset" title="投票项筛选">
      <zy-widget label="关键字">
        <el-input v-model="keyword" placeholder="请输入关键字" clearable @keyup.enter.native="search"></el-input>
      </zy-widget>
    </search-box>
    <div class="qd-btn-box">
      <el-button type="primary" size="small" @click="handleAdd">新增</el-button>
      <!-- <el-button type="success" plain size="small" @click="handleUpload">通过模版添加</el-button>
      <el-button type="success" plain size="small" @click="handleDownload">下载模版</el-button> -->
      <el-button type="danger" size="small" plain @click="handleBatchDelete">删除</el-button>
    </div>
    <el-table :data="list" stripe ref="table" @selection-change="handleSelectionChange">
      <el-table-column type="selection" fixed="left" width="60"></el-table-column>
      <el-table-column label="排序" prop="sort" width="80"></el-table-column>
      <el-table-column label="封面图" width="120">
        <template slot-scope="scope">
          <el-image v-if="scope.row.imgPath" style="width: 60px; height: 60px" :src="scope.row.imgPath" :preview-src-list="srcList">
          </el-image>
          <span v-else>无封面图</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" prop="name" min-width="360" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" prop="format" width="100">
        <template slot-scope="scope">
          <el-button @click="handleEdit(scope.row)" type="text">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <zy-pop-up v-model="isAdd" :title="editData?'编辑投票项':'新建投票项'">
      <add-vote-item :editData="editData" @callback="handleCallback"></add-vote-item>
    </zy-pop-up>
  </div>
</template>

<script>
import AddVoteItem from './widget/add-vote-item.vue'
export default {
  components: { AddVoteItem },
  data() {
    return {
      keyword: '',
      list: [],
      selectionList: [],
      originList: [],
      isAdd: false,
      editData: null
    }
  },
  computed: {
    srcList: function () {
      return this.list.map(v => v.imgPath)
    }
  },
  methods: {
    search() {
      if (this.keyword === '') {
        return this.$message.warning('请输入想要搜索的')
      }
      this.list = this.originList.filter(v => v.name.indexOf(this.keyword) !== -1)
    },
    reset() {
      this.keyword = ''
      this.list = this.originList
    },
    handleBatchDelete() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择想要删除的项')
      }
      this.selectionList.map(v => this.deleteList('list', v.kid))
      this.selectionList.map(v => this.deleteList('originList', v.kid))
    },
    deleteList(type, kid) {
      this[type].splice(this[type].findIndex(v => v.kid === kid), 1)
    },
    handleSelectionChange(val) {
      this.selectionList = val
    },
    handleAdd() {
      this.isAdd = true
      this.editData = null
    },
    handleEdit(val) {
      this.editData = val
      this.isAdd = true
    },
    // handleUpload() {},
    // handleDownload() {},
    handleCallback(val) {
      if (!val.kid) {
        val.kid = new Date().getTime().toString()
        if (val.files.length > 0) {
          val.imgPath = val.files[0].filePath
        }
        this.list.push(val)
      } else {
        if (val.files.length > 0) {
          val.imgPath = val.files[0].filePath
        }
        this.list[this.list.findIndex(v => v.kid === val.kid)] = val
      }
      this.originList = this.list
      this.isAdd = false
    }
  }
}
</script>
<style lang="scss">
.vote-item-list {
  .search-box {
    box-shadow: none;
    margin: 10px 0;
  }
  .qd-btn-box {
    height: 54px;
    display: flex;
    align-items: center;
  }
}
</style>
