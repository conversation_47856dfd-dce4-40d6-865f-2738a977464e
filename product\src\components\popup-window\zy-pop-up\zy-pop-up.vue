
<template>
  <div class="zy-pop-up"
       ref="popupWindow">
    <div class="zy-pop-up-cover"
         v-if="value"
         ref="zyPopUpCover"></div>
    <transition enter-active-class="animate__animated animate__zoomIn animate__faster"
                leave-active-class="animate__animated animate__zoomOut animate__faster">
      <div class="zy-pop-up-content"
           v-if="value"
           ref="zyPopUpContent">
        <div class="zy-pop-up-head"
             @mousedown="onMovedown"
             @mouseup="onMoveup">
          <div class="zy-pop-up-title">{{title}}</div>
          <div @click="closeClick"><i class="el-icon-close"></i></div>
        </div>
        <el-scrollbar class="zy-pop-up-body">
          <slot></slot>
        </el-scrollbar>
      </div>
    </transition>
  </div>
</template>
<script>
import animated from 'animate.css' // eslint-disable-line
import elementResizeDetectorMaker from 'element-resize-detector'
const erd = elementResizeDetectorMaker()
export default {
  name: 'zyPopUp',
  data () {
    return {
      width: null,
      height: null,
      positionX: 0,
      positionY: 0
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    beforeClose: Function
  },
  mounted () {
  },
  updated () {
  },
  methods: {
    location () {
      this.$nextTick(() => {
        this.height = 0
        this.$slots.default.forEach(item => {
          this.height += item.elm.offsetHeight
        })
        const cover = this.$refs.zyPopUpCover
        const content = this.$refs.zyPopUpContent
        content.style.height = this.height + 42 + 'px'
        this.positionX = (cover.offsetHeight - content.offsetHeight) / 2
        this.positionY = (cover.offsetWidth - content.offsetWidth) / 2
        content.style.top = (cover.offsetHeight - content.offsetHeight) / 2 + 'px'
        content.style.left = (cover.offsetWidth - content.offsetWidth) / 2 + 'px'
      })
    },
    // 鼠标按下移动
    onMovedown (e) {
      const pdiv = this.$refs.zyPopUpCover
      const odiv = this.$refs.zyPopUpContent
      const disX = e.clientX - odiv.offsetLeft
      const disY = e.clientY - odiv.offsetTop
      const diffWidth = pdiv.offsetWidth - odiv.offsetWidth
      const diffHeight = pdiv.offsetHeight - odiv.offsetHeight
      if (diffWidth <= 0 || diffHeight <= 0) {
        document.onmousemove = null
        document.onmouseup = null
        return
      }
      document.onmousemove = (e) => {
        let left = e.clientX - disX
        let top = e.clientY - disY
        const minWidth = pdiv.offsetLeft
        const minHeight = pdiv.offsetTop
        const maxWidth = (pdiv.offsetLeft + pdiv.offsetWidth) - odiv.offsetWidth
        const maxHeight = (pdiv.offsetTop + pdiv.offsetHeight) - odiv.offsetHeight
        left = left < minWidth ? minWidth : left
        top = top < minHeight ? minHeight : top
        left = left > maxWidth ? maxWidth : left
        top = top > maxHeight ? maxHeight : top
        this.positionX = top
        this.positionY = left
        odiv.style.left = left + 'px'
        odiv.style.top = top + 'px'
      }
      document.onmouseup = (e) => {
        document.onmousemove = null
        document.onmouseup = null
      }
    },
    onMoveup () {
      document.onmousemove = null
      document.onmouseup = null
    },
    closeClick () {
      if (typeof this.beforeClose === 'function') {
        this.beforeClose(() => { this.$emit('input', false) })
      } else {
        this.$emit('input', false)
      }
    }
  },
  watch: {
    value (val) {
      if (val) {
        this.$nextTick(() => {
          const that = this
          erd.listenTo(this.$refs.zyPopUpCover, (element) => {
            that.$nextTick(() => {
              that.width = element.offsetWidth
              // that.height = element.offsetHeight
              that.location()
            })
          })
        })
      } else {
        erd.uninstall(this.$refs.zyPopUpCover)
      }
    }
  }
}
</script>
<style lang="scss">
@import "./zy-pop-up.scss";
</style>
