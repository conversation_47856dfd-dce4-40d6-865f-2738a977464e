<template>
  <div class="domtree-content">
    <div class="details-content"
      v-if="isShow===0">
        <div v-html="details.content"></div>
        <div class="form-button details-content-button">
          <el-button type="primary"
                    @click="editClick">编辑</el-button>
          <!-- <el-button @click="cancel">重置</el-button> -->
          <el-button type="danger"
                    @click="delClick">删除</el-button>
        </div>
      </div>
    <div class="details-form"  v-else>
      <el-form :model="form"
              :rules="rules"
              inline
              ref="form"
              label-position="top"
              class="newForm">
        <el-form-item label="所属结构"
                      prop="organizationId"
                      class="form-input">
          <zy-select width="296"
                    node-key="id"
                    :props="{children: 'children',label: 'name'}"
                    v-model="form.organizationId"
                    :data="organizationIdData"
                    placeholder="请选择所属结构"></zy-select>
        </el-form-item>
        <el-form-item label="内容"
                      prop="content"
                      class="form-ue">
          <UEditor v-model="form.content"></UEditor>
        </el-form-item>
        <div class="form-button">
          <el-button type="primary"
                    @click="submitForm('form')">提交</el-button>
          <el-button @click="cancel">重置</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
export default {
  name: 'organizationalStructureContent',
  data() {
    return {
      form: {
        content: '',
        organizationId: ''
      },
      rules: {
        content: [
          { required: true, message: '请输入组织架构内容', trigger: 'blur' }
        ],
        organizationId: [
          { required: true, message: '请选择所属结构', trigger: 'blur' }
        ]
      },
      organizationIdData: [],
      details: {},
      isShow: 1,
      contentId: ''
    }
  },
  created() {
    this.informationColumnTree()
  },
  props: ['id'],
  methods: {
    async informationColumnTree () {
      const res = await this.$api.appManagement.organizationTree({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword
      })
      var { data } = res
      this.organizationIdData = data
    },
    editClick() {
      this.isShow = 1
      this.form = this.details
    },
    /**
   * 提交
  */
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var url = '/organizationcontent/add'
          if (this.contentId) {
            url = '/organizationcontent/edit'
          }
          this.$api.appManagement.organizationcontentAdd(url, {
            id: this.contentId,
            content: this.form.content,
            organizationId: this.form.organizationId
          }).then(res => {
            var { errcode, errmsg } = res
            if (errcode === 200) {
              this.$message({
                message: errmsg,
                type: 'success'
              })
              if (this.contentId) {
                this.isShow = 0
              } else {
                this.isShow = 1
              }
              this.informationList(sessionStorage.listId)
            }
          })
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    /**
   * 取消按钮
  */
    cancel () {
      this.form.content = ''
      this.form.organizationId = ''
    },
    async informationList (id) {
      this.details = {}
      this.contentId = ''
      const res = await this.$api.appManagement.organizationcontentList({
        pageNo: this.page,
        pageSize: this.pageSize,
        keyword: this.keyword
      })
      var { data } = res
      data.forEach(item => {
        if (item.organizationId === id) {
          this.isShow = 0
          this.informationDetails(item.id)
          this.contentId = item.id
        } else {
          this.isShow = 1
          this.form.content = ''
          this.form.organizationId = ''
        }
      })
      sessionStorage.listId = id
    },
    async informationDetails (id) {
      const res = await this.$api.appManagement.organizationcontentInfo(id)
      var { data } = res
      this.details = data
      this.isShow = 0
    },
    delClick() {
      this.$confirm('此操作将删除当前选中的栏目, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const res = await this.$api.appManagement.organizationcontentDel(this.contentId)
        var { errcode, errmsg } = res
        if (errcode === 200) {
          this.informationList(sessionStorage.listId)
          this.contentId = ''
          this.isShow = 1
          this.$message({
            message: errmsg,
            type: 'success'
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
<style lang="scss">
.domtree-content{
  width: 70%;
  margin: 15px auto;
  .details-content-button{
    padding:  20px 0;
    text-align: center;
  }
}
</style>
