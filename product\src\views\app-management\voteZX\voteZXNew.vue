<template>
  <div class="voteZXNew">
    <el-steps :active="active"
              finish-status="success"
              simple>
      <el-step title="会议/活动基本信息"></el-step>
      <el-step title="时间、地点和人员"></el-step>
      <el-step title="各项设置"></el-step>
    </el-steps>
    <el-scrollbar class="voteZXNewScrollbar">
      <el-form :model="form"
               :rules="rules"
               inline
               ref="form"
               label-position="top"
               class="newForm">
        <template v-if="active==0">
          <el-form-item label="活动大类"
                        prop="meetType"
                        class="form-input">
            <el-select v-model="form.meetType"
                       filterable
                       clearable
                       placeholder="请选择活动大类">
              <el-option v-for="item in meetType"
                         :key="item.id"
                         :label="item.name"
                         :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="活动小类"
                        class="form-input">
            <el-select v-model="form.typeSmall"
                       filterable
                       clearable
                       placeholder="请选择活动小类">
              <el-option v-for="item in typeSmall"
                         :key="item.id"
                         :label="item.name"
                         :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="form-title"
                        prop="meetName">
            <span slot="label"
                  class="label-button">活动主题 <span style="color: red;">(最多50字)</span>
            </span>
            <el-input placeholder="请输入活动主题"
                      v-model="form.meetName"
                      clearable
                      prop="meetName">
            </el-input>
          </el-form-item>
          <el-form-item label="内容"
                        prop="content"
                        class="form-ue">
            <UEditor v-model="form.content"></UEditor>
          </el-form-item>
          <el-form-item label="组织部门"
                        class="form-title"
                        prop="organizer">
            <el-input placeholder="请输入组织部门"
                      v-model="form.organizer"
                      clearable>
            </el-input>
          </el-form-item>
          <div class="form-button">
            <el-button type="primary"
                       @click="oneClick('form')">填好了,下一步</el-button>
            <el-button @click="oneReset">重置以上内容</el-button>
          </div>
        </template>
        <template v-if="active==1">
          <el-form-item class="form-title"
                        label="活动地点"
                        prop="address">
            <el-input placeholder="请输入活动地点"
                      v-model="form.address"
                      clearable>
            </el-input>
          </el-form-item>
          <el-form-item label="报名时间"
                        class="form-title"
                        prop="signUpTime">
            <el-date-picker v-model="form.signUpTime"
                            type="datetimerange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="报名开始日期"
                            end-placeholder="报名结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="签到时间"
                        class="form-title"
                        prop="signTime">
            <el-date-picker v-model="form.signTime"
                            type="datetimerange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="签到开始日期"
                            end-placeholder="签到结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="活动时间"
                        class="form-title"
                        prop="activityTime">
            <el-date-picker v-model="form.activityTime"
                            type="datetimerange"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="活动开始日期"
                            end-placeholder="活动结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="form-title">
            <span slot="label">邀请参与活动人员 <span class="red">(若不选择人员,则表示该活动为公开活动,所有人都可以参与)</span></span>
            <div class="form-user-box"
                 @click="userClick('1')">
              <div v-if="!inviters.length"
                   class="form-user-box-text">请选择参与活动人员</div>
              <el-tag v-for="tag in inviters"
                      :key="tag.userId"
                      size="medium"
                      closable
                      :disable-transitions="false"
                      @close.stop="remove(tag,'1')">
                {{tag.name}}
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item class="form-title">
            <span slot="label">已报名人员 <span class="red">(此项为管理员后台处理数据所用)</span></span>
            <div class="form-user-box"
                 @click="userClick('2')">
              <div v-if="!signUps.length"
                   class="form-user-box-text">请选择报名人</div>
              <el-tag v-for="tag in signUps"
                      :key="tag.userId"
                      size="medium"
                      closable
                      :disable-transitions="false"
                      @close.stop="remove(tag,'2')">
                {{tag.name}}
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item class="form-title">
            <span slot="label">已签到人员 <span class="red">(此项为管理员后台处理数据所用)</span></span>
            <div class="form-user-box"
                 @click="userClick('3')">
              <div v-if="!signIns.length"
                   class="form-user-box-text">请选择签到人</div>
              <el-tag v-for="tag in signIns"
                      :key="tag.userId"
                      size="medium"
                      closable
                      :disable-transitions="false"
                      @close.stop="remove(tag,'3')">
                {{tag.name}}
              </el-tag>
            </div>
          </el-form-item>
          <div class="form-button">
            <el-button type="primary"
                       @click="twoClick('form')">填好了,下一步</el-button>
            <el-button @click="twoReturn">返回上一步</el-button>
            <el-button @click="twoReset">重置以上内容</el-button>
          </div>
        </template>
        <template v-if="active==2">
          <el-form-item label="是否在手机APP上显示该活动"
                        prop="isAppShow"
                        class="form-input">
            <el-radio-group v-model="form.isAppShow">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否让所有人可以看到该活动"
                        prop="isPublish"
                        class="form-input">
            <el-radio-group v-model="form.isPublish">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否让所有人可以报名参加该活动"
                        prop="isPublishbm"
                        class="form-input">
            <el-radio-group v-model="form.isPublishbm">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否需要用短信通知活动邀请人"
                        prop="isNotice"
                        class="form-input">
            <el-radio-group v-model="form.isNotice">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="是否同步生成活动通知"
                        prop="isRelease"
                        class="form-input">
            <el-radio-group v-model="form.isRelease">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <br>
          <el-form-item label="请您为本次活动选几个标签吧">
            <el-checkbox-group v-model="form.label">
              <el-checkbox v-for="item in activity_label"
                           :key="item.value"
                           :label="item.id">{{item.value}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div class="form-button">
            <el-button type="primary"
                       @click="submitForm('form')">填好了,提交</el-button>
            <el-button @click="threeReturn">返回上一步</el-button>
            <el-button @click="threeReset">重置以上内容</el-button>
          </div>
        </template>
      </el-form>
    </el-scrollbar>
    <zy-pop-up v-model="userShow"
               title="选择邀请人">
      <candidates-user point="point_15"
                       :data="userData"
                       @userCallback="userCallback"></candidates-user>
    </zy-pop-up>
  </div>
</template>
<script>
export default {
  name: 'voteZXNew',
  data () {
    return {
      id: this.$route.query.id,
      toId: this.$route.query.toId,
      active: 0,
      form: {
        meetName: '', // 标题
        meetType: '', // 活动父类型
        typeSmall: '', // 活动子类型
        meetingotherTypename: '', // 报送单位
        organizer: '', // 组织部门
        pubOrganizer: '', // 发布部门
        num: '', // 排序
        address: '', // 地点
        signUpTime: null, // 报名时间
        signTime: null, // 签到时间
        activityTime: null, // 活动时间
        isAppShow: 1, // 是否app显示
        isPublish: 1, // 是否公开
        isPublishbm: 1, // 是否公开报名
        isNotice: 0, // 是否短信通知邀请人
        isRelease: 0, // 是否发布通知
        label: [], // 标签
        content: '' // 内容
      },
      rules: {
        meetName: [
          { required: true, message: '请输入活动主题', trigger: 'blur' }
        ],
        meetType: [
          { required: true, message: '请选择活动大类', trigger: 'blur' }
        ],
        organizer: [
          { required: true, message: '请输入组织部门', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入活动内容', trigger: 'blur' }
        ]
      },
      meetType: [],
      typeSmall: [],
      activity_label: [],
      type: '',
      userShow: false,
      userData: [],
      inviters: [], // 邀请人
      signUps: [], // 报名人
      signIns: [] // 签到人
    }
  },
  inject: ['tabDel', 'refresh'],
  watch: {
    'form.meetType' (val) {
      if (val) {
        this.meetType.forEach(item => {
          if (item.id === val) {
            this.form.typeSmall = ''
            this.typeSmall = item.children
          }
        })
      } else {
        this.typeSmall = []
      }
    }
  },
  mounted () {
    this.treelist(3)
    // this.treelist(5)
    this.dictionaryPubkvs()
    if (this.id) {
      this.activityInfo()
    }
  },
  methods: {
    /**
     *详情
    */
    async activityInfo () {
      const res = await this.$api.activity.activityInfo(this.id)
      var { data } = res
      this.form.meetName = data.meetName // 标题
      this.form.organizer = data.organizer // 组织部门
      this.form.pubOrganizer = data.pubOrganizer // 发布部门
      this.form.num = data.num // 排序
      this.form.address = data.address // 地点
      this.form.meetingotherTypename = data.meetingotherTypename // 报送单位
      this.form.meetType = data.meetType // 活动父类型
      setTimeout(() => {
        this.form.typeSmall = data.typeSmall // 活动子类型
      }, 520)
      this.form.isAppShow = data.isAppShow // 是否app显示
      this.form.isPublish = data.isPublish // 是否公开
      this.form.isPublishbm = data.isPublishbm // 是否公开报名
      this.form.isNotice = data.isNotice // 是否短信通知邀请人
      this.form.isRelease = data.isRelease // 是否发布通知
      this.form.label = data.label ? data.label.split(',') : []
      this.form.content = data.content
      this.inviters = data.inviterList // 邀请人
      this.signUps = data.signUpList // 报名人
      this.signIns = data.signInList // 签到人
      this.form.signUpTime = [data.signBeginTime, data.signEndTime] // 报名时间
      this.form.signTime = [data.meetSignBeginTime, data.meetSignEndTime] // 签到时间
      this.form.activityTime = [data.meetStartTime, data.meetEndTime] // 活动时间
    },
    async treelist (treeType) {
      // (5,委员通讯录树),
      // (3,”活动类型”),
      const res = await this.$api.activity.treelist({
        treeType: treeType
      })
      var { data } = res
      if (treeType === 3) {
        this.meetType = data
      } if (treeType === 5) {
        // this.meetingotherTypenameList = data
      }
    },
    async dictionaryPubkvs () {
      const res = await this.$api.systemSettings.dictionaryPubkvs({
        types: 'activity_label'
      })
      var { data } = res
      this.activity_label = data.activity_label
    },
    userClick (type) {
      this.type = type
      if (type === '1') {
        this.userData = this.inviters
      } else if (type === '2') {
        this.userData = this.signUps
      } else if (type === '3') {
        this.userData = this.signIns
      }
      this.userShow = !this.userShow
    },
    userCallback (data, type) {
      if (type) {
        if (this.type === '1') {
          this.inviters = data
        } else if (this.type === '2') {
          this.signUps = data
        } else if (this.type === '3') {
          this.signIns = data
        }
      }
      this.userShow = !this.userShow
    },
    // 移除tag
    remove (data, type) {
      var userData = []
      if (type === '1') {
        userData = this.inviters
        this.inviters = userData.filter(v => v.userId !== data.userId)
      } else if (type === '2') {
        userData = this.signUps
        this.signUps = userData.filter(v => v.userId !== data.userId)
      } else if (type === '3') {
        userData = this.signIns
        this.signIns = userData.filter(v => v.userId !== data.userId)
      }
    },
    submitForm (formName, type) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          var inviters = []
          this.inviters.forEach(item => {
            inviters.push(item.userId)
          })
          var signUps = []
          this.signUps.forEach(item => {
            signUps.push(item.userId)
          })
          var signIns = []
          this.signIns.forEach(item => {
            signIns.push(item.userId)
          })
          var data = this.form
          data.signBeginTime = this.form.signUpTime[0] // 报名开始时间
          data.signEndTime = this.form.signUpTime[1] // 报名截止时间
          data.meetSignBeginTime = this.form.signTime[0] // 开始签到时间
          data.meetSignEndTime = this.form.signTime[1] // 签到截至时间
          data.meetStartTime = this.form.activityTime[0] // 活动开始时间
          data.meetEndTime = this.form.activityTime[1] // 活动结束时间
          data.label = this.form.label.join(',') // 标签
          data.inviters = inviters.join(',') // 邀请人ids 以逗号隔开
          data.signUps = signUps.join(',') // 报名人ids 以逗号隔开
          data.signIns = signIns.join(',') // 签到人ids 以逗号隔开
          if (this.id) {
            data.id = this.id
          }
          this.activitySaveActivity(data)
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    async activitySaveActivity (data) {
      const res = await this.$api.activity.activitySaveActivity(data)
      var { errcode, errmsg } = res
      if (errcode === 200) {
        this.$message({
          message: errmsg,
          type: 'success'
        })
        if (this.id) {
          this.tabDel(this.id, this.toId)
        } else {
          this.refresh()
        }
      }
    },
    oneClick (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.rules = {
            address: [
              { required: true, message: '请输入活动地点', trigger: 'blur' }
            ],
            activityTime: [
              { required: true, message: '请选择活动时间', trigger: 'blur' }
            ],
            signUpTime: [
              { required: true, message: '请选择报名时间', trigger: 'blur' }
            ],
            signTime: [
              { required: true, message: '请选择签到时间', trigger: 'blur' }
            ]
          }
          this.active = 1
        } else {
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    oneReset () {
      this.form.meetName = '' // 标题
      this.form.meetType = '' // 活动父类型
      this.form.typeSmall = '' // 活动子类型
      this.form.content = '' // 内容
      this.form.organizer = '' // 组织部门
    },
    twoClick (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.active = 2
        } else {
          console.log(this.form)
          this.$message({
            message: '请输入必填项',
            type: 'warning'
          })
          return false
        }
      })
    },
    twoReturn () {
      this.rules = {
        meetName: [
          { required: true, message: '请输入活动主题', trigger: 'blur' }
        ],
        meetType: [
          { required: true, message: '请选择活动大类', trigger: 'blur' }
        ],
        organizer: [
          { required: true, message: '请输入组织部门', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入活动内容', trigger: 'blur' }
        ]
      }
      this.active = 0
    },
    twoReset () {
      this.form.address = '' // 地点
      this.form.signUpTime = [] // 报名时间
      this.form.signTime = [] // 签到时间
      this.form.activityTime = [] // 活动时间
      this.inviters = [] // 邀请人
      this.signUps = [] // 报名人
      this.signIns = [] // 签到人
    },
    threeReturn () {
      this.rules = {
        address: [
          { required: true, message: '请输入地点', trigger: 'blur' }
        ],
        activityTime: [
          { required: true, message: '请选择时间', trigger: 'blur' }
        ],
        signUpTime: [
          { required: true, message: '请选择时间', trigger: 'blur' }
        ],
        signTime: [
          { required: true, message: '请选择时间', trigger: 'blur' }
        ]
      }
      this.active = 1
    },
    threeReset () {
      this.form.isAppShow = 1 // 是否app显示
      this.form.isPublish = 1 // 是否公开
      this.form.isPublishbm = 1 // 是否公开报名
      this.form.isNotice = 0 // 是否短信通知邀请人
      this.form.isRelease = 0 // 是否发布通知
      this.form.label = [] // 标签
    },
    /**
     * 取消按钮
    */
    cancel () {
      this.$emit('callback')
    }
  }
}
</script>
<style lang="scss">
.voteZXNew {
  width: 100%;
  height: 100%;
  padding: 24px;
  .el-steps {
    width: 974px;
  }
  .voteZXNewScrollbar {
    height: calc(100% - 46px);
    width: 100%;
    .el-scrollbar__wrap {
      overflow-x: hidden !important;
    }
    .newForm {
      width: 988px;
      .form-button {
        justify-content: flex-start;
        .el-button {
          padding: 0 22px;
        }
      }
      .red {
        color: red;
      }
    }
  }
  .form-user-box {
    width: 100%;
    min-height: 40px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    padding: 0 16px;
    padding-right: 40px;
    padding-top: 6px;

    .form-user-box-text {
      color: #999;
      font-size: 14px;
      padding-bottom: 6px;
      line-height: 28px;
    }

    .el-tag {
      margin-bottom: 6px;
      margin-right: 12px;
    }

    &:hover {
      border-color: $zy-color;
    }

    &:focus {
      border-color: $zy-color;
    }
  }
}
</style>
