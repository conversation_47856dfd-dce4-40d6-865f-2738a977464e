<template>
  <div class="activity-add">
    <step :active="active"></step>
    <base-form v-show="active === 1"
               ref="baseForm"></base-form>
    <join-user v-show="active === 2"
               ref="joinUser"></join-user>
    <file-list v-show="active === 3"
               ref="material"></file-list>
    <footerBtn :active.sync="active"
               @submit="handleSubmit"></footerBtn>
  </div>
</template>

<script>
import _ from 'lodash'
import step from './widget/step'
import baseForm from './widget/base-form'
import joinUser from './widget/join-user'
import fileList from './widget/file-list'
import footerBtn from './widget/footer-btn'
export default {
  components: { step, baseForm, joinUser, fileList, footerBtn },
  data () {
    return {
      active: 1
    }
  },
  mounted () {
    const id = this.$route.query.id
    if (id) {
      this.getInfo(id)
    } else {
      this.$refs.baseForm.form.organizer = JSON.parse(sessionStorage.getItem('user' + this.$logo())).officeId
    }
  },
  inject: ['tabDel'],
  methods: {
    // 获取详情
    getInfo (id) {
      this.$api.activityqd.info(id).then(res => {
        const {
          meetName, meetType,
          organizer, address,
          signEndTime, meetSignBeginTime,
          meetSignEndTime, meetStartTime,
          meetEndTime, isAppShow,
          isRelease, isNotice, content,
          inviterList, materiainfos
        } = res.data
        this.$refs.baseForm.form = {
          meetName,
          meetType,
          organizer,
          address,
          signEndTime,
          time1: [meetSignBeginTime, meetSignEndTime],
          time2: [meetStartTime, meetEndTime],
          isAppShow,
          isNotice,
          isRelease,
          content
        }
        this.$refs.joinUser.userData = inviterList
        this.$refs.material.list = materiainfos
      })
    },
    handleSubmit () {
      const isForm = this.$refs.baseForm.validForm()
      if (isForm) {
        const data = _.cloneDeep(this.$refs.baseForm.form)
        data.meetSignBeginTime = data.time1[0]
        data.meetSignEndTime = data.time1[1]
        delete data.time1
        data.meetStartTime = data.time2[0]
        data.meetEndTime = data.time2[1]
        delete data.time2
        const userArr = this.$refs.joinUser.userData
        if (userArr.length > 0) {
          data.inviters = userArr.map(v => v.userId).join(',')
        }
        const files = this.$refs.material.list
        if (files.length > 0) {
          data.materiainfos = files.map(v => {
            return {
              fileId: v.fileId,
              isAppShow: v.isAppShow,
              date: v.date,
              sort: v.sort
            }
          })
        }
        const id = this.$route.query.id
        if (id) {
          data.id = id
        }
        console.log(data)
        this.$api.activityqd.add(data).then(res => {
          this.handleClose()
        })
      } else {
        this.$message.warning('有必填项未输入或者未选择')
        return false
      }
    },
    // 关闭页面
    handleClose () {
      const { id, toId, mid } = this.$route.query
      // 没有从列表页面进来的逻辑
      if (!mid) {
        this.$refs.baseForm.reset()
        this.$refs.joinUser.userData = []
        this.$refs.material.list = []
        this.$refs.material.reset()
      }
      if (id) {
        this.$message.success('编辑活动成功')
        this.tabDel(mid, toId)
      } else {
        this.$message.success('新增活动成功')
        this.$refs.baseForm.reset()
        this.$refs.joinUser.userData = []
        this.$refs.material.list = []
        this.$refs.material.reset()
      }
    }
  }
}
</script>

<style lang="scss">
.activity-add {
  width: 988px;
  padding-top: 24px;
}
</style>
