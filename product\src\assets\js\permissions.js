import router from '../../router'
function menuPermission (data, to) {
  var arr = []
  data.forEach(item => {
    if (item.to == to) { // eslint-disable-line
      arr = item.permissions
      return
    }
    if (item.children.length !== 0) {
      arr = [...arr, ...menuPermission(item.children, to)]
    }
  })
  return arr
}
const hasPermission = (role, rawName = '') => {
  var show = false
  var menuChild = JSON.parse(sessionStorage.getItem('menuChild'))
  var permissions = ''
  if (rawName) {
    permissions = menuPermission(menuChild, rawName)
  } else {
    var data = router.history.current.query
    if (JSON.stringify(data) === '{}') {
      permissions = menuPermission(menuChild, router.history.current.path)
    } else {
      var text = '?'
      for (const key in data) {
        if (text === '?') {
          text += `${key}=${data[key]}`
        } else {
          text += `&${key}=${data[key]}`
        }
      }
      var path = router.history.current.path + text
      permissions = menuPermission(menuChild, path)
    }
  }
  if (role instanceof Array) {
    var arr = []
    role.forEach(roleItem => {
      permissions.forEach(item => {
        if (item == roleItem) { // eslint-disable-line
          arr.push({ show: true })
        }
      })
    })
    if (arr.length !== 0) {
      show = true
    }
  } else {
    permissions.forEach(item => {
      if (item == role) { // eslint-disable-line
        show = true
      }
    })
  }
  return show
}

export default {
  hasPermission
}
